{"version": 3, "sources": ["../../../src/lib/typescript/writeConfigurationDefaults.ts"], "names": ["promises", "fs", "chalk", "CommentJson", "semver", "os", "getTypeScriptConfiguration", "Log", "getDesiredCompilerOptions", "ts", "userTsConfig", "o", "lib", "suggested", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "lt", "version", "forceConsistentCasingInFileNames", "undefined", "noEmit", "gte", "incremental", "esModuleInterop", "value", "reason", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "ES2020", "CommonJS", "AMD", "NodeNext", "Node16", "moduleResolution", "ModuleResolutionKind", "<PERSON><PERSON><PERSON>", "Node10", "NodeJs", "Node12", "filter", "val", "resolveJsonModule", "compilerOptions", "verbatimModuleSyntax", "isolatedModules", "jsx", "JsxEmit", "Preserve", "getRequiredConfiguration", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "writeConfigurationDefaults", "tsConfigPath", "isFirstTimeSetup", "isAppDirEnabled", "distDir", "hasPagesDir", "writeFile", "EOL", "options", "tsOptions", "raw", "rawConfig", "userTsConfigContent", "readFile", "encoding", "parse", "suggestedActions", "requiredActions", "check", "push", "cyan", "bold", "includes", "_", "nextAppTypes", "include", "plugins", "Array", "isArray", "hasNextPlugin", "some", "name", "length", "info", "strict<PERSON>ull<PERSON>hecks", "exclude", "stringify", "for<PERSON>ach", "action", "white"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,WAAW,2BAA0B;AAC5C,YAAYC,iBAAiB,kCAAiC;AAC9D,OAAOC,YAAY,4BAA2B;AAC9C,OAAOC,QAAQ,KAAI;AAEnB,SAASC,0BAA0B,QAAQ,+BAA8B;AACzE,YAAYC,SAAS,yBAAwB;AAa7C,SAASC,0BACPC,EAA+B,EAC/BC,YAAoD;QA4D9CA;IA1DN,MAAMC,IAAiC;QACrC,qEAAqE;QACrE,gBAAgB;QAChBC,KAAK;YAAEC,WAAW;gBAAC;gBAAO;gBAAgB;aAAS;QAAC;QACpDC,SAAS;YAAED,WAAW;QAAK;QAC3BE,cAAc;YAAEF,WAAW;QAAK;QAChCG,QAAQ;YAAEH,WAAW;QAAM;QAC3B,GAAIT,OAAOa,EAAE,CAACR,GAAGS,OAAO,EAAE,WACtB;YAAEC,kCAAkC;gBAAEN,WAAW;YAAK;QAAE,IACxDO,SAAS;QACbC,QAAQ;YAAER,WAAW;QAAK;QAC1B,GAAIT,OAAOkB,GAAG,CAACb,GAAGS,OAAO,EAAE,WACvB;YAAEK,aAAa;gBAAEV,WAAW;YAAK;QAAE,IACnCO,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EI,iBAAiB;YACfC,OAAO;YACPC,QAAQ;QACV;QACAC,QAAQ;YACNC,aAAanB,GAAGoB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,cAAc;gBACZtB,GAAGoB,UAAU,CAACG,MAAM;gBACpBvB,GAAGoB,UAAU,CAACC,MAAM;gBACpBrB,GAAGoB,UAAU,CAACI,QAAQ;gBACtBxB,GAAGoB,UAAU,CAACK,GAAG;gBACjBzB,GAAGoB,UAAU,CAACM,QAAQ;gBACtB1B,GAAGoB,UAAU,CAACO,MAAM;aACrB;YACDX,OAAO;YACPC,QAAQ;QACV;QACAW,kBAAkB;YAChB,sDAAsD;YACtDT,aACEnB,GAAG6B,oBAAoB,CAACC,OAAO,IAC/B9B,GAAG6B,oBAAoB,CAACH,QAAQ,IAChC,AAAC1B,GAAG6B,oBAAoB,CAASE,MAAM,IACvC/B,GAAG6B,oBAAoB,CAACG,MAAM;YAChC,4BAA4B;YAC5BV,cAAc;gBACXtB,GAAG6B,oBAAoB,CAASE,MAAM,IACrC/B,GAAG6B,oBAAoB,CAACG,MAAM;gBAChC,qDAAqD;gBACrD,kDAAkD;gBACjDhC,GAAG6B,oBAAoB,CAASI,MAAM;gBACvCjC,GAAG6B,oBAAoB,CAACF,MAAM;gBAC9B3B,GAAG6B,oBAAoB,CAACH,QAAQ;gBAChC1B,GAAG6B,oBAAoB,CAACC,OAAO;aAChC,CAACI,MAAM,CAAC,CAACC,MAAQ,OAAOA,QAAQ;YACjCnB,OAAO;YACPC,QAAQ;QACV;QACAmB,mBAAmB;YAAEpB,OAAO;YAAMC,QAAQ;QAA8B;QACxE,GAAIhB,CAAAA,iCAAAA,gCAAAA,aAAcoC,eAAe,qBAA7BpC,8BAA+BqC,oBAAoB,MAAK,OACxD3B,YACA;YACE4B,iBAAiB;gBACfvB,OAAO;gBACPC,QAAQ;YACV;QACF,CAAC;QACLuB,KAAK;YACHrB,aAAanB,GAAGyC,OAAO,CAACC,QAAQ;YAChC1B,OAAO;YACPC,QAAQ;QACV;IACF;IAEA,OAAOf;AACT;AAEA,OAAO,SAASyC,yBACd3C,EAA+B;IAE/B,MAAM4C,MAAqD,CAAC;IAE5D,MAAMC,yBAAyB9C,0BAA0BC;IACzD,KAAK,MAAM8C,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMI,KAAKJ,sBAAsB,CAACC,UAAU;QAC5C,IAAI,CAAE,CAAA,WAAWG,EAAC,GAAI;YACpB;QACF;QACAL,GAAG,CAACE,UAAU,GAAGG,GAAG9B,WAAW,IAAI8B,GAAGjC,KAAK;IAC7C;IAEA,OAAO4B;AACT;AAEA,OAAO,eAAeM,2BACpBlD,EAA+B,EAC/BmD,YAAoB,EACpBC,gBAAyB,EACzBC,eAAwB,EACxBC,OAAe,EACfC,WAAoB;IAEpB,IAAIH,kBAAkB;QACpB,MAAM5D,GAAGgE,SAAS,CAACL,cAAc,OAAOvD,GAAG6D,GAAG;IAChD;IAEA,MAAM,EAAEC,SAASC,SAAS,EAAEC,KAAKC,SAAS,EAAE,GAC1C,MAAMhE,2BAA2BG,IAAImD,cAAc;IAErD,MAAMW,sBAAsB,MAAMtE,GAAGuE,QAAQ,CAACZ,cAAc;QAC1Da,UAAU;IACZ;IACA,MAAM/D,eAAeP,YAAYuE,KAAK,CAACH;IACvC,IAAI7D,aAAaoC,eAAe,IAAI,QAAQ,CAAE,CAAA,aAAawB,SAAQ,GAAI;QACrE5D,aAAaoC,eAAe,GAAG,CAAC;QAChCe,mBAAmB;IACrB;IAEA,MAAMP,yBAAyB9C,0BAA0BC,IAAIC;IAE7D,MAAMiE,mBAA6B,EAAE;IACrC,MAAMC,kBAA4B,EAAE;IACpC,KAAK,MAAMrB,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMuB,QAAQvB,sBAAsB,CAACC,UAAU;QAC/C,IAAI,eAAesB,OAAO;YACxB,IAAI,CAAEtB,CAAAA,aAAaa,SAAQ,GAAI;gBAC7B,IAAI,CAAC1D,aAAaoC,eAAe,EAAE;oBACjCpC,aAAaoC,eAAe,GAAG,CAAC;gBAClC;gBACApC,aAAaoC,eAAe,CAACS,UAAU,GAAGsB,MAAMhE,SAAS;gBACzD8D,iBAAiBG,IAAI,CACnB5E,MAAM6E,IAAI,CAACxB,aAAa,iBAAiBrD,MAAM8E,IAAI,CAACH,MAAMhE,SAAS;YAEvE;QACF,OAAO,IAAI,WAAWgE,OAAO;gBAIrBA;YAHN,MAAMnB,KAAKU,SAAS,CAACb,UAAU;YAC/B,IACE,CAAE,CAAA,kBAAkBsB,SAChBA,sBAAAA,MAAM9C,YAAY,qBAAlB8C,oBAAoBI,QAAQ,CAACvB,MAC7B,iBAAiBmB,QACjBA,MAAMjD,WAAW,KAAK8B,KACtBmB,MAAMpD,KAAK,KAAKiC,EAAC,GACrB;gBACA,IAAI,CAAChD,aAAaoC,eAAe,EAAE;oBACjCpC,aAAaoC,eAAe,GAAG,CAAC;gBAClC;gBACApC,aAAaoC,eAAe,CAACS,UAAU,GAAGsB,MAAMpD,KAAK;gBACrDmD,gBAAgBE,IAAI,CAClB5E,MAAM6E,IAAI,CAACxB,aACT,iBACArD,MAAM8E,IAAI,CAACH,MAAMpD,KAAK,IACtB,CAAC,EAAE,EAAEoD,MAAMnD,MAAM,CAAC,CAAC,CAAC;YAE1B;QACF,OAAO;YACL,6DAA6D;YAC7D,MAAMwD,IAAWL;QACnB;IACF;IAEA,MAAMM,eAAe,CAAC,EAAEpB,QAAQ,cAAc,CAAC;IAE/C,IAAI,CAAE,CAAA,aAAaO,SAAQ,GAAI;QAC7B5D,aAAa0E,OAAO,GAAGtB,kBACnB;YAAC;YAAiBqB;YAAc;YAAW;SAAW,GACtD;YAAC;YAAiB;YAAW;SAAW;QAC5CR,iBAAiBG,IAAI,CACnB5E,MAAM6E,IAAI,CAAC,aACT,iBACA7E,MAAM8E,IAAI,CACRlB,kBACI,CAAC,mBAAmB,EAAEqB,aAAa,yBAAyB,CAAC,GAC7D,CAAC,wCAAwC,CAAC;IAGtD,OAAO,IAAIrB,mBAAmB,CAACQ,UAAUc,OAAO,CAACH,QAAQ,CAACE,eAAe;QACvEzE,aAAa0E,OAAO,CAACN,IAAI,CAACK;QAC1BR,iBAAiBG,IAAI,CACnB5E,MAAM6E,IAAI,CAAC,aACT,yBACA7E,MAAM8E,IAAI,CAAC,CAAC,CAAC,EAAEG,aAAa,CAAC,CAAC;IAEpC;IAEA,wCAAwC;IACxC,IAAIrB,iBAAiB;QACnB,qEAAqE;QACrE,MAAMuB,UAAU;eACVC,MAAMC,OAAO,CAACnB,UAAUiB,OAAO,IAAIjB,UAAUiB,OAAO,GAAG,EAAE;eACzD3E,aAAaoC,eAAe,IAChCwC,MAAMC,OAAO,CAAC7E,aAAaoC,eAAe,CAACuC,OAAO,IAC9C3E,aAAaoC,eAAe,CAACuC,OAAO,GACpC,EAAE;SACP;QACD,MAAMG,gBAAgBH,QAAQI,IAAI,CAChC,CAAC,EAAEC,IAAI,EAAoB,GAAKA,SAAS;QAG3C,8EAA8E;QAC9E,0DAA0D;QAC1D,4EAA4E;QAC5E,IACE,CAAChF,aAAaoC,eAAe,IAC5BuC,QAAQM,MAAM,IACb,CAACH,iBACD,aAAalB,aACZ,CAAA,CAACA,UAAUxB,eAAe,IAAI,CAACwB,UAAUxB,eAAe,CAACuC,OAAO,AAAD,GAClE;YACA9E,IAAIqF,IAAI,CACN,CAAC,OAAO,EAAE1F,MAAM8E,IAAI,CAClB,iBACA,yLAAyL,EAAE9E,MAAM6E,IAAI,CACrM,mCACA,+JAA+J,CAAC;QAEtK,OAAO,IAAI,CAACS,eAAe;YACzB,IAAI,CAAE,CAAA,aAAa9E,aAAaoC,eAAe,AAAD,GAAI;gBAChDpC,aAAaoC,eAAe,CAACuC,OAAO,GAAG,EAAE;YAC3C;YACA3E,aAAaoC,eAAe,CAACuC,OAAO,CAACP,IAAI,CAAC;gBAAEY,MAAM;YAAO;YACzDf,iBAAiBG,IAAI,CACnB5E,MAAM6E,IAAI,CAAC,aACT,yBACA7E,MAAM8E,IAAI,CAAC,CAAC,gBAAgB,CAAC;QAEnC;QAEA,yEAAyE;QACzE,yCAAyC;QACzC,IACEhB,eACAF,mBACApD,aAAaoC,eAAe,IAC5B,CAACpC,aAAaoC,eAAe,CAAC9B,MAAM,IACpC,CAAE,CAAA,sBAAsBN,aAAaoC,eAAe,AAAD,GACnD;YACApC,aAAaoC,eAAe,CAAC+C,gBAAgB,GAAG;YAChDlB,iBAAiBG,IAAI,CACnB5E,MAAM6E,IAAI,CAAC,sBAAsB,iBAAiB7E,MAAM8E,IAAI,CAAC,CAAC,IAAI,CAAC;QAEvE;IACF;IAEA,IAAI,CAAE,CAAA,aAAaV,SAAQ,GAAI;QAC7B5D,aAAaoF,OAAO,GAAG;YAAC;SAAe;QACvCnB,iBAAiBG,IAAI,CACnB5E,MAAM6E,IAAI,CAAC,aAAa,iBAAiB7E,MAAM8E,IAAI,CAAC,CAAC,gBAAgB,CAAC;IAE1E;IAEA,IAAIL,iBAAiBgB,MAAM,GAAG,KAAKf,gBAAgBe,MAAM,GAAG,GAAG;QAC7D;IACF;IAEA,MAAM1F,GAAGgE,SAAS,CAChBL,cACAzD,YAAY4F,SAAS,CAACrF,cAAc,MAAM,KAAKL,GAAG6D,GAAG;IAGvD3D,IAAIqF,IAAI,CAAC;IACT,IAAI/B,kBAAkB;QACpBtD,IAAIqF,IAAI,CACN,CAAC,qDAAqD,EAAE1F,MAAM6E,IAAI,CAChE,iBACA,cAAc,CAAC;QAEnB;IACF;IAEAxE,IAAIqF,IAAI,CACN,CAAC,6DAA6D,EAAE1F,MAAM6E,IAAI,CACxE,iBACA,qCAAqC,EAAE7E,MAAM6E,IAAI,CAAC,SAAS,YAAY,CAAC;IAE5E,IAAIJ,iBAAiBgB,MAAM,EAAE;QAC3BpF,IAAIqF,IAAI,CACN,CAAC,kDAAkD,EAAE1F,MAAM6E,IAAI,CAC7D,iBACA,eAAe,EAAE7E,MAAM6E,IAAI,CAC3B,kBACA,+BAA+B,CAAC;QAGpCJ,iBAAiBqB,OAAO,CAAC,CAACC,SAAW1F,IAAIqF,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE7D1F,IAAIqF,IAAI,CAAC;IACX;IAEA,IAAIhB,gBAAgBe,MAAM,EAAE;QAC1BpF,IAAIqF,IAAI,CACN,CAAC,cAAc,EAAE1F,MAAMgG,KAAK,CAC1B,qBACA,mBAAmB,EAAEhG,MAAM6E,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAGzDH,gBAAgBoB,OAAO,CAAC,CAACC,SAAW1F,IAAIqF,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE5D1F,IAAIqF,IAAI,CAAC;IACX;AACF"}