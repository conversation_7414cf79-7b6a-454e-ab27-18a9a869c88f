{"version": 3, "sources": ["../../../src/server/app-render/types.ts"], "names": ["flightRouterStateSchema", "dynamicParamTypesSchema", "zod", "enum", "segmentSchema", "union", "string", "tuple", "lazy", "parallelRoutesSchema", "record", "urlSchema", "nullable", "optional", "refreshSchema", "literal", "isRootLayoutSchema", "boolean"], "mappings": ";;;;+BA2BaA;;;eAAAA;;;4DArBG;;;;;;AAIhB,MAAMC,0BAA0BC,YAAG,CAACC,IAAI,CAAC;IAAC;IAAK;IAAM;CAAI;AAQzD,MAAMC,gBAAgBF,YAAG,CAACG,KAAK,CAAC;IAC9BH,YAAG,CAACI,MAAM;IACVJ,YAAG,CAACK,KAAK,CAAC;QAACL,YAAG,CAACI,MAAM;QAAIJ,YAAG,CAACI,MAAM;QAAIL;KAAwB;CAChE;AAMM,MAAMD,0BAA0DE,YAAG,CAACM,IAAI,CAC7E;IACE,MAAMC,uBAAuBP,YAAG,CAACQ,MAAM,CAACV;IACxC,MAAMW,YAAYT,YAAG,CAACI,MAAM,GAAGM,QAAQ,GAAGC,QAAQ;IAClD,MAAMC,gBAAgBZ,YAAG,CAACa,OAAO,CAAC,WAAWH,QAAQ,GAAGC,QAAQ;IAChE,MAAMG,qBAAqBd,YAAG,CAACe,OAAO,GAAGJ,QAAQ;IAEjD,6EAA6E;IAC7E,gDAAgD;IAChD,OAAOX,YAAG,CAACG,KAAK,CAAC;QACfH,YAAG,CAACK,KAAK,CAAC;YACRH;YACAK;YACAE;YACAG;YACAE;SACD;QACDd,YAAG,CAACK,KAAK,CAAC;YACRH;YACAK;YACAE;YACAG;SACD;QACDZ,YAAG,CAACK,KAAK,CAAC;YAACH;YAAeK;YAAsBE;SAAU;QAC1DT,YAAG,CAACK,KAAK,CAAC;YAACH;YAAeK;SAAqB;KAChD;AACH"}