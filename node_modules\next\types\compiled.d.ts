// this file is used to stub compiled imports when skipLibCheck: false is used
// it is not meant to be used with local type checking and is ignored in our
// local tsconfig.json

declare module 'next/dist/compiled/webpack/webpack' {
  export function init(): void
  export let BasicEvaluatedExpression: any
  export let GraphHelpers: any
  export let sources: any
  export let StringXor: any
  export const webpack: any
  export const Compiler: any
  export const Compilation: any
  export const Module: any
  export const Stats: any
  export const Template: any
  export const RuntimeModule: any
  export const RuntimeGlobals: any
  export const NormalModule: any
  export const ResolvePluginInstance: any
}
