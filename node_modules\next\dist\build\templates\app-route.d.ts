import '../../server/node-polyfill-headers';
declare const routeModule: import("../../server/future/route-modules/app-route/module").AppRouteRouteModule;
declare const requestAsyncStorage: import("../../client/components/request-async-storage.external").RequestAsyncStorage, staticGenerationAsyncStorage: import("../../client/components/static-generation-async-storage.external").StaticGenerationAsyncStorage, serverHooks: typeof import("../../client/components/hooks-server-context"), headerHooks: typeof import("../../client/components/headers"), staticGenerationBailout: import("../../client/components/static-generation-bailout").StaticGenerationBailout;
declare const originalPathname = "VAR_ORIGINAL_PATHNAME";
export { routeModule, requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout, originalPathname, };
