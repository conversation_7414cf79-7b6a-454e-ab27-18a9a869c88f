(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[799],{2401:function(n,e,o){var t={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function r(n){if(!o.o(t,n))return Promise.resolve().then(function(){var e=Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e});var e=t[n],r=e[0];return o.e(e[1]).then(function(){return o.t(r,19)})}r.keys=function(){return Object.keys(t)},r.id=2401,n.exports=r},1539:function(n,e,o){var t={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function r(n){if(!o.o(t,n))return Promise.resolve().then(function(){var e=Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e});var e=t[n],r=e[0];return o.e(e[1]).then(function(){return o.t(r,19)})}r.keys=function(){return Object.keys(t)},r.id=1539,n.exports=r},5531:function(n,e,o){"use strict";o.d(e,{Z:function(){return i}});var t=o(2265);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...n)=>n.filter((n,e,o)=>!!n&&o.indexOf(n)===e).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,t.forwardRef)(({color:n="currentColor",size:e=24,strokeWidth:o=2,absoluteStrokeWidth:r,className:c="",children:i,iconNode:l,...u},d)=>(0,t.createElement)("svg",{ref:d,...a,width:e,height:e,stroke:n,strokeWidth:r?24*Number(o)/Number(e):o,className:s("lucide",c),...u},[...l.map(([n,e])=>(0,t.createElement)(n,e)),...Array.isArray(i)?i:[i]])),i=(n,e)=>{let o=(0,t.forwardRef)(({className:o,...a},i)=>(0,t.createElement)(c,{ref:i,iconNode:e,className:s(`lucide-${r(n)}`,o),...a}));return o.displayName=`${n}`,o}},3523:function(n,e,o){"use strict";o.d(e,{Z:function(){return r}});var t=o(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},8027:function(n,e,o){Promise.resolve().then(o.bind(o,6954))},6954:function(n,e,o){"use strict";o.r(e),o.d(e,{default:function(){return l}});var t=o(7437),r=o(2265),s=o(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var c=o(3523),i=o(4346);function l(){let[n,e]=(0,r.useState)(null),{t:o}=(0,i.$G)("faq"),s=o("faqs",{returnObjects:!0}),l=Array.isArray(s)?s:[];return(0,t.jsx)("section",{className:"py-20 bg-white",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-16",children:[(0,t.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:o("title")}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:o("subtitle")})]}),(0,t.jsx)("div",{className:"space-y-4",children:l.map((o,r)=>(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsxs)("button",{className:"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors",onClick:()=>e(n===r?null:r),children:[(0,t.jsx)("span",{className:"font-semibold text-gray-900",children:o.question}),n===r?(0,t.jsx)(a,{className:"h-5 w-5 text-gray-500"}):(0,t.jsx)(c.Z,{className:"h-5 w-5 text-gray-500"})]}),n===r&&(0,t.jsx)("div",{className:"px-6 pb-4",children:(0,t.jsx)("p",{className:"text-gray-600 leading-relaxed",children:o.answer})})]},r))}),(0,t.jsxs)("div",{className:"mt-16 bg-blue-50 rounded-2xl p-8 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:o("support.title")}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:o("support.description")}),(0,t.jsx)("button",{className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:o("support.button")})]})]})})}},4346:function(n,e,o){"use strict";o.d(e,{$G:function(){return r},bU:function(){return s}});var t=o(3275);function r(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:e,locale:o}=(0,t.QT)();return{t:(o,t)=>e(o,n,t),locale:o,ready:!0}}function s(){let{locale:n,changeLanguage:e}=(0,t.QT)();return{locale:n,locales:["en","ja"],defaultLocale:"en",changeLanguage:e,isRTL:!1}}},3275:function(n,e,o){"use strict";o.d(e,{QT:function(){return i},XJ:function(){return l},bd:function(){return c}});var t=o(7437),r=o(2265),s=o(4033);let a=(0,r.createContext)(void 0);function c(n){let{children:e,locale:o,translations:c}=n,[i,u]=(0,r.useState)(o),[d,j]=(0,r.useState)(c);(0,s.useRouter)(),(0,s.usePathname)();let f=async n=>{if(n!==i)try{localStorage.setItem("locale",n);let e=Object.keys(d),o=await l(n,e);u(n),j(o)}catch(n){console.error("Failed to change language:",n),localStorage.setItem("locale",i)}};return(0,r.useEffect)(()=>{u(o),j(c)},[o,c]),(0,t.jsx)(a.Provider,{value:{locale:i,t:function(n){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",o=arguments.length>2?arguments[2]:void 0,t=n.split("."),r=d[e];for(let s of t){if(!r||"object"!=typeof r||!(s in r))return console.warn("Translation key not found: ".concat(e,".").concat(n)),(null==o?void 0:o.returnObjects)?[]:n;r=r[s]}return(null==o?void 0:o.returnObjects)?r:"string"==typeof r?r:n},changeLanguage:f,translations:d},children:e})}function i(){let n=(0,r.useContext)(a);if(void 0===n)throw Error("useI18n must be used within an I18nProvider");return n}async function l(n,e){let t={};for(let r of e)try{let e=await o(1539)("./".concat(n,"/").concat(r,".json"));t[r]=e.default||e}catch(e){if(console.warn("Failed to load translation: ".concat(n,"/").concat(r,".json")),"en"!==n)try{let n=await o(2401)("./".concat(r,".json"));t[r]=n.default||n}catch(n){console.warn("Failed to load fallback translation: en/".concat(r,".json")),t[r]={}}else t[r]={}}return t}},622:function(n,e,o){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var t=o(2265),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,c=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function l(n,e,o){var t,s={},l=null,u=null;for(t in void 0!==o&&(l=""+o),void 0!==e.key&&(l=""+e.key),void 0!==e.ref&&(u=e.ref),e)a.call(e,t)&&!i.hasOwnProperty(t)&&(s[t]=e[t]);if(n&&n.defaultProps)for(t in e=n.defaultProps)void 0===s[t]&&(s[t]=e[t]);return{$$typeof:r,type:n,key:l,ref:u,props:s,_owner:c.current}}e.Fragment=s,e.jsx=l,e.jsxs=l},7437:function(n,e,o){"use strict";n.exports=o(622)},4033:function(n,e,o){n.exports=o(290)}},function(n){n.O(0,[2971,7864,1744],function(){return n(n.s=8027)}),_N_E=n.O()}]);