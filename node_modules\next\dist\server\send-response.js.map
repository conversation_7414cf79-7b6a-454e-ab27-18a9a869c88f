{"version": 3, "sources": ["../../src/server/send-response.ts"], "names": ["sendResponse", "req", "res", "response", "process", "env", "NEXT_RUNTIME", "statusCode", "status", "statusMessage", "statusText", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "splitCookiesString", "append<PERSON><PERSON>er", "originalResponse", "body", "method", "pipeReadable", "end"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;8BAVO;uBACM;AAS5B,eAAeA,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB;IAElB,4BAA4B;IAC5B,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YAKvC,kCAAkC;QAClCH;QALA,iCAAiC;QACjCD,IAAIK,UAAU,GAAGJ,SAASK,MAAM;QAChCN,IAAIO,aAAa,GAAGN,SAASO,UAAU;SAGvCP,oBAAAA,SAASQ,OAAO,qBAAhBR,kBAAkBS,OAAO,CAAC,CAACC,OAAOC;YAChC,yDAAyD;YACzD,IAAIA,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,UAAUC,IAAAA,yBAAkB,EAACJ,OAAQ;oBAC9CX,IAAIgB,YAAY,CAACJ,MAAME;gBACzB;YACF,OAAO;gBACLd,IAAIgB,YAAY,CAACJ,MAAMD;YACzB;QACF;QAEA;;;;;KAKC,GAED,MAAMM,mBAAmB,AAACjB,IAAyBiB,gBAAgB;QAEnE,qGAAqG;QACrG,IAAIhB,SAASiB,IAAI,IAAInB,IAAIoB,MAAM,KAAK,QAAQ;YAC1C,MAAMC,IAAAA,0BAAY,EAACnB,SAASiB,IAAI,EAAED;QACpC,OAAO;YACLA,iBAAiBI,GAAG;QACtB;IACF;AACF"}