{"version": 3, "sources": ["../../../src/lib/typescript/writeConfigurationDefaults.ts"], "names": ["getRequiredConfiguration", "writeConfigurationDefaults", "getDesiredCompilerOptions", "ts", "userTsConfig", "o", "lib", "suggested", "allowJs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "strict", "semver", "lt", "version", "forceConsistentCasingInFileNames", "undefined", "noEmit", "gte", "incremental", "esModuleInterop", "value", "reason", "module", "parsedValue", "Module<PERSON>ind", "ESNext", "parsed<PERSON><PERSON>ues", "ES2020", "CommonJS", "AMD", "NodeNext", "Node16", "moduleResolution", "ModuleResolutionKind", "<PERSON><PERSON><PERSON>", "Node10", "NodeJs", "Node12", "filter", "val", "resolveJsonModule", "compilerOptions", "verbatimModuleSyntax", "isolatedModules", "jsx", "JsxEmit", "Preserve", "res", "desiredCompilerOptions", "optionKey", "Object", "keys", "ev", "tsConfigPath", "isFirstTimeSetup", "isAppDirEnabled", "distDir", "hasPagesDir", "fs", "writeFile", "os", "EOL", "options", "tsOptions", "raw", "rawConfig", "getTypeScriptConfiguration", "userTsConfigContent", "readFile", "encoding", "CommentJson", "parse", "suggestedActions", "requiredActions", "check", "push", "chalk", "cyan", "bold", "includes", "_", "nextAppTypes", "include", "plugins", "Array", "isArray", "hasNextPlugin", "some", "name", "length", "Log", "info", "strict<PERSON>ull<PERSON>hecks", "exclude", "stringify", "for<PERSON>ach", "action", "white"], "mappings": ";;;;;;;;;;;;;;;IAoGgBA,wBAAwB;eAAxBA;;IAiBMC,0BAA0B;eAA1BA;;;oBArHS;8DACb;qEACW;+DACV;2DACJ;4CAE4B;6DACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAarB,SAASC,0BACPC,EAA+B,EAC/BC,YAAoD;QA4D9CA;IA1DN,MAAMC,IAAiC;QACrC,qEAAqE;QACrE,gBAAgB;QAChBC,KAAK;YAAEC,WAAW;gBAAC;gBAAO;gBAAgB;aAAS;QAAC;QACpDC,SAAS;YAAED,WAAW;QAAK;QAC3BE,cAAc;YAAEF,WAAW;QAAK;QAChCG,QAAQ;YAAEH,WAAW;QAAM;QAC3B,GAAII,eAAM,CAACC,EAAE,CAACT,GAAGU,OAAO,EAAE,WACtB;YAAEC,kCAAkC;gBAAEP,WAAW;YAAK;QAAE,IACxDQ,SAAS;QACbC,QAAQ;YAAET,WAAW;QAAK;QAC1B,GAAII,eAAM,CAACM,GAAG,CAACd,GAAGU,OAAO,EAAE,WACvB;YAAEK,aAAa;gBAAEX,WAAW;YAAK;QAAE,IACnCQ,SAAS;QAEb,8DAA8D;QAC9D,4CAA4C;QAC5C,8EAA8E;QAC9EI,iBAAiB;YACfC,OAAO;YACPC,QAAQ;QACV;QACAC,QAAQ;YACNC,aAAapB,GAAGqB,UAAU,CAACC,MAAM;YACjC,4BAA4B;YAC5BC,cAAc;gBACZvB,GAAGqB,UAAU,CAACG,MAAM;gBACpBxB,GAAGqB,UAAU,CAACC,MAAM;gBACpBtB,GAAGqB,UAAU,CAACI,QAAQ;gBACtBzB,GAAGqB,UAAU,CAACK,GAAG;gBACjB1B,GAAGqB,UAAU,CAACM,QAAQ;gBACtB3B,GAAGqB,UAAU,CAACO,MAAM;aACrB;YACDX,OAAO;YACPC,QAAQ;QACV;QACAW,kBAAkB;YAChB,sDAAsD;YACtDT,aACEpB,GAAG8B,oBAAoB,CAACC,OAAO,IAC/B/B,GAAG8B,oBAAoB,CAACH,QAAQ,IAChC,AAAC3B,GAAG8B,oBAAoB,CAASE,MAAM,IACvChC,GAAG8B,oBAAoB,CAACG,MAAM;YAChC,4BAA4B;YAC5BV,cAAc;gBACXvB,GAAG8B,oBAAoB,CAASE,MAAM,IACrChC,GAAG8B,oBAAoB,CAACG,MAAM;gBAChC,qDAAqD;gBACrD,kDAAkD;gBACjDjC,GAAG8B,oBAAoB,CAASI,MAAM;gBACvClC,GAAG8B,oBAAoB,CAACF,MAAM;gBAC9B5B,GAAG8B,oBAAoB,CAACH,QAAQ;gBAChC3B,GAAG8B,oBAAoB,CAACC,OAAO;aAChC,CAACI,MAAM,CAAC,CAACC,MAAQ,OAAOA,QAAQ;YACjCnB,OAAO;YACPC,QAAQ;QACV;QACAmB,mBAAmB;YAAEpB,OAAO;YAAMC,QAAQ;QAA8B;QACxE,GAAIjB,CAAAA,iCAAAA,gCAAAA,aAAcqC,eAAe,qBAA7BrC,8BAA+BsC,oBAAoB,MAAK,OACxD3B,YACA;YACE4B,iBAAiB;gBACfvB,OAAO;gBACPC,QAAQ;YACV;QACF,CAAC;QACLuB,KAAK;YACHrB,aAAapB,GAAG0C,OAAO,CAACC,QAAQ;YAChC1B,OAAO;YACPC,QAAQ;QACV;IACF;IAEA,OAAOhB;AACT;AAEO,SAASL,yBACdG,EAA+B;IAE/B,MAAM4C,MAAqD,CAAC;IAE5D,MAAMC,yBAAyB9C,0BAA0BC;IACzD,KAAK,MAAM8C,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAMI,KAAKJ,sBAAsB,CAACC,UAAU;QAC5C,IAAI,CAAE,CAAA,WAAWG,EAAC,GAAI;YACpB;QACF;QACAL,GAAG,CAACE,UAAU,GAAGG,GAAG7B,WAAW,IAAI6B,GAAGhC,KAAK;IAC7C;IAEA,OAAO2B;AACT;AAEO,eAAe9C,2BACpBE,EAA+B,EAC/BkD,YAAoB,EACpBC,gBAAyB,EACzBC,eAAwB,EACxBC,OAAe,EACfC,WAAoB;IAEpB,IAAIH,kBAAkB;QACpB,MAAMI,YAAE,CAACC,SAAS,CAACN,cAAc,OAAOO,WAAE,CAACC,GAAG;IAChD;IAEA,MAAM,EAAEC,SAASC,SAAS,EAAEC,KAAKC,SAAS,EAAE,GAC1C,MAAMC,IAAAA,sDAA0B,EAAC/D,IAAIkD,cAAc;IAErD,MAAMc,sBAAsB,MAAMT,YAAE,CAACU,QAAQ,CAACf,cAAc;QAC1DgB,UAAU;IACZ;IACA,MAAMjE,eAAekE,aAAYC,KAAK,CAACJ;IACvC,IAAI/D,aAAaqC,eAAe,IAAI,QAAQ,CAAE,CAAA,aAAawB,SAAQ,GAAI;QACrE7D,aAAaqC,eAAe,GAAG,CAAC;QAChCa,mBAAmB;IACrB;IAEA,MAAMN,yBAAyB9C,0BAA0BC,IAAIC;IAE7D,MAAMoE,mBAA6B,EAAE;IACrC,MAAMC,kBAA4B,EAAE;IACpC,KAAK,MAAMxB,aAAaC,OAAOC,IAAI,CAACH,wBAAyB;QAC3D,MAAM0B,QAAQ1B,sBAAsB,CAACC,UAAU;QAC/C,IAAI,eAAeyB,OAAO;YACxB,IAAI,CAAEzB,CAAAA,aAAac,SAAQ,GAAI;gBAC7B,IAAI,CAAC3D,aAAaqC,eAAe,EAAE;oBACjCrC,aAAaqC,eAAe,GAAG,CAAC;gBAClC;gBACArC,aAAaqC,eAAe,CAACQ,UAAU,GAAGyB,MAAMnE,SAAS;gBACzDiE,iBAAiBG,IAAI,CACnBC,cAAK,CAACC,IAAI,CAAC5B,aAAa,iBAAiB2B,cAAK,CAACE,IAAI,CAACJ,MAAMnE,SAAS;YAEvE;QACF,OAAO,IAAI,WAAWmE,OAAO;gBAIrBA;YAHN,MAAMtB,KAAKW,SAAS,CAACd,UAAU;YAC/B,IACE,CAAE,CAAA,kBAAkByB,SAChBA,sBAAAA,MAAMhD,YAAY,qBAAlBgD,oBAAoBK,QAAQ,CAAC3B,MAC7B,iBAAiBsB,QACjBA,MAAMnD,WAAW,KAAK6B,KACtBsB,MAAMtD,KAAK,KAAKgC,EAAC,GACrB;gBACA,IAAI,CAAChD,aAAaqC,eAAe,EAAE;oBACjCrC,aAAaqC,eAAe,GAAG,CAAC;gBAClC;gBACArC,aAAaqC,eAAe,CAACQ,UAAU,GAAGyB,MAAMtD,KAAK;gBACrDqD,gBAAgBE,IAAI,CAClBC,cAAK,CAACC,IAAI,CAAC5B,aACT,iBACA2B,cAAK,CAACE,IAAI,CAACJ,MAAMtD,KAAK,IACtB,CAAC,EAAE,EAAEsD,MAAMrD,MAAM,CAAC,CAAC,CAAC;YAE1B;QACF,OAAO;YACL,6DAA6D;YAC7D,MAAM2D,IAAWN;QACnB;IACF;IAEA,MAAMO,eAAe,CAAC,EAAEzB,QAAQ,cAAc,CAAC;IAE/C,IAAI,CAAE,CAAA,aAAaS,SAAQ,GAAI;QAC7B7D,aAAa8E,OAAO,GAAG3B,kBACnB;YAAC;YAAiB0B;YAAc;YAAW;SAAW,GACtD;YAAC;YAAiB;YAAW;SAAW;QAC5CT,iBAAiBG,IAAI,CACnBC,cAAK,CAACC,IAAI,CAAC,aACT,iBACAD,cAAK,CAACE,IAAI,CACRvB,kBACI,CAAC,mBAAmB,EAAE0B,aAAa,yBAAyB,CAAC,GAC7D,CAAC,wCAAwC,CAAC;IAGtD,OAAO,IAAI1B,mBAAmB,CAACU,UAAUiB,OAAO,CAACH,QAAQ,CAACE,eAAe;QACvE7E,aAAa8E,OAAO,CAACP,IAAI,CAACM;QAC1BT,iBAAiBG,IAAI,CACnBC,cAAK,CAACC,IAAI,CAAC,aACT,yBACAD,cAAK,CAACE,IAAI,CAAC,CAAC,CAAC,EAAEG,aAAa,CAAC,CAAC;IAEpC;IAEA,wCAAwC;IACxC,IAAI1B,iBAAiB;QACnB,qEAAqE;QACrE,MAAM4B,UAAU;eACVC,MAAMC,OAAO,CAACtB,UAAUoB,OAAO,IAAIpB,UAAUoB,OAAO,GAAG,EAAE;eACzD/E,aAAaqC,eAAe,IAChC2C,MAAMC,OAAO,CAACjF,aAAaqC,eAAe,CAAC0C,OAAO,IAC9C/E,aAAaqC,eAAe,CAAC0C,OAAO,GACpC,EAAE;SACP;QACD,MAAMG,gBAAgBH,QAAQI,IAAI,CAChC,CAAC,EAAEC,IAAI,EAAoB,GAAKA,SAAS;QAG3C,8EAA8E;QAC9E,0DAA0D;QAC1D,4EAA4E;QAC5E,IACE,CAACpF,aAAaqC,eAAe,IAC5B0C,QAAQM,MAAM,IACb,CAACH,iBACD,aAAarB,aACZ,CAAA,CAACA,UAAUxB,eAAe,IAAI,CAACwB,UAAUxB,eAAe,CAAC0C,OAAO,AAAD,GAClE;YACAO,KAAIC,IAAI,CACN,CAAC,OAAO,EAAEf,cAAK,CAACE,IAAI,CAClB,iBACA,yLAAyL,EAAEF,cAAK,CAACC,IAAI,CACrM,mCACA,+JAA+J,CAAC;QAEtK,OAAO,IAAI,CAACS,eAAe;YACzB,IAAI,CAAE,CAAA,aAAalF,aAAaqC,eAAe,AAAD,GAAI;gBAChDrC,aAAaqC,eAAe,CAAC0C,OAAO,GAAG,EAAE;YAC3C;YACA/E,aAAaqC,eAAe,CAAC0C,OAAO,CAACR,IAAI,CAAC;gBAAEa,MAAM;YAAO;YACzDhB,iBAAiBG,IAAI,CACnBC,cAAK,CAACC,IAAI,CAAC,aACT,yBACAD,cAAK,CAACE,IAAI,CAAC,CAAC,gBAAgB,CAAC;QAEnC;QAEA,yEAAyE;QACzE,yCAAyC;QACzC,IACErB,eACAF,mBACAnD,aAAaqC,eAAe,IAC5B,CAACrC,aAAaqC,eAAe,CAAC/B,MAAM,IACpC,CAAE,CAAA,sBAAsBN,aAAaqC,eAAe,AAAD,GACnD;YACArC,aAAaqC,eAAe,CAACmD,gBAAgB,GAAG;YAChDpB,iBAAiBG,IAAI,CACnBC,cAAK,CAACC,IAAI,CAAC,sBAAsB,iBAAiBD,cAAK,CAACE,IAAI,CAAC,CAAC,IAAI,CAAC;QAEvE;IACF;IAEA,IAAI,CAAE,CAAA,aAAab,SAAQ,GAAI;QAC7B7D,aAAayF,OAAO,GAAG;YAAC;SAAe;QACvCrB,iBAAiBG,IAAI,CACnBC,cAAK,CAACC,IAAI,CAAC,aAAa,iBAAiBD,cAAK,CAACE,IAAI,CAAC,CAAC,gBAAgB,CAAC;IAE1E;IAEA,IAAIN,iBAAiBiB,MAAM,GAAG,KAAKhB,gBAAgBgB,MAAM,GAAG,GAAG;QAC7D;IACF;IAEA,MAAM/B,YAAE,CAACC,SAAS,CAChBN,cACAiB,aAAYwB,SAAS,CAAC1F,cAAc,MAAM,KAAKwD,WAAE,CAACC,GAAG;IAGvD6B,KAAIC,IAAI,CAAC;IACT,IAAIrC,kBAAkB;QACpBoC,KAAIC,IAAI,CACN,CAAC,qDAAqD,EAAEf,cAAK,CAACC,IAAI,CAChE,iBACA,cAAc,CAAC;QAEnB;IACF;IAEAa,KAAIC,IAAI,CACN,CAAC,6DAA6D,EAAEf,cAAK,CAACC,IAAI,CACxE,iBACA,qCAAqC,EAAED,cAAK,CAACC,IAAI,CAAC,SAAS,YAAY,CAAC;IAE5E,IAAIL,iBAAiBiB,MAAM,EAAE;QAC3BC,KAAIC,IAAI,CACN,CAAC,kDAAkD,EAAEf,cAAK,CAACC,IAAI,CAC7D,iBACA,eAAe,EAAED,cAAK,CAACC,IAAI,CAC3B,kBACA,+BAA+B,CAAC;QAGpCL,iBAAiBuB,OAAO,CAAC,CAACC,SAAWN,KAAIC,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE7DN,KAAIC,IAAI,CAAC;IACX;IAEA,IAAIlB,gBAAgBgB,MAAM,EAAE;QAC1BC,KAAIC,IAAI,CACN,CAAC,cAAc,EAAEf,cAAK,CAACqB,KAAK,CAC1B,qBACA,mBAAmB,EAAErB,cAAK,CAACC,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAGzDJ,gBAAgBsB,OAAO,CAAC,CAACC,SAAWN,KAAIC,IAAI,CAAC,CAAC,IAAI,EAAEK,OAAO,CAAC;QAE5DN,KAAIC,IAAI,CAAC;IACX;AACF"}