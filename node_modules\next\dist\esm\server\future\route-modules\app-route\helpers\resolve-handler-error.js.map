{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/resolve-handler-error.ts"], "names": ["isNotFoundError", "getURLFromRedirectError", "isRedirectError", "handleNotFoundResponse", "handleTemporaryRedirectResponse", "resolveHandlerError", "err", "redirect", "Error", "mutableCookies"], "mappings": "AAAA,SAASA,eAAe,QAAQ,6CAA4C;AAC5E,SACEC,uBAAuB,EACvBC,eAAe,QACV,4CAA2C;AAClD,SACEC,sBAAsB,EACtBC,+BAA+B,QAC1B,kCAAiC;AAExC,OAAO,SAASC,oBAAoBC,GAAQ;IAC1C,IAAIJ,gBAAgBI,MAAM;QACxB,MAAMC,WAAWN,wBAAwBK;QACzC,IAAI,CAACC,UAAU;YACb,MAAM,IAAIC,MAAM;QAClB;QAEA,wDAAwD;QACxD,OAAOJ,gCAAgCG,UAAUD,IAAIG,cAAc;IACrE;IAEA,IAAIT,gBAAgBM,MAAM;QACxB,0DAA0D;QAC1D,OAAOH;IACT;IAEA,6DAA6D;IAC7D,OAAO;AACT"}