{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.test.ts"], "names": ["getNamedRouteRegex", "describe", "it", "regex", "expect", "routeKeys", "toEqual", "nxtIauthor", "nxtPid", "groups", "pos", "repeat", "optional", "re", "test", "toBe", "toBeUndefined"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gBAAe;AAElDC,SAAS,sBAAsB;IAC7BC,GAAG,wEAAwE;QACzE,MAAMC,QAAQH,mBAAmB,4BAA4B;QAE7DI,OAAOD,MAAME,SAAS,EAAEC,OAAO,CAAC;YAC9BC,YAAY;YACZC,QAAQ;QACV;QAEAJ,OAAOD,MAAMM,MAAM,CAAC,SAAS,EAAEH,OAAO,CAAC;YACrCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOD,MAAMM,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOD,MAAMU,EAAE,CAACC,IAAI,CAAC,wBAAwBC,IAAI,CAAC;IACpD;IAEAb,GAAG,kDAAkD;QACnD,MAAMC,QAAQH,mBAAmB,iCAAiC;QAElEI,OAAOD,MAAME,SAAS,EAAEC,OAAO,CAAC;YAC9BC,YAAY;YACZC,QAAQ;QACV;QAEAJ,OAAOD,MAAMM,MAAM,CAAC,SAAS,EAAEH,OAAO,CAAC;YACrCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOD,MAAMM,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOD,MAAMU,EAAE,CAACC,IAAI,CAAC,6BAA6BC,IAAI,CAAC;IACzD;IAEAb,GAAG,4EAA4E;QAC7E,MAAMC,QAAQH,mBAAmB,0BAA0B;QAE3DI,OAAOD,MAAME,SAAS,EAAEC,OAAO,CAAC;YAC9BE,QAAQ;QACV;QAEAJ,OAAOD,MAAMM,MAAM,CAAC,SAAS,EAAEO,aAAa;QAE5CZ,OAAOD,MAAMM,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOD,MAAMU,EAAE,CAACC,IAAI,CAAC,0BAA0BC,IAAI,CAAC;IACtD;IAEAb,GAAG,gDAAgD;QACjD,MAAMC,QAAQH,mBAAmB,kBAAkB;QAEnDI,OAAOD,MAAME,SAAS,EAAEC,OAAO,CAAC;YAC9BE,QAAQ;QACV;QAEAJ,OAAOD,MAAMM,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;IACF;IAEAV,GAAG,0DAA0D;QAC3D,MAAMC,QAAQH,mBAAmB,qBAAqB;QAEtDI,OAAOD,MAAME,SAAS,EAAEC,OAAO,CAAC;YAC9BE,QAAQ;QACV;QAEAJ,OAAOD,MAAMM,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOD,MAAMU,EAAE,CAACC,IAAI,CAAC,cAAcC,IAAI,CAAC;QACxCX,OAAOD,MAAMU,EAAE,CAACC,IAAI,CAAC,kBAAkBC,IAAI,CAAC;QAC5CX,OAAOD,MAAMU,EAAE,CAACC,IAAI,CAAC,YAAYC,IAAI,CAAC;IACxC;AACF"}