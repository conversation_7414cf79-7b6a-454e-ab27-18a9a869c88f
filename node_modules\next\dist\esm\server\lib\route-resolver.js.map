{"version": 3, "sources": ["../../../src/server/lib/route-resolver.ts"], "names": ["url", "path", "findPageFile", "getRequestMeta", "setupDebug", "getCloneableBody", "findPagesDir", "setupFsCheck", "proxyRequest", "getResolveRoutes", "PERMANENT_REDIRECT_STATUS", "formatHostname", "signalFromNodeResponse", "getMiddlewareRouteMatcher", "pipeReadable", "debug", "makeResolver", "dir", "nextConfig", "middleware", "hostname", "port", "fs<PERSON><PERSON><PERSON>", "dev", "minimalMode", "config", "appDir", "pagesDir", "fetchHostname", "ensure<PERSON><PERSON>back", "item", "result", "type", "Error", "itemPath", "pageExtensions", "distDir", "join", "middlewareInfo", "name", "paths", "files", "map", "file", "process", "cwd", "wasm", "assets", "length", "middlewareMatcher", "matcher", "regexp", "originalSource", "resolveRoutes", "isNodeDebugging", "workerType", "pages", "initialize", "requestHandler", "req", "res", "headers", "cloneableBody", "run", "require", "edgeFunctionEntry", "request", "method", "i18n", "basePath", "trailingSlash", "body", "signal", "useCache", "onWarning", "console", "warn", "err", "upgradeHandler", "deleteAppClientCache", "deleteCache", "clearModuleContext", "propagateServerField", "resolveRoute", "routeResult", "isUpgradeReq", "matchedOutput", "bodyStream", "statusCode", "parsedUrl", "resHeaders", "finished", "pathname", "query", "key", "Object", "keys", "<PERSON><PERSON><PERSON><PERSON>", "destination", "format", "end", "protocol", "undefined", "cloneBodyStream", "experimental", "proxyTimeout"], "mappings": "AAGA,OAAO,kBAAiB;AACxB,OAAO,yBAAwB;AAE/B,OAAOA,SAAS,MAAK;AACrB,OAAOC,UAAU,OAAM;AACvB,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,cAAc,QAAQ,kBAAiB;AAChD,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,gBAAgB,QAAQ,kBAAiB;AAClD,SAASC,YAAY,QAAQ,2BAA0B;AACvD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,YAAY,QAAQ,+BAA8B;AAC3D,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,yBAAyB,QAAQ,6BAA4B;AACtE,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,sBAAsB,QAAQ,8CAA6C;AACpF,SAASC,yBAAyB,QAAQ,yDAAwD;AAElG,SAASC,YAAY,QAAQ,mBAAkB;AA+B/C,MAAMC,QAAQX,WAAW;AAEzB,OAAO,eAAeY,aACpBC,GAAW,EACXC,UAA8B,EAC9BC,UAA4B,EAC5B,EAAEC,WAAW,WAAW,EAAEC,OAAO,IAAI,EAA0B;IAE/D,MAAMC,YAAY,MAAMf,aAAa;QACnCU;QACAM,KAAK;QACLC,aAAa;QACbC,QAAQP;IACV;IACA,MAAM,EAAEQ,MAAM,EAAEC,QAAQ,EAAE,GAAGrB,aAAaW;IAC1C,mDAAmD;IACnD,MAAMW,gBAAgBjB,eAAeS;IAErCE,UAAUO,cAAc,CAAC,OAAOC;QAC9B,IAAIC,SAAwB;QAE5B,IAAID,KAAKE,IAAI,KAAK,WAAW;YAC3B,IAAI,CAACN,QAAQ;gBACX,MAAM,IAAIO,MAAM;YAClB;YACAF,SAAS,MAAM7B,aACbwB,QACAI,KAAKI,QAAQ,EACbhB,WAAWiB,cAAc,EACzB;QAEJ,OAAO,IAAIL,KAAKE,IAAI,KAAK,YAAY;YACnC,IAAI,CAACL,UAAU;gBACb,MAAM,IAAIM,MAAM;YAClB;YACAF,SAAS,MAAM7B,aACbyB,UACAG,KAAKI,QAAQ,EACbhB,WAAWiB,cAAc,EACzB;QAEJ;QACA,IAAI,CAACJ,QAAQ;YACX,MAAM,IAAIE,MAAM,CAAC,yBAAyB,EAAEH,KAAKE,IAAI,CAAC,CAAC,EAAEF,KAAKI,QAAQ,CAAC,CAAC;QAC1E;IACF;IAEA,MAAME,UAAUnC,KAAKoC,IAAI,CAACpB,KAAKC,WAAWkB,OAAO;IACjD,MAAME,iBAAiBnB,aACnB;QACEoB,MAAM;QACNC,OAAOrB,WAAWsB,KAAK,CAACC,GAAG,CAAC,CAACC,OAAS1C,KAAKoC,IAAI,CAACO,QAAQC,GAAG,IAAIF;QAC/DG,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ,IACA,CAAC;IAEL,IAAI5B,8BAAAA,WAAYsB,KAAK,CAACO,MAAM,EAAE;YAE1B7B;QADFG,UAAU2B,iBAAiB,GAAGpC,0BAC5BM,EAAAA,sBAAAA,WAAW+B,OAAO,qBAAlB/B,oBAAoBuB,GAAG,CAAC,CAACZ,OAAU,CAAA;gBACjCqB,QAAQrB;gBACRsB,gBAAgBtB;YAClB,CAAA,OAAO;YAAC;gBAAEqB,QAAQ;gBAAMC,gBAAgB;YAAU;SAAE;IAExD;IAEA,MAAMC,gBAAgB5C,iBACpBa,WACAJ,YACA;QACED;QACAI;QACAD;QACAkC,iBAAiB;QACjB/B,KAAK;QACLgC,YAAY;IACd,GACA;QACEC,OAAO;YACL,MAAMC;gBACJ,OAAO;oBACL,MAAMC,gBAAeC,GAAG,EAAEC,GAAG;wBAC3B,IAAI,CAACD,IAAIE,OAAO,CAAC,sBAAsB,EAAE;4BACvC,MAAM,IAAI5B,MAAM,CAAC,yCAAyC,CAAC;wBAC7D;wBAEA,MAAM6B,gBAAgBzD,iBAAiBsD;wBACvC,MAAM,EAAEI,GAAG,EAAE,GACXC,QAAQ;wBAEV,MAAMjC,SAAS,MAAMgC,IAAI;4BACvB3B;4BACAG,MAAMD,eAAeC,IAAI,IAAI;4BAC7BC,OAAOF,eAAeE,KAAK,IAAI,EAAE;4BACjCyB,mBAAmB3B;4BACnB4B,SAAS;gCACPL,SAASF,IAAIE,OAAO;gCACpBM,QAAQR,IAAIQ,MAAM,IAAI;gCACtBjD,YAAY;oCACVkD,MAAMlD,WAAWkD,IAAI;oCACrBC,UAAUnD,WAAWmD,QAAQ;oCAC7BC,eAAepD,WAAWoD,aAAa;gCACzC;gCACAtE,KAAK,CAAC,OAAO,EAAE4B,cAAc,CAAC,EAAEP,KAAK,EAAEsC,IAAI3D,GAAG,CAAC,CAAC;gCAChDuE,MAAMT;gCACNU,QAAQ5D,uBAAuBgD;4BACjC;4BACAa,UAAU;4BACVC,WAAWC,QAAQC,IAAI;wBACzB;wBAEA,MAAMC,MAAM,IAAI5C;wBACd4C,IAAY9C,MAAM,GAAGA;wBACvB,MAAM8C;oBACR;oBACA,MAAMC;wBACJ,MAAM,IAAI7C,MAAM,CAAC,0CAA0C,CAAC;oBAC9D;gBACF;YACF;YACA8C,yBAAwB;YACxB,MAAMC,gBAAe;YACrB,MAAMC,uBAAsB;YAC5B,MAAMC,yBAAwB;QAChC;IACF,GACA,CAAC;IAGH,OAAO,eAAeC,aACpBxB,GAAoB,EACpBC,GAAmB;QAEnB,MAAMwB,cAAc,MAAM/B,cAAc;YACtCM;YACAC;YACAyB,cAAc;YACdb,QAAQ5D,uBAAuBgD;QACjC;QAEA,MAAM,EACJ0B,aAAa,EACbC,UAAU,EACVC,UAAU,EACVC,SAAS,EACTC,UAAU,EACVC,QAAQ,EACT,GAAGP;QAEJrE,MAAM,mBAAmB4C,IAAI3D,GAAG,EAAE;YAChCsF;YACAE;YACAE;YACAH,YAAY,CAAC,CAACA;YACdE,WAAW;gBACTG,UAAUH,UAAUG,QAAQ;gBAC5BC,OAAOJ,UAAUI,KAAK;YACxB;YACAF;QACF;QAEA,KAAK,MAAMG,OAAOC,OAAOC,IAAI,CAACN,cAAc,CAAC,GAAI;YAC/C9B,IAAIqC,SAAS,CAACH,KAAKJ,UAAU,CAACI,IAAI;QACpC;QAEA,IAAI,CAACP,cAAcC,cAAcA,aAAa,OAAOA,aAAa,KAAK;YACrE,MAAMU,cAAclG,IAAImG,MAAM,CAACV;YAC/B7B,IAAI4B,UAAU,GAAGA;YACjB5B,IAAIqC,SAAS,CAAC,YAAYC;YAE1B,IAAIV,eAAe9E,2BAA2B;gBAC5CkD,IAAIqC,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEC,YAAY,CAAC;YACjD;YACAtC,IAAIwC,GAAG,CAACF;YACR;QACF;QAEA,kCAAkC;QAClC,IAAIX,YAAY;YACd3B,IAAI4B,UAAU,GAAGA,cAAc;YAC/B,OAAO,MAAM1E,aAAayE,YAAY3B;QACxC;QAEA,IAAI+B,YAAYF,UAAUY,QAAQ,EAAE;gBAMhClG;YALF,MAAMK,aACJmD,KACAC,KACA6B,WACAa,YACAnG,kBAAAA,eAAewD,KAAK,4CAApBxD,gBAA6CoG,eAAe,IAC5DrF,WAAWsF,YAAY,CAACC,YAAY;YAEtC;QACF;QAEA7C,IAAIqC,SAAS,CAAC,yBAAyB;QACvCrC,IAAIwC,GAAG;QAEP,OAAO;YACLpE,MAAM;YACNwD,YAAY;YACZ3B,SAAS6B;YACT1F,KAAKA,IAAImG,MAAM,CAACV;QAClB;IACF;AACF"}