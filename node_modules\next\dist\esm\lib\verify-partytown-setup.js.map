{"version": 3, "sources": ["../../src/lib/verify-partytown-setup.ts"], "names": ["promises", "chalk", "path", "hasNecessaryDependencies", "fileExists", "FileType", "FatalE<PERSON>r", "Log", "getPkgManager", "missingDependencyError", "dir", "packageManager", "bold", "red", "cyan", "copyPartytownStaticFiles", "deps", "staticDir", "partytownLibDir", "join", "hasPartytownLibDir", "Directory", "rm", "recursive", "force", "copyLibFiles", "Promise", "resolve", "require", "resolved", "get", "verifyPartytownSetup", "targetDir", "partytownDeps", "file", "pkg", "exportsRestrict", "missing", "length", "err", "warn", "console", "error", "message", "process", "exit"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,KAAI;AAC7B,OAAOC,WAAW,2BAA0B;AAE5C,OAAOC,UAAU,OAAM;AACvB,SACEC,wBAAwB,QAEnB,+BAA8B;AACrC,SAASC,UAAU,EAAEC,QAAQ,QAAQ,gBAAe;AACpD,SAASC,UAAU,QAAQ,gBAAe;AAC1C,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,aAAa,QAAQ,4BAA2B;AAEzD,eAAeC,uBAAuBC,GAAW;IAC/C,MAAMC,iBAAiBH,cAAcE;IAErC,MAAM,IAAIJ,WACRL,MAAMW,IAAI,CAACC,GAAG,CACZ,sHAEA,SACAZ,MAAMW,IAAI,CAAC,CAAC,oCAAoC,CAAC,IACjD,SACA,CAAC,EAAE,EAAEX,MAAMW,IAAI,CAACE,IAAI,CAClB,AAACH,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IAAK,0BAChC,CAAC,GACH,SACAV,MAAMW,IAAI,CACR,CAAC,wEAAwE,EAAEX,MAAMa,IAAI,CACnF,uBACA,wBAAwB,CAAC,IAE7B;AAEN;AAEA,eAAeC,yBACbC,IAA2B,EAC3BC,SAAiB;IAEjB,MAAMC,kBAAkBhB,KAAKiB,IAAI,CAACF,WAAW;IAC7C,MAAMG,qBAAqB,MAAMhB,WAC/Bc,iBACAb,SAASgB,SAAS;IAGpB,IAAID,oBAAoB;QACtB,MAAMpB,SAASsB,EAAE,CAACJ,iBAAiB;YAAEK,WAAW;YAAMC,OAAO;QAAK;IACpE;IAEA,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAMC,QAAQC,OAAO,CAC5CC,QAAQ1B,KAAKiB,IAAI,CAACH,KAAKa,QAAQ,CAACC,GAAG,CAAC,0BAA2B;IAGjE,MAAML,aAAaP;AACrB;AAEA,OAAO,eAAea,qBACpBrB,GAAW,EACXsB,SAAiB;IAEjB,IAAI;YAYEC;QAXJ,MAAMA,gBAAuC,MAAM9B,yBACjDO,KACA;YACE;gBACEwB,MAAM;gBACNC,KAAK;gBACLC,iBAAiB;YACnB;SACD;QAGH,IAAIH,EAAAA,yBAAAA,cAAcI,OAAO,qBAArBJ,uBAAuBK,MAAM,IAAG,GAAG;YACrC,MAAM7B,uBAAuBC;QAC/B,OAAO;YACL,IAAI;gBACF,MAAMK,yBAAyBkB,eAAeD;YAChD,EAAE,OAAOO,KAAK;gBACZhC,IAAIiC,IAAI,CACN,CAAC,wFAAwF,EAAEvC,MAAMW,IAAI,CAACE,IAAI,CACxG,yBACA,8BAA8B,CAAC;YAErC;QACF;IACF,EAAE,OAAOyB,KAAK;QACZ,8EAA8E;QAC9E,IAAIA,eAAejC,YAAY;YAC7BmC,QAAQC,KAAK,CAACH,IAAII,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMN;IACR;AACF"}