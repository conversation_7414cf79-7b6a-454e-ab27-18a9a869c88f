'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { I18nProvider, loadTranslations } from '@/lib/i18n-provider';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  const pathname = usePathname();
  const [locale, setLocale] = useState('en');
  const [translations, setTranslations] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for locale in localStorage or use default
    let currentLocale = 'en';

    if (typeof window !== 'undefined') {
      const savedLocale = localStorage.getItem('locale');
      const supportedLocales = ['en', 'ja'];

      if (savedLocale && supportedLocales.includes(savedLocale)) {
        currentLocale = savedLocale;
      } else {
        // Try to detect from browser language
        const browserLang = navigator.language.split('-')[0];
        if (supportedLocales.includes(browserLang)) {
          currentLocale = browserLang;
        }
      }
    }

    setLocale(currentLocale);

    // Load translations
    const loadAllTranslations = async () => {
      try {
        const namespaces = [
          'common',
          'navbar',
          'footer',
          'hero',
          'faq',
          'faq-page',
          'contact',
          'not-found',
          'markdown-editor',
          'demo',
          'demo-player',
          'editor',
          'layout',
          'benefits',
          'comparison',
          'features',
          'introduction',
          'tutorial',
          'use-cases',
          'privacy',
          'markdown-to-html',
          'markdown-to-pdf',
          'markdown-to-word',
          'markdown-to-html-page',
          'markdown-to-pdf-page',
          'markdown-to-word-page'
        ];
        
        const loadedTranslations = await loadTranslations(currentLocale, namespaces);
        setTranslations(loadedTranslations);
      } catch (error) {
        console.error('Failed to load translations:', error);
        // Fallback to empty translations
        setTranslations({});
      } finally {
        setIsLoading(false);
      }
    };

    loadAllTranslations();
  }, [pathname]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <I18nProvider locale={locale} translations={translations}>
      <Navbar />
      <main>{children}</main>
      <Footer />
    </I18nProvider>
  );
}
