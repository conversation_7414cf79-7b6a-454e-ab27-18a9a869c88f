{"version": 3, "sources": ["../../src/lib/verifyTypeScriptSetup.ts"], "names": ["verifyTypeScriptSetup", "requiredPackages", "file", "pkg", "exportsRestrict", "dir", "distDir", "cacheDir", "intentDirs", "tsconfigPath", "typeCheckPreflight", "disableStaticImages", "hasAppDir", "hasPagesDir", "resolvedTsConfigPath", "path", "join", "deps", "intent", "getTypeScriptIntent", "version", "hasNecessaryDependencies", "missing", "length", "isCI", "missingDepsError", "console", "log", "chalk", "bold", "yellow", "cyan", "installDependencies", "catch", "err", "error", "command", "tsPath", "resolved", "get", "ts", "Promise", "resolve", "require", "semver", "lt", "warn", "writeConfigurationDefaults", "firstTimeSetup", "writeAppTypeDeclarations", "baseDir", "imageImportsEnabled", "isAppDirEnabled", "result", "runTypeCheck", "CompileError", "red", "message", "process", "exit", "FatalE<PERSON>r"], "mappings": ";;;;+BAsCsBA;;;eAAAA;;;8DAtCJ;6DACD;0CAKV;+DACY;8BACU;4BACF;6DACN;qCAEe;0CAEK;4CACE;qCACP;wBACf;wCACY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEjC,MAAMC,mBAAmB;IACvB;QACEC,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAEM,eAAeJ,sBAAsB,EAC1CK,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EAWZ;IACC,MAAMC,uBAAuBC,aAAI,CAACC,IAAI,CAACX,KAAKI;IAE5C,IAAI;YAaEQ;QAZJ,wCAAwC;QACxC,MAAMC,SAAS,MAAMC,IAAAA,wCAAmB,EAACd,KAAKG,YAAYC;QAC1D,IAAI,CAACS,QAAQ;YACX,OAAO;gBAAEE,SAAS;YAAK;QACzB;QAEA,4DAA4D;QAC5D,IAAIH,OAA8B,MAAMI,IAAAA,kDAAwB,EAC9DhB,KACAJ;QAGF,IAAIgB,EAAAA,gBAAAA,KAAKK,OAAO,qBAAZL,cAAcM,MAAM,IAAG,GAAG;YAC5B,IAAIC,YAAI,EAAE;gBACR,4DAA4D;gBAC5D,2DAA2D;gBAC3D,MAAMC,IAAAA,wCAAgB,EAACpB,KAAKY,KAAKK,OAAO;YAC1C;YACAI,QAAQC,GAAG,CACTC,cAAK,CAACC,IAAI,CAACC,MAAM,CACf,CAAC,gGAAgG,CAAC,IAElG,OACA,4BACA,SACAF,cAAK,CAACC,IAAI,CACR,gEACED,cAAK,CAACG,IAAI,CAAC,mBACX,sFAEJ;YAEJ,MAAMC,IAAAA,wCAAmB,EAAC3B,KAAKY,KAAKK,OAAO,EAAE,MAAMW,KAAK,CAAC,CAACC;gBACxD,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDR,QAAQS,KAAK,CACX,CAAC,+FAA+F,CAAC,GAC/F,AAACD,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;YACAjB,OAAO,MAAMI,IAAAA,kDAAwB,EAAChB,KAAKJ;QAC7C;QAEA,8CAA8C;QAC9C,MAAMoC,SAASpB,KAAKqB,QAAQ,CAACC,GAAG,CAAC;QACjC,MAAMC,KAAM,MAAMC,QAAQC,OAAO,CAC/BC,QAAQN;QAGV,IAAIO,eAAM,CAACC,EAAE,CAACL,GAAGpB,OAAO,EAAE,UAAU;YAClCO,KAAImB,IAAI,CACN,CAAC,yHAAyH,EAAEN,GAAGpB,OAAO,CAAC,CAAC;QAE5I;QAEA,+DAA+D;QAC/D,MAAM2B,IAAAA,sDAA0B,EAC9BP,IACA1B,sBACAI,OAAO8B,cAAc,EACrBpC,WACAN,SACAO;QAEF,qEAAqE;QACrE,kBAAkB;QAClB,MAAMoC,IAAAA,kDAAwB,EAAC;YAC7BC,SAAS7C;YACT8C,qBAAqB,CAACxC;YACtBE;YACAuC,iBAAiBxC;QACnB;QAEA,IAAIyC;QACJ,IAAI3C,oBAAoB;YACtB,MAAM,EAAE4C,YAAY,EAAE,GAAGX,QAAQ;YAEjC,yEAAyE;YACzEU,SAAS,MAAMC,aACbd,IACAnC,KACAC,SACAQ,sBACAP,UACAK;QAEJ;QACA,OAAO;YAAEyC;YAAQjC,SAASoB,GAAGpB,OAAO;QAAC;IACvC,EAAE,OAAOc,KAAK;QACZ,+DAA+D;QAC/D,IAAIA,eAAeqB,0BAAY,EAAE;YAC/B7B,QAAQS,KAAK,CAACP,cAAK,CAAC4B,GAAG,CAAC;YACxB9B,QAAQS,KAAK,CAACD,IAAIuB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf,OAAO,IAAIzB,eAAe0B,sBAAU,EAAE;YACpClC,QAAQS,KAAK,CAACD,IAAIuB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMzB;IACR;AACF"}