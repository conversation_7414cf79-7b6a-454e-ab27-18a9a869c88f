{"version": 3, "sources": ["../../../src/server/lib/render-server.ts"], "names": ["clearModuleContext", "deleteAppClientCache", "deleteCache", "propagateServerField", "initialize", "result", "apps", "sandboxContext", "requireCacheHotReloader", "process", "env", "NODE_ENV", "require", "target", "filePaths", "filePath", "dir", "field", "value", "app", "Error", "appField", "server", "apply", "Array", "isArray", "opts", "type", "__NEXT_PRIVATE_RENDER_WORKER", "title", "requestHandler", "upgradeHandler", "next", "_routerWorker", "workerType", "_renderWorker", "hostname", "customServer", "httpServer", "port", "isNodeDebugging", "getRequestHandler", "getUpgradeHandler", "prepare", "serverFields"], "mappings": ";;;;;;;;;;;;;;;;;;IA8BgBA,kBAAkB;eAAlBA;;IAIAC,oBAAoB;eAApBA;;IAIAC,WAAW;eAAXA;;IAMMC,oBAAoB;eAApBA;;IAuBAC,UAAU;eAAVA;;;6DAjEL;;;;;;AAGjB,MAAMC,SAWF,CAAC;AAEL,IAAIC,OAA4D,CAAC;AAEjE,IAAIC;AACJ,IAAIC;AAIJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,iBAAiBK,QAAQ;IACzBJ,0BAA0BI,QAAQ;AACpC;AAEO,SAASZ,mBAAmBa,MAAc;IAC/C,OAAON,kCAAAA,eAAgBP,kBAAkB,CAACa;AAC5C;AAEO,SAASZ;IACd,OAAOO,2CAAAA,wBAAyBP,oBAAoB;AACtD;AAEO,SAASC,YAAYY,SAAmB;IAC7C,KAAK,MAAMC,YAAYD,UAAW;QAChCN,2CAAAA,wBAAyBN,WAAW,CAACa;IACvC;AACF;AAEO,eAAeZ,qBACpBa,GAAW,EACXC,KAA8B,EAC9BC,KAAU;IAEV,MAAMC,MAAMb,IAAI,CAACU,IAAI;IACrB,IAAI,CAACG,KAAK;QACR,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,WAAW,AAACF,IAAYG,MAAM;IAElC,IAAID,UAAU;QACZ,IAAI,OAAOA,QAAQ,CAACJ,MAAM,KAAK,YAAY;YACzC,MAAMI,QAAQ,CAACJ,MAAM,CAACM,KAAK,CACzB,AAACJ,IAAYG,MAAM,EACnBE,MAAMC,OAAO,CAACP,SAASA,QAAQ,EAAE;QAErC,OAAO;YACLG,QAAQ,CAACJ,MAAM,GAAGC;QACpB;IACF;AACF;AAEO,eAAed,WAAWsB,IAchC;IACC,8DAA8D;IAC9D,4BAA4B;IAC5B,IAAIrB,MAAM,CAACqB,KAAKV,GAAG,CAAC,EAAE;QACpB,OAAOX,MAAM,CAACqB,KAAKV,GAAG,CAAC;IACzB;IAEA,MAAMW,OAAOlB,QAAQC,GAAG,CAACkB,4BAA4B;IACrD,IAAID,MAAM;QACRlB,QAAQoB,KAAK,GAAG,wBAAwBF;IAC1C;IAEA,IAAIG;IACJ,IAAIC;IAEJ,MAAMZ,MAAMa,IAAAA,aAAI,EAAC;QACf,GAAGN,IAAI;QACPO,eAAeP,KAAKQ,UAAU,KAAK;QACnCC,eAAeT,KAAKQ,UAAU,KAAK;QACnCE,UAAUV,KAAKU,QAAQ,IAAI;QAC3BC,cAAc;QACdC,YAAYZ,KAAKJ,MAAM;QACvBiB,MAAMb,KAAKa,IAAI;QACfC,iBAAiBd,KAAKc,eAAe;IACvC;IACAlC,IAAI,CAACoB,KAAKV,GAAG,CAAC,GAAGG;IACjBW,iBAAiBX,IAAIsB,iBAAiB;IACtCV,iBAAiBZ,IAAIuB,iBAAiB;IAEtC,MAAMvB,IAAIwB,OAAO,CAACjB,KAAKkB,YAAY;IAEnCvC,MAAM,CAACqB,KAAKV,GAAG,CAAC,GAAG;QACjBc;QACAC;IACF;IACA,OAAO1B,MAAM,CAACqB,KAAKV,GAAG,CAAC;AACzB"}