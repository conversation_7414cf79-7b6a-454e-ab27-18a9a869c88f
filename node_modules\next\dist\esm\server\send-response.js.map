{"version": 3, "sources": ["../../src/server/send-response.ts"], "names": ["pipeReadable", "splitCookiesString", "sendResponse", "req", "res", "response", "process", "env", "NEXT_RUNTIME", "statusCode", "status", "statusMessage", "statusText", "headers", "for<PERSON>ach", "value", "name", "toLowerCase", "cookie", "append<PERSON><PERSON>er", "originalResponse", "body", "method", "end"], "mappings": "AAEA,SAASA,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,cAAa;AAEhD;;;;;;CAMC,GACD,OAAO,eAAeC,aACpBC,GAAoB,EACpBC,GAAqB,EACrBC,QAAkB;IAElB,4BAA4B;IAC5B,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;YAKvC,kCAAkC;QAClCH;QALA,iCAAiC;QACjCD,IAAIK,UAAU,GAAGJ,SAASK,MAAM;QAChCN,IAAIO,aAAa,GAAGN,SAASO,UAAU;SAGvCP,oBAAAA,SAASQ,OAAO,qBAAhBR,kBAAkBS,OAAO,CAAC,CAACC,OAAOC;YAChC,yDAAyD;YACzD,IAAIA,KAAKC,WAAW,OAAO,cAAc;gBACvC,qFAAqF;gBACrF,KAAK,MAAMC,UAAUjB,mBAAmBc,OAAQ;oBAC9CX,IAAIe,YAAY,CAACH,MAAME;gBACzB;YACF,OAAO;gBACLd,IAAIe,YAAY,CAACH,MAAMD;YACzB;QACF;QAEA;;;;;KAKC,GAED,MAAMK,mBAAmB,AAAChB,IAAyBgB,gBAAgB;QAEnE,qGAAqG;QACrG,IAAIf,SAASgB,IAAI,IAAIlB,IAAImB,MAAM,KAAK,QAAQ;YAC1C,MAAMtB,aAAaK,SAASgB,IAAI,EAAED;QACpC,OAAO;YACLA,iBAAiBG,GAAG;QACtB;IACF;AACF"}