{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["encodeText", "decodeText", "htmlEscapeJsonString", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "useFlightResponse", "writable", "req", "clientReferenceManifest", "rscChunks", "flightResponseRef", "nonce", "current", "createFromReadableStream", "require", "renderStream", "forwardStream", "tee", "res", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "bootstrapped", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "writer", "getWriter", "startScriptTag", "JSON", "stringify", "textDecoder", "TextDecoder", "read", "then", "done", "value", "push", "write", "setTimeout", "close", "responsePartial", "scripts"], "mappings": "AAEA,SAASA,UAAU,EAAEC,UAAU,QAAQ,gCAA+B;AACtE,SAASC,oBAAoB,QAAQ,gBAAe;AAEpD,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD;;;CAGC,GACD,OAAO,SAASC,kBACdC,QAAoC,EACpCC,GAA+B,EAC/BC,uBAAgD,EAChDC,SAAuB,EACvBC,iBAAoC,EACpCC,KAAc;IAEd,IAAID,kBAAkBE,OAAO,KAAK,MAAM;QACtC,OAAOF,kBAAkBE,OAAO;IAClC;IACA,wGAAwG;IACxG,MAAM,EACJC,wBAAwB,EACzB,GAAGC,QAAQ,CAAC,oCAAoC,CAAC;IAElD,MAAM,CAACC,cAAcC,cAAc,GAAGT,IAAIU,GAAG;IAC7C,MAAMC,MAAML,yBAAyBE,cAAc;QACjDI,WAAWlB,gBACPO,wBAAwBY,oBAAoB,GAC5CZ,wBAAwBa,gBAAgB;IAC9C;IACAX,kBAAkBE,OAAO,GAAGM;IAE5B,IAAII,eAAe;IACnB,iDAAiD;IACjD,MAAMC,gBAAgBP,cAAcQ,SAAS;IAC7C,MAAMC,SAASnB,SAASoB,SAAS;IACjC,MAAMC,iBAAiBhB,QACnB,CAAC,cAAc,EAAEiB,KAAKC,SAAS,CAAClB,OAAO,CAAC,CAAC,GACzC;IACJ,MAAMmB,cAAc,IAAIC;IAExB,SAASC;QACPT,cAAcS,IAAI,GAAGC,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAE;YACxC,IAAIA,OAAO;gBACT1B,UAAU2B,IAAI,CAACD;YACjB;YAEA,IAAI,CAACb,cAAc;gBACjBA,eAAe;gBACfG,OAAOY,KAAK,CACVvC,WACE,CAAC,EAAE6B,eAAe,uCAAuC,EAAE3B,qBACzD4B,KAAKC,SAAS,CAAC;oBAAC;iBAAE,GAClB,UAAU,CAAC;YAGnB;YACA,IAAIK,MAAM;gBACR,iIAAiI;gBACjI,iEAAiE;gBACjE,+IAA+I;gBAC/I,iDAAiD;gBACjDI,WAAW;oBACT5B,kBAAkBE,OAAO,GAAG;gBAC9B;gBACAa,OAAOc,KAAK;YACd,OAAO;gBACL,MAAMC,kBAAkBzC,WAAWoC,OAAOL;gBAC1C,MAAMW,UAAU,CAAC,EAAEd,eAAe,mBAAmB,EAAE3B,qBACrD4B,KAAKC,SAAS,CAAC;oBAAC;oBAAGW;iBAAgB,GACnC,UAAU,CAAC;gBAEbf,OAAOY,KAAK,CAACvC,WAAW2C;gBACxBT;YACF;QACF;IACF;IACAA;IAEA,OAAOd;AACT"}