{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/websocket.ts"], "names": ["source", "eventCallbacks", "getSocketProtocol", "assetPrefix", "protocol", "location", "URL", "addMessageListener", "callback", "push", "sendMessage", "data", "readyState", "OPEN", "send", "connectHMR", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "eventCallback", "handleDisconnect", "onerror", "onclose", "hostname", "port", "replace", "url", "startsWith", "split", "WebSocket", "path", "onopen", "onmessage"], "mappings": "AAEA,IAAIA;AAIJ,MAAMC,iBAAwC,EAAE;AAEhD,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,SAASD,QAAQ;IAEhC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIE,IAAIH,aAAaC,QAAQ;IAC1C,EAAE,UAAM,CAAC;IAET,OAAOA,aAAa,UAAU,OAAO;AACvC;AAEA,OAAO,SAASG,mBAAmBC,QAAwB;IACzDP,eAAeQ,IAAI,CAACD;AACtB;AAEA,OAAO,SAASE,YAAYC,IAAY;IACtC,IAAI,CAACX,UAAUA,OAAOY,UAAU,KAAKZ,OAAOa,IAAI,EAAE;IAClD,OAAOb,OAAOc,IAAI,CAACH;AACrB;AAEA,OAAO,SAASI,WAAWC,OAA8C;IACvE,SAASC;QACP,IAAIjB,QAAQA,OAAOkB,KAAK;QAExB,SAASC;YACPC,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,sDAAsD;YACtD,MAAMC,MAAwBC,KAAKC,KAAK,CAACH,MAAMb,IAAI;YACnD,KAAK,MAAMiB,iBAAiB3B,eAAgB;gBAC1C2B,cAAcH;YAChB;QACF;QAEA,SAASI;YACP7B,OAAO8B,OAAO,GAAG;YACjB9B,OAAO+B,OAAO,GAAG;YACjB/B,OAAOkB,KAAK;YACZD;QACF;QAEA,MAAM,EAAEe,QAAQ,EAAEC,IAAI,EAAE,GAAG5B;QAC3B,MAAMD,WAAWF,kBAAkBc,QAAQb,WAAW,IAAI;QAC1D,MAAMA,cAAca,QAAQb,WAAW,CAAC+B,OAAO,CAAC,QAAQ;QAExD,IAAIC,MAAM,AAAG/B,WAAS,QAAK4B,WAAS,MAAGC,OACrC9B,CAAAA,cAAc,AAAC,MAAGA,cAAgB,EAAC;QAGrC,IAAIA,YAAYiC,UAAU,CAAC,SAAS;YAClCD,MAAM,AAAG/B,WAAS,QAAKD,YAAYkC,KAAK,CAAC,MAAM,CAAC,EAAE;QACpD;QAEArC,SAAS,IAAIoB,OAAOkB,SAAS,CAAC,AAAC,KAAEH,MAAMnB,QAAQuB,IAAI;QACnDvC,OAAOwC,MAAM,GAAGrB;QAChBnB,OAAO8B,OAAO,GAAGD;QACjB7B,OAAO+B,OAAO,GAAGF;QACjB7B,OAAOyC,SAAS,GAAGlB;IACrB;IAEAN;AACF"}