{"version": 3, "sources": ["../../../../../src/server/future/route-modules/helpers/response-handlers.ts"], "names": ["handleTemporaryRedirectResponse", "handleBadRequestResponse", "handleNotFoundResponse", "handleMethodNotAllowedResponse", "handleInternalServerErrorResponse", "url", "mutableCookies", "headers", "Headers", "location", "appendMutableCookies", "Response", "status"], "mappings": ";;;;;;;;;;;;;;;;;;IAGgBA,+BAA+B;eAA/BA;;IAWAC,wBAAwB;eAAxBA;;IAIAC,sBAAsB;eAAtBA;;IAIAC,8BAA8B;eAA9BA;;IAIAC,iCAAiC;eAAjCA;;;gCA1BqB;AAG9B,SAASJ,gCACdK,GAAW,EACXC,cAA+B;IAE/B,MAAMC,UAAU,IAAIC,QAAQ;QAAEC,UAAUJ;IAAI;IAE5CK,IAAAA,oCAAoB,EAACH,SAASD;IAE9B,OAAO,IAAIK,SAAS,MAAM;QAAEC,QAAQ;QAAKL;IAAQ;AACnD;AAEO,SAASN;IACd,OAAO,IAAIU,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEO,SAASV;IACd,OAAO,IAAIS,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEO,SAAST;IACd,OAAO,IAAIQ,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEO,SAASR;IACd,OAAO,IAAIO,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C"}