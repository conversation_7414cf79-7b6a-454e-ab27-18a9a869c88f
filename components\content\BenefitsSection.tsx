'use client';

import {
  Clock,
  DollarSign,
  Shield,
  Zap,
  Users,
  Award,
  TrendingUp,
  CheckCircle,
  Target,
  Star
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export default function BenefitsSection() {
  const { t } = useTranslation('benefits');

  // Icon mapping
  const iconMap = {
    clock: Clock,
    dollarSign: DollarSign,
    shield: Shield,
    zap: Zap,
    users: Users,
    award: Award
  };

  // Get benefits from translation
  const benefitsData = t('benefits', { returnObjects: true });
  const benefits = Array.isArray(benefitsData) ? benefitsData as Array<{
    title: string;
    description: string;
    stats: string;
    icon: keyof typeof iconMap;
  }> : [];

  // Get metrics from translation
  const metricsData = t('metrics.data', { returnObjects: true });
  const metrics = Array.isArray(metricsData) ? metricsData as Array<{
    value: string;
    label: string;
    description: string;
  }> : [];

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-shadow">
              <div className="flex items-center mb-4">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-lg">
                  {(() => {
                    const IconComponent = iconMap[benefit.icon];
                    return <IconComponent className="h-6 w-6 text-white" />;
                  })()}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 ml-3">{benefit.title}</h3>
              </div>
              <p className="text-gray-600 mb-4 leading-relaxed">{benefit.description}</p>
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-sm font-medium text-blue-800">{benefit.stats}</div>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-12 border border-white/20 mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              {t('metrics.title')}
            </h3>
            <p className="text-gray-600 max-w-3xl mx-auto">
              {t('metrics.description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {metrics.map((metric, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">{metric.value}</div>
                <div className="text-lg font-semibold text-gray-900 mb-1">{metric.label}</div>
                <div className="text-sm text-gray-600">{metric.description}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              {t('improvements.productivity.title')}
            </h3>
            <div className="space-y-4">
              {(() => {
                const productivityItems = t('improvements.productivity.items', { returnObjects: true });
                const items = Array.isArray(productivityItems) ? productivityItems as string[] : [];
                return items.map((item, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <span className="text-gray-700">{item}</span>
                  </div>
                ));
              })()}
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-6">
              {t('improvements.satisfaction.title')}
            </h3>
            <div className="space-y-4">
              {(() => {
                const satisfactionItems = t('improvements.satisfaction.items', { returnObjects: true });
                const items = Array.isArray(satisfactionItems) ? satisfactionItems as string[] : [];
                const icons = [Star, Target, TrendingUp, Award];
                return items.map((item, index) => {
                  const IconComponent = icons[index] || Star;
                  return (
                    <div key={index} className="flex items-center">
                      <IconComponent className="h-5 w-5 text-yellow-300 mr-3" />
                      <span>{item}</span>
                    </div>
                  );
                });
              })()}
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl p-12 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">
            {t('cta.title')}
          </h3>
          <p className="text-lg opacity-90 mb-8 max-w-3xl mx-auto">
            {t('cta.description')}
          </p>
          <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            {t('cta.button')}
          </button>
        </div>
      </div>
    </section>
  );
}