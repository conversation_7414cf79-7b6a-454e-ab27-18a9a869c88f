"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2937],{2937:function(e){e.exports=JSON.parse('{"title":"Markdown to HTML Converter","subtitle":"Convert your Markdown content to clean, professional HTML with advanced customization options","buttons":{"htmlSettings":"HTML Settings","enterFullscreen":"Enter Fullscreen","exitFullscreen":"Exit Fullscreen","generateHtml":"Generate HTML","generating":"Generating...","uploadFile":"Upload Markdown File","copyMarkdown":"Copy Markdown","copyHtml":"Copy HTML","clear":"Clear"},"panels":{"markdownEditor":"Markdown Editor","htmlPreview":"HTML Preview"},"placeholders":{"markdownInput":"Paste your Markdown content here or start typing...","customCss":"/* Add your custom CSS here */"},"status":{"success":"HTML generated successfully!","error":"Failed to generate HTML. Please try again."},"settings":{"title":"HTML Output Settings","outputFormat":{"title":"Output Format","formatType":"Format Type","options":{"fragment":"HTML Fragment","clean":"Clean HTML","standalone":"Standalone Page"},"cssFramework":"CSS Framework","cssOptions":{"none":"None","bootstrap":"Bootstrap","tailwind":"Tailwind CSS","bulma":"Bulma"},"theme":"Theme","themeOptions":{"default":"Default","github":"GitHub","material":"Material Design","dark":"Dark Theme","minimal":"Minimal"}},"features":{"title":"Features","includeCss":"Include CSS Styles","includeJs":"Include JavaScript","syntaxHighlighting":"Syntax Highlighting","mathRendering":"Math Rendering","responsiveDesign":"Responsive Design","accessibilityFeatures":"Accessibility Features","semanticHtml":"Semantic HTML","minifyOutput":"Minify Output"},"customCss":"Custom CSS"},"previewModes":{"desktop":"Desktop View","tablet":"Tablet View","mobile":"Mobile View"}}')}}]);