import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import ClientLayout from './client-layout';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Free Markdown Live Preview Online Editor | Real-time MD Preview',
  description: 'Free online Markdown live preview editor with real-time rendering. Edit and preview Markdown instantly with our powerful web-based tool. No download required.',
  keywords: 'markdown, live preview, online editor, free markdown editor, markdown to html, real-time preview, web editor, markdown converter',
  authors: [{ name: 'Markdown Live Preview' }],
  openGraph: {
    title: 'Free Markdown Live Preview Online Editor',
    description: 'Edit and preview Markdown in real-time with our free online editor',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Markdown Live Preview Online Editor',
    description: 'Edit and preview Markdown in real-time with our free online editor',
  },
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <script 
          async 
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************" 
          crossOrigin="anonymous"
        ></script>
      </head>
      <body className={inter.className}>
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}