const fs = require('fs');
const path = require('path');

// 获取所有英文翻译文件
function getEnglishFiles() {
  const enDir = path.join(__dirname, '../locales/en');
  return fs.readdirSync(enDir).filter(file => file.endsWith('.json'));
}

// 获取所有日语翻译文件
function getJapaneseFiles() {
  const jaDir = path.join(__dirname, '../locales/ja');
  return fs.readdirSync(jaDir).filter(file => file.endsWith('.json'));
}

// 递归获取对象的所有键路径
function getKeyPaths(obj, prefix = '') {
  const paths = [];
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const currentPath = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        paths.push(...getKeyPaths(obj[key], currentPath));
      } else {
        paths.push(currentPath);
      }
    }
  }
  
  return paths;
}

// 验证翻译文件
function validateTranslations() {
  console.log('🔍 验证翻译文件完整性...\n');
  
  const enFiles = getEnglishFiles();
  const jaFiles = getJapaneseFiles();
  
  console.log(`📁 英文文件数量: ${enFiles.length}`);
  console.log(`📁 日语文件数量: ${jaFiles.length}\n`);
  
  // 检查缺失的日语文件
  const missingJaFiles = enFiles.filter(file => !jaFiles.includes(file));
  if (missingJaFiles.length > 0) {
    console.log('❌ 缺失的日语翻译文件:');
    missingJaFiles.forEach(file => console.log(`   - ${file}`));
    console.log();
  } else {
    console.log('✅ 所有日语翻译文件都存在\n');
  }
  
  // 检查每个文件的键完整性
  let totalMissingKeys = 0;
  
  for (const file of enFiles) {
    if (!jaFiles.includes(file)) continue;
    
    try {
      const enPath = path.join(__dirname, '../locales/en', file);
      const jaPath = path.join(__dirname, '../locales/ja', file);
      
      const enContent = JSON.parse(fs.readFileSync(enPath, 'utf8'));
      const jaContent = JSON.parse(fs.readFileSync(jaPath, 'utf8'));
      
      const enKeys = getKeyPaths(enContent);
      const jaKeys = getKeyPaths(jaContent);
      
      const missingKeys = enKeys.filter(key => !jaKeys.includes(key));
      
      if (missingKeys.length > 0) {
        console.log(`❌ ${file} 中缺失的键:`);
        missingKeys.forEach(key => console.log(`   - ${key}`));
        console.log();
        totalMissingKeys += missingKeys.length;
      } else {
        console.log(`✅ ${file} - 所有键都存在`);
      }
      
    } catch (error) {
      console.log(`❌ 解析 ${file} 时出错: ${error.message}`);
    }
  }
  
  console.log('\n📊 验证总结:');
  console.log(`   - 英文文件: ${enFiles.length}`);
  console.log(`   - 日语文件: ${jaFiles.length}`);
  console.log(`   - 缺失文件: ${missingJaFiles.length}`);
  console.log(`   - 缺失键: ${totalMissingKeys}`);
  
  if (missingJaFiles.length === 0 && totalMissingKeys === 0) {
    console.log('\n🎉 所有翻译文件都完整！');
    return true;
  } else {
    console.log('\n⚠️  发现缺失的翻译内容');
    return false;
  }
}

// 运行验证
if (require.main === module) {
  validateTranslations();
}

module.exports = { validateTranslations };
