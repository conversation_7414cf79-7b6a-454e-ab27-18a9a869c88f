{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["validateTurboNextConfig", "supportedTurbopackNextConfigOptions", "prodSpecificTurboNextConfigOptions", "dir", "isCustomTurbopack", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "chalk", "interopDefault", "isTTY", "process", "stdout", "turbopackGradient", "bold", "dim", "thankYouMessage", "join", "unsupportedParts", "babelrc", "path", "basename", "hasWebpack", "hasTurbo", "unsupportedConfig", "rawNextConfig", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isSupported", "some", "<PERSON><PERSON><PERSON>", "e", "console", "error", "hasWarningOrError", "log", "feedbackMessage", "underline", "warn", "yellow", "cyan", "map", "name", "red", "pkgManager", "exit"], "mappings": ";;;;+BAqFsBA;;;eAAAA;;;6DArFL;+DACM;2BAEkB;;;;;;AAEzC,MAAMC,sCAAsC;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,sDAAsD;IACtD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kEAAkE;AAClE,MAAMC,qCAAqC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,eAAeF,wBAAwB,EAC5CG,GAAG,EACHC,iBAAiB,EACjBC,KAAK,EAQN;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAMG,QACJH,QAAQ;IACV,MAAM,EAAEI,cAAc,EAAE,GACtBJ,QAAQ;IAEV,kGAAkG;IAClG,MAAMK,QAAQC,QAAQC,MAAM,CAACF,KAAK;IAElC,MAAMG,oBAAoB,CAAC,EAAEL,MAAMM,IAAI,CACrCJ,QACI,sVACA,iBACJ,CAAC,EAAEF,MAAMO,GAAG,CAAC,UAAU,IAAI,CAAC;IAE9B,IAAIC,kBACF;QACE;QACA;QACA;QACA;KACD,CAACC,IAAI,CAAC,QAAQ;IAEjB,IAAIC,mBAAmB;IACvB,IAAIC,UAAU,MAAMb,mBAAmBL;IACvC,IAAIkB,SAASA,UAAUC,aAAI,CAACC,QAAQ,CAACF;IAErC,IAAIG,aAAa;IACjB,IAAIC,WAAW;IAEf,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,IAAI;QACFA,gBAAgBhB,eACd,MAAMiB,IAAAA,eAAU,EAACC,mCAAwB,EAAE1B,KAAK;YAC9C2B,WAAW;QACb;QAGF,IAAI,OAAOH,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsBE,mCAAwB,EAAE;gBAC/DpB;YACF;QACF;QAEA,MAAMsB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYJ;QAE/B,IAAImB,gBAAgBzC,QAChB;eACKJ;eACAC;SACJ,GACDD;QAEJ,KAAK,MAAMkC,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,YAAY;gBAC7BvB,aAAa;YACf;YACA,IAAIW,IAAIY,UAAU,CAAC,uBAAuB;gBACxCtB,WAAW;YACb;YAEA,IAAIuB,cACFF,cAAcG,IAAI,CAAC,CAACC,eAAiBf,IAAIY,UAAU,CAACG,kBACpDR,aAAaf,eAAeQ,SAASO,aAAajC,eAAe0B;YACnE,IAAI,CAACa,aAAa;gBAChBtB,kBAAkBe,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOgB,GAAG;QACVC,QAAQC,KAAK,CAAC,mDAAmDF;IACnE;IAEA,MAAMG,oBAAoBjC,WAAWK,kBAAkBW,MAAM;IAC7D,IAAI,CAACiB,mBAAmB;QACtBpC,kBAAkBR,MAAMO,GAAG,CAACC;IAC9B;IACA,IAAI,CAACd,mBAAmB;QACtBgD,QAAQG,GAAG,CAACxC,oBAAoBG;IAClC;IAEA,IAAIsC,kBAAkB,CAAC,4CAA4C,EAAE9C,MAAM+C,SAAS,CAClF,sCACA,6BAA6B,EAAE/C,MAAM+C,SAAS,CAC9C,0CACA,EAAE,CAAC;IAEL,IAAIjC,cAAc,CAACC,UAAU;QAC3B2B,QAAQM,IAAI,CACV,CAAC,EAAE,EAAEhD,MAAMiD,MAAM,CACf,YACA;EACN,EAAE,CAAC,sHAAsH,CAAC,CAAC,CAAC;IAE5H;IAEA,IAAItC,SAAS;QACXD,oBAAoB,CAAC,oBAAoB,EAAEV,MAAMkD,IAAI,CACnDvC,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEK,kBAAkBW,MAAM,KAAK,KAC7BX,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACA0B,QAAQM,IAAI,CACV,CAAC,EAAE,EAAEhD,MAAMiD,MAAM,CAAC,YAAY,CAAC,EAAEjD,MAAMkD,IAAI,CACzC,uCACA,uDAAuD,CAAC;IAE9D,OAAO,IAAIlC,kBAAkBW,MAAM,EAAE;QACnCjB,oBAAoB,CAAC,mDAAmD,EAAEV,MAAMkD,IAAI,CAClF,kBACA,oEAAoE,EAAElC,kBACrEmC,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAEpD,MAAMqD,GAAG,CAACD,MAAM,EAAE,CAAC,EAC1C3C,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIC,oBAAoB,CAAChB,mBAAmB;QAC1C,MAAM4D,aAAa1D,cAAcH;QAEjCiD,QAAQC,KAAK,CACX,CAAC,4GAA4G,EAAEjC,iBAAiB;;;EAGpI,EAAEV,MAAMM,IAAI,CAAC4C,IAAI,CACf,CAAC,EACCI,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,EAC9C,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGL,IAAI,CAAC5D,mBAAmB;YACtBgD,QAAQM,IAAI,CAACF;YAEb3C,QAAQoD,IAAI,CAAC;QACf,OAAO;YACLb,QAAQM,IAAI,CAAC;YACbN,QAAQM,IAAI,CACV,CAAC,EAAEhD,MAAMM,IAAI,CAAC2C,MAAM,CAClB,YACA,yEAAyE,CAAC;QAEhF;IACF;IAEA,IAAI,CAACvD,mBAAmB;QACtBgD,QAAQG,GAAG,CAACC;IACd;IAEA,OAAO7B;AACT"}