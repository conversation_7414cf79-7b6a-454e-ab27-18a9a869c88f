'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  FileText, 
  Download, 
  Upload, 
  Settings, 
  Eye, 
  Palette,
  FileDown,
  Zap,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Copy,
  Image as ImageIcon,
  Type,
  Layout,
  Maximize2,
  Minimize2,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  List,
  ListOrdered
} from 'lucide-react';

interface WordSettings {
  pageSize: 'A4' | 'Letter' | 'Legal';
  orientation: 'portrait' | 'landscape';
  margin: 'normal' | 'narrow' | 'wide';
  fontSize: 'small' | 'medium' | 'large';
  fontFamily: 'calibri' | 'times' | 'arial' | 'georgia';
  theme: 'default' | 'professional' | 'academic' | 'modern';
  includeTableOfContents: boolean;
  includePageNumbers: boolean;
  headerText: string;
  footerText: string;
  lineSpacing: 'single' | 'double' | '1.5';
  paragraphSpacing: 'none' | 'small' | 'medium';
}

const defaultMarkdown = `# Markdown to Word Converter

Welcome to our **free online Markdown to Word converter**! This powerful tool transforms your Markdown documents into professional Microsoft Word (.docx) files with advanced formatting and styling options.

## Key Features

### ✅ Professional Word Output
- High-quality DOCX generation compatible with Microsoft Word
- Multiple page size options (A4, Letter, Legal)
- Portrait and landscape orientation support
- Customizable margins and spacing
- Professional typography and formatting

### ✅ Advanced Document Formatting
- **Multiple font families**: Calibri, Times New Roman, Arial, Georgia
- **Typography control**: Font size, line spacing, paragraph spacing
- **Document themes**: Default, Professional, Academic, Modern
- **Page elements**: Headers, footers, and page numbers
- **Table of contents**: Automatic generation from headings

### ✅ Complete Markdown Support
- All standard Markdown syntax elements
- Tables with professional Word formatting
- Code blocks with syntax highlighting
- Images and media embedding
- Mathematical expressions support
- Lists, links, and emphasis formatting

## How to Use This Converter

1. **Input your Markdown**: Paste or type your content in the editor panel
2. **Customize settings**: Choose page size, font, theme, and formatting options
3. **Preview document**: See how your Word document will look before downloading
4. **Generate DOCX**: Create and download your professional Word document

## Supported Markdown Elements

### Text Formatting
Make your text stand out with various formatting options:
- **Bold text** for emphasis
- *Italic text* for subtle emphasis
- ~~Strikethrough text~~ for deletions
- \`Inline code\` for technical terms
- [Hyperlinks](https://example.com) for references

### Document Structure
#### Headings
Use headings to organize your document structure:
- # Main Title (Heading 1)
- ## Section Title (Heading 2)
- ### Subsection Title (Heading 3)

#### Lists
Create organized lists for better readability:

**Unordered Lists:**
- Feature 1: Real-time conversion
- Feature 2: Professional formatting
  - Sub-feature: Custom themes
  - Sub-feature: Multiple fonts
- Feature 3: Easy export

**Ordered Lists:**
1. Open the converter
2. Paste your Markdown content
3. Customize document settings
4. Generate and download DOCX

### Code Examples
Display code with proper formatting:

\`\`\`javascript
function convertToWord() {
  console.log("Converting Markdown to Word...");
  return {
    status: "success",
    format: "docx",
    quality: "professional"
  };
}

// Call the function
convertToWord();
\`\`\`

### Tables
Create professional tables that translate perfectly to Word:

| Feature | Free Version | Premium |
|---------|-------------|---------|
| Basic Conversion | ✅ | ✅ |
| Custom Themes | ✅ | ✅ |
| Advanced Settings | ✅ | ✅ |
| Batch Processing | ❌ | ✅ |
| Priority Support | ❌ | ✅ |

### Blockquotes
Add emphasis with professional blockquotes:

> "The best way to predict the future is to create it. This converter helps you create professional documents that stand out."
> 
> — Document Creation Expert

### Mathematical Expressions
Include mathematical formulas and equations:

Inline math: $E = mc^2$

Block math:
$$
\\sum_{i=1}^{n} x_i = \\frac{1}{n} \\sum_{i=1}^{n} x_i
$$

---

## Why Choose Our Markdown to Word Converter?

### 🚀 Fast and Reliable
- **Instant conversion**: No waiting time for document generation
- **Large document support**: Handles documents of any size efficiently
- **Consistent output**: Reliable formatting every time
- **Error handling**: Robust processing with helpful error messages

### 🔒 Privacy and Security
- **Local processing**: All conversion happens in your browser
- **No data transmission**: Your documents never leave your device
- **Complete privacy**: No registration or personal information required
- **Secure environment**: Safe and protected document handling

### 💰 Completely Free
- **No registration**: Start converting immediately
- **No watermarks**: Clean, professional output
- **Unlimited conversions**: Convert as many documents as needed
- **Full feature access**: All formatting options available for free

### 🎨 Professional Results
- **Publication-ready**: Documents suitable for business and academic use
- **Consistent formatting**: Professional appearance across all platforms
- **Word compatibility**: Perfect integration with Microsoft Word
- **Print optimization**: Optimized layouts for both screen and print

### 📊 Advanced Features
- **Custom styling**: Multiple themes and formatting options
- **Document structure**: Automatic table of contents generation
- **Typography control**: Professional font and spacing options
- **Image support**: Seamless image integration and formatting

---

## Document Formatting Options

### Page Layout
- **Page sizes**: A4, Letter, Legal formats
- **Orientation**: Portrait or landscape modes
- **Margins**: Narrow, normal, or wide margin settings
- **Headers/Footers**: Custom header and footer text

### Typography
- **Font families**: Calibri, Times New Roman, Arial, Georgia
- **Font sizes**: Small (11pt), Medium (12pt), Large (14pt)
- **Line spacing**: Single, 1.5x, or double spacing
- **Paragraph spacing**: Customizable paragraph separation

### Document Themes
- **Default**: Clean, modern formatting
- **Professional**: Business-appropriate styling
- **Academic**: Scholarly document formatting
- **Modern**: Contemporary design elements

---

## Best Practices for Conversion

### 1. Document Structure
- Use proper heading hierarchy (H1 → H2 → H3)
- Include a clear document title
- Organize content with logical sections

### 2. Formatting Consistency
- Use consistent formatting throughout
- Apply emphasis (bold/italic) purposefully
- Maintain consistent list formatting

### 3. Image Optimization
- Use high-quality images for best results
- Include descriptive alt text for accessibility
- Consider image placement and sizing

### 4. Table Design
- Keep tables simple and readable
- Use clear column headers
- Avoid overly complex table structures

---

**Ready to convert?** Start by editing this sample content or paste your own Markdown text above. Customize the document settings to match your preferences and click "Generate Word Document" to create your professional DOCX file.

Transform your Markdown into beautiful Word documents today! 📄✨`;

export default function MarkdownToWordConverter() {
  const { t } = useTranslation('markdown-to-word');
  const [markdown, setMarkdown] = useState(defaultMarkdown);
  const [html, setHtml] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [conversionStatus, setConversionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [settings, setSettings] = useState<WordSettings>({
    pageSize: 'A4',
    orientation: 'portrait',
    margin: 'normal',
    fontSize: 'medium',
    fontFamily: 'calibri',
    theme: 'default',
    includeTableOfContents: false,
    includePageNumbers: true,
    headerText: '',
    footerText: '',
    lineSpacing: 'single',
    paragraphSpacing: 'small'
  });

  // Enhanced markdown to HTML converter for Word
  const convertMarkdownToHtml = (markdown: string) => {
    if (!markdown) return '';
    
    let html = markdown
      // Headers with proper Word styling
      .replace(/^### (.*$)/gim, '<h3 class="word-heading-3">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="word-heading-2">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="word-heading-1">$1</h1>')
      // Images with Word-compatible styling
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" class="word-image" />')
      // Text formatting
      .replace(/\*\*\*(.*?)\*\*\*/gim, '<strong><em>$1</em></strong>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong class="word-bold">$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em class="word-italic">$1</em>')
      .replace(/~~(.*?)~~/gim, '<del class="word-strikethrough">$1</del>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" class="word-link">$1</a>')
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre class="word-code-block"><code class="language-$1">$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code class="word-inline-code">$1</code>')
      // Tables
      .replace(/^\|(.+)\|$/gim, (match, content) => {
        const cells = content.split('|').map((cell: string) => cell.trim()).filter((cell: string) => cell);
        return '<tr>' + cells.map((cell: string) => `<td class="word-table-cell">${cell}</td>`).join('') + '</tr>';
      })
      // Lists
      .replace(/^\- (.*$)/gim, '<li class="word-list-item">$1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li class="word-ordered-item">$1</li>')
      // Blockquotes
      .replace(/^> (.*$)/gim, '<blockquote class="word-blockquote">$1</blockquote>')
      // Horizontal rules
      .replace(/^---$/gim, '<hr class="word-hr">')
      // Math expressions
      .replace(/\$\$([\s\S]*?)\$\$/gim, '<div class="word-math-block">$1</div>')
      .replace(/\$([^$]+)\$/gim, '<span class="word-math-inline">$1</span>')
      // Line breaks
      .replace(/\n/gim, '<br>');

    // Wrap consecutive list items
    html = html.replace(/(<li class="word-list-item">.*<\/li>)/s, '<ul class="word-unordered-list">$1</ul>');
    html = html.replace(/(<li class="word-ordered-item">.*<\/li>)/s, '<ol class="word-ordered-list">$1</ol>');
    
    // Wrap table rows
    if (html.includes('<tr>')) {
      html = html.replace(/(<tr>.*<\/tr>)/s, '<table class="word-table">$1</table>');
    }
    
    return html;
  };

  // Generate table of contents for Word
  const generateTableOfContents = (html: string) => {
    const headings = html.match(/<h[1-3][^>]*>([^<]+)<\/h[1-3]>/g) || [];
    if (headings.length === 0) return '';

    let toc = '<div class="word-toc"><h2 class="word-toc-title">Table of Contents</h2><ul class="word-toc-list">';
    headings.forEach(heading => {
      const level = parseInt(heading.match(/<h([1-3])/)?.[1] || '1');
      const text = heading.replace(/<[^>]*>/g, '');
      const indent = level > 1 ? `style="margin-left: ${(level - 1) * 20}px;"` : '';
      toc += `<li class="word-toc-item" ${indent}><a href="#${text}" class="word-toc-link">${text}</a></li>`;
    });
    toc += '</ul></div>';
    
    return toc;
  };

  // Get CSS styles for Word document
  const getWordStyles = () => {
    const fontFamilies = {
      calibri: 'Calibri, sans-serif',
      times: 'Times New Roman, serif',
      arial: 'Arial, sans-serif',
      georgia: 'Georgia, serif'
    };

    const fontSizes = {
      small: '11pt',
      medium: '12pt',
      large: '14pt'
    };

    const lineSpacings = {
      single: '1.0',
      '1.5': '1.5',
      double: '2.0'
    };

    const baseStyles = `
      @page {
        size: ${settings.pageSize} ${settings.orientation};
        margin: ${settings.margin === 'narrow' ? '0.5in' : settings.margin === 'wide' ? '1.5in' : '1in'};
      }
      
      body {
        font-family: ${fontFamilies[settings.fontFamily]};
        font-size: ${fontSizes[settings.fontSize]};
        line-height: ${lineSpacings[settings.lineSpacing]};
        color: #000000;
        margin: 0;
        padding: 20px;
        background: white;
      }
      
      .word-heading-1 {
        font-size: 18pt;
        font-weight: bold;
        color: #2c3e50;
        margin-top: 24pt;
        margin-bottom: 12pt;
        page-break-after: avoid;
      }
      
      .word-heading-2 {
        font-size: 16pt;
        font-weight: bold;
        color: #34495e;
        margin-top: 18pt;
        margin-bottom: 6pt;
        page-break-after: avoid;
      }
      
      .word-heading-3 {
        font-size: 14pt;
        font-weight: bold;
        color: #34495e;
        margin-top: 12pt;
        margin-bottom: 6pt;
        page-break-after: avoid;
      }
      
      .word-bold {
        font-weight: bold;
      }
      
      .word-italic {
        font-style: italic;
      }
      
      .word-strikethrough {
        text-decoration: line-through;
      }
      
      .word-link {
        color: #0066cc;
        text-decoration: underline;
      }
      
      .word-image {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 12pt auto;
        border: 1px solid #ddd;
      }
      
      .word-code-block {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 12pt;
        margin: 12pt 0;
        font-family: 'Courier New', monospace;
        font-size: 10pt;
        overflow-x: auto;
      }
      
      .word-inline-code {
        background: #f1f3f4;
        padding: 2pt 4pt;
        border-radius: 2pt;
        font-family: 'Courier New', monospace;
        font-size: 10pt;
      }
      
      .word-table {
        width: 100%;
        border-collapse: collapse;
        margin: 12pt 0;
        border: 1px solid #000;
      }
      
      .word-table-cell {
        border: 1px solid #000;
        padding: 6pt 12pt;
        text-align: left;
        vertical-align: top;
      }
      
      .word-table tr:first-child .word-table-cell {
        background-color: #f8f9fa;
        font-weight: bold;
      }
      
      .word-unordered-list {
        margin: 6pt 0;
        padding-left: 24pt;
      }
      
      .word-ordered-list {
        margin: 6pt 0;
        padding-left: 24pt;
      }
      
      .word-list-item {
        margin: 3pt 0;
      }
      
      .word-ordered-item {
        margin: 3pt 0;
      }
      
      .word-blockquote {
        border-left: 4pt solid #3498db;
        margin: 12pt 0;
        padding-left: 12pt;
        color: #666;
        font-style: italic;
      }
      
      .word-hr {
        border: none;
        border-top: 1pt solid #ccc;
        margin: 18pt 0;
      }
      
      .word-toc {
        page-break-after: always;
        margin-bottom: 24pt;
      }
      
      .word-toc-title {
        font-size: 16pt;
        font-weight: bold;
        margin-bottom: 12pt;
      }
      
      .word-toc-list {
        list-style: none;
        padding-left: 0;
      }
      
      .word-toc-item {
        margin: 6pt 0;
      }
      
      .word-toc-link {
        color: #0066cc;
        text-decoration: none;
      }
      
      .word-math-block {
        text-align: center;
        margin: 12pt 0;
        font-family: 'Times New Roman', serif;
        font-size: 14pt;
      }
      
      .word-math-inline {
        font-family: 'Times New Roman', serif;
      }
      
      p {
        margin-top: 0;
        margin-bottom: ${settings.paragraphSpacing === 'none' ? '0' : settings.paragraphSpacing === 'small' ? '6pt' : '12pt'};
      }
    `;

    // Theme-specific styles
    const themeStyles = {
      default: '',
      professional: `
        .word-heading-1, .word-heading-2, .word-heading-3 {
          color: #1f4e79;
        }
        .word-link {
          color: #1f4e79;
        }
      `,
      academic: `
        body {
          font-family: 'Times New Roman', serif;
        }
        .word-heading-1, .word-heading-2, .word-heading-3 {
          color: #000;
          text-align: center;
        }
      `,
      modern: `
        .word-heading-1, .word-heading-2, .word-heading-3 {
          color: #e74c3c;
        }
        .word-link {
          color: #e74c3c;
        }
        .word-blockquote {
          border-left-color: #e74c3c;
        }
      `
    };

    return baseStyles + themeStyles[settings.theme];
  };

  // Convert markdown to HTML
  useEffect(() => {
    const htmlContent = convertMarkdownToHtml(markdown);
    setHtml(htmlContent);
  }, [markdown]);

  // Auto-save to localStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      localStorage.setItem('word-markdown-content', markdown);
      localStorage.setItem('word-settings', JSON.stringify(settings));
    }, 1000);

    return () => clearTimeout(timer);
  }, [markdown, settings]);

  // Load saved content on mount
  useEffect(() => {
    const saved = localStorage.getItem('word-markdown-content');
    const savedSettings = localStorage.getItem('word-settings');
    
    if (saved) {
      setMarkdown(saved);
    }
    
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to parse saved settings:', error);
      }
    }
  }, []);

  // Generate Word document
  const generateWordDocument = async () => {
    setIsGenerating(true);
    setConversionStatus('idle');
    
    try {
      // Simulate Word document generation
      await new Promise(resolve => setTimeout(resolve, 2500));
      
      // Create complete HTML document
      let fullHtml = html;
      
      // Add table of contents if enabled
      if (settings.includeTableOfContents) {
        const toc = generateTableOfContents(html);
        fullHtml = toc + fullHtml;
      }
      
      // Create the complete HTML document for Word
      const completeHtml = `
        <!DOCTYPE html>
        <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word">
        <head>
          <meta charset="UTF-8">
          <meta name="ProgId" content="Word.Document">
          <meta name="Generator" content="Microsoft Word 15">
          <meta name="Originator" content="Microsoft Word 15">
          <title>Markdown Document</title>
          <style>${getWordStyles()}</style>
        </head>
        <body>
          ${settings.headerText ? `<div style="text-align: center; border-bottom: 1pt solid #ccc; padding-bottom: 6pt; margin-bottom: 12pt;">${settings.headerText}</div>` : ''}
          ${fullHtml}
          ${settings.footerText ? `<div style="text-align: center; border-top: 1pt solid #ccc; padding-top: 6pt; margin-top: 12pt;">${settings.footerText}</div>` : ''}
        </body>
        </html>
      `;
      
      // Create downloadable Word document (HTML format that Word can open)
      const blob = new Blob([completeHtml], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'document.doc'; // Using .doc extension for better compatibility
      a.click();
      URL.revokeObjectURL(url);
      
      setConversionStatus('success');
    } catch (error) {
      console.error('Word document generation failed:', error);
      setConversionStatus('error');
    } finally {
      setIsGenerating(false);
    }
  };

  // File upload handler
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setMarkdown(content);
      };
      reader.readAsText(file);
    }
  };

  // Copy to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(markdown);
      console.log('Markdown copied to clipboard');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'min-h-screen'} bg-gray-50`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{t('title')}</h1>
                <p className="text-sm text-gray-600">{t('subtitle')}</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`p-2 rounded-lg transition-colors ${
                showPreview ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={showPreview ? 'Hide Preview' : 'Show Preview'}
            >
              <Eye className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setShowSettings(!showSettings)}
              className={`p-2 rounded-lg transition-colors ${
                showSettings ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="Word Settings"
            >
              <Settings className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
              title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              {isFullscreen ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
            </button>
            
            <button
              onClick={generateWordDocument}
              disabled={isGenerating || !markdown.trim()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>{t('buttons.generating')}</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>{t('buttons.generateWord')}</span>
                </>
              )}
            </button>
          </div>
        </div>
        
        {/* Status Messages */}
        {conversionStatus === 'success' && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="text-green-800">{t('status.success')}</span>
          </div>
        )}
        
        {conversionStatus === 'error' && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <span className="text-red-800">{t('status.error')}</span>
          </div>
        )}
      </div>

      <div className="flex" style={{ height: isFullscreen ? 'calc(100vh - 89px)' : 'calc(100vh - 89px)' }}>
        {/* Settings Panel */}
        {showSettings && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('settings.title')}</h3>
              
              {/* Page Settings */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Layout className="h-4 w-4 mr-2" />
                  Page Layout
                </h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Page Size</label>
                    <select
                      value={settings.pageSize}
                      onChange={(e) => setSettings(prev => ({ ...prev, pageSize: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="A4">A4</option>
                      <option value="Letter">Letter</option>
                      <option value="Legal">Legal</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Orientation</label>
                    <select
                      value={settings.orientation}
                      onChange={(e) => setSettings(prev => ({ ...prev, orientation: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="portrait">Portrait</option>
                      <option value="landscape">Landscape</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Margins</label>
                    <select
                      value={settings.margin}
                      onChange={(e) => setSettings(prev => ({ ...prev, margin: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="narrow">Narrow</option>
                      <option value="normal">Normal</option>
                      <option value="wide">Wide</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Typography */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Type className="h-4 w-4 mr-2" />
                  Typography
                </h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Font Family</label>
                    <select
                      value={settings.fontFamily}
                      onChange={(e) => setSettings(prev => ({ ...prev, fontFamily: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="calibri">Calibri</option>
                      <option value="times">Times New Roman</option>
                      <option value="arial">Arial</option>
                      <option value="georgia">Georgia</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Font Size</label>
                    <select
                      value={settings.fontSize}
                      onChange={(e) => setSettings(prev => ({ ...prev, fontSize: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="small">Small (11pt)</option>
                      <option value="medium">Medium (12pt)</option>
                      <option value="large">Large (14pt)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Line Spacing</label>
                    <select
                      value={settings.lineSpacing}
                      onChange={(e) => setSettings(prev => ({ ...prev, lineSpacing: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="single">Single</option>
                      <option value="1.5">1.5 Lines</option>
                      <option value="double">Double</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Paragraph Spacing</label>
                    <select
                      value={settings.paragraphSpacing}
                      onChange={(e) => setSettings(prev => ({ ...prev, paragraphSpacing: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="none">None</option>
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Theme</label>
                    <select
                      value={settings.theme}
                      onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="default">Default</option>
                      <option value="professional">Professional</option>
                      <option value="academic">Academic</option>
                      <option value="modern">Modern</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Document Options */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Document Options</h4>
                
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.includeTableOfContents}
                      onChange={(e) => setSettings(prev => ({ ...prev, includeTableOfContents: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Include Table of Contents</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.includePageNumbers}
                      onChange={(e) => setSettings(prev => ({ ...prev, includePageNumbers: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Include Page Numbers</span>
                  </label>
                </div>
              </div>
              
              {/* Headers and Footers */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Headers & Footers</h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Header Text</label>
                    <input
                      type="text"
                      value={settings.headerText}
                      onChange={(e) => setSettings(prev => ({ ...prev, headerText: e.target.value }))}
                      placeholder="Optional header text"
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Footer Text</label>
                    <input
                      type="text"
                      value={settings.footerText}
                      onChange={(e) => setSettings(prev => ({ ...prev, footerText: e.target.value }))}
                      placeholder="Optional footer text"
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Editor Panel */}
        <div className={`${showPreview ? (showSettings ? 'w-1/2' : 'w-1/2') : 'w-full'} flex flex-col`}>
          <div className="bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">{t('panels.markdownEditor')}</span>
            <div className="flex items-center space-x-2">
              <input
                ref={fileInputRef}
                type="file"
                accept=".md,.markdown"
                onChange={handleFileUpload}
                className="hidden"
              />
              
              <button
                onClick={() => fileInputRef.current?.click()}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title="Upload Markdown File"
              >
                <Upload className="h-4 w-4" />
              </button>
              
              <button
                onClick={copyToClipboard}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title="Copy Markdown"
              >
                <Copy className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => setMarkdown('')}
                className="text-xs px-2 py-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
              >
                Clear
              </button>
            </div>
          </div>
          
          <textarea
            ref={textareaRef}
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
            className="flex-1 p-4 font-mono text-sm resize-none focus:outline-none bg-white"
            placeholder="Paste your Markdown content here or start typing..."
            spellCheck={false}
          />
        </div>

        {/* Preview Panel */}
        {showPreview && (
          <div className={`${showSettings ? 'w-1/2' : 'w-1/2'} flex flex-col border-l border-gray-200`}>
            <div className="bg-gray-100 border-b border-gray-200 px-4 py-2">
              <span className="text-sm font-medium text-gray-700">{t('panels.wordPreview')}</span>
            </div>
            
            <div
              ref={previewRef}
              className="flex-1 p-4 overflow-auto bg-white"
              style={{ 
                fontFamily: settings.fontFamily === 'calibri' ? 'Calibri, sans-serif' : 
                           settings.fontFamily === 'times' ? 'Times New Roman, serif' :
                           settings.fontFamily === 'arial' ? 'Arial, sans-serif' : 'Georgia, serif',
                fontSize: settings.fontSize === 'small' ? '11pt' : settings.fontSize === 'large' ? '14pt' : '12pt',
                lineHeight: settings.lineSpacing === 'single' ? '1.0' : settings.lineSpacing === 'double' ? '2.0' : '1.5'
              }}
            >
              {settings.includeTableOfContents && html && (
                <div 
                  className="mb-8 pb-8 border-b border-gray-200"
                  dangerouslySetInnerHTML={{ __html: generateTableOfContents(html) }}
                />
              )}
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: html }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}