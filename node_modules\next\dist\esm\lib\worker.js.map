{"version": 3, "sources": ["../../src/lib/worker.ts"], "names": ["Worker", "JestWorker", "getNodeOptionsWithoutInspect", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "timeout", "onRestart", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "createWorker", "forkOptions", "env", "process", "NODE_OPTIONS", "replace", "trim", "Promise", "resolve", "enableWorkerThreads", "on", "code", "signal", "console", "error", "getStdout", "pipe", "stdout", "getStderr", "stderr", "onHanging", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error", "close"], "mappings": "AACA,SAASA,UAAUC,UAAU,QAAQ,iCAAgC;AACrE,SAASC,4BAA4B,QAAQ,sBAAqB;AAGlE,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEA,OAAO,MAAMX;IAGXY,YACEC,UAAkB,EAClBC,OAKC,CACD;QACA,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAGC,aAAa,GAAGH;QAE7C,IAAII;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,MAAMC,eAAe;gBAMRN;YALX,IAAI,CAACI,OAAO,GAAG,IAAIpB,WAAWY,YAAY;gBACxC,GAAGI,WAAW;gBACdO,aAAa;oBACX,GAAGP,YAAYO,WAAW;oBAC1BC,KAAK;wBACH,GAAKR,EAAAA,2BAAAA,YAAYO,WAAW,qBAAvBP,yBAAyBQ,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGC,QAAQD,GAAG;wBACd,4CAA4C;wBAC5C,qBAAqB;wBACrBE,cAAczB,+BACX0B,OAAO,CAAC,iCAAiC,IACzCC,IAAI;oBACT;gBACF;YACF;YACAX,iBAAiB,IAAIY,QACnB,CAACC,UAAaZ,wBAAwBY;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAACd,YAAYe,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM1B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACe,OAAO,CAASb,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH;qBAAAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAe2B,EAAE,CAAC,QAAQ,CAACC,MAAMC;wBAC/B,8CAA8C;wBAC9C,IAAI,AAACD,CAAAA,QAAQC,MAAK,KAAM,IAAI,CAACd,OAAO,EAAE;4BACpCe,QAAQC,KAAK,CACX,CAAC,6CAA6C,EAAEH,KAAK,aAAa,EAAEC,OAAO,CAAC;wBAEhF;oBACF;gBACF;YACF;YAEA,IAAI,CAACd,OAAO,CAACiB,SAAS,GAAGC,IAAI,CAACb,QAAQc,MAAM;YAC5C,IAAI,CAACnB,OAAO,CAACoB,SAAS,GAAGF,IAAI,CAACb,QAAQgB,MAAM;QAC9C;QACAnB;QAEA,MAAMoB,YAAY;YAChB,MAAMrC,SAAS,IAAI,CAACe,OAAO;YAC3B,IAAI,CAACf,QAAQ;YACb,MAAMyB,UAAUZ;YAChBI;YACAjB,OAAOsC,GAAG,GAAGC,IAAI,CAAC;gBAChBd,QAAQ5B;YACV;QACF;QAEA,IAAI2C,eAAuC;QAE3C,MAAMC,aAAa;YACjB,IAAID,cAAcE,aAAaF;YAC/BA,eAAe1B,cAAc,KAAK6B,WAAWN,WAAW5B;QAC1D;QAEA,KAAK,MAAMmC,UAAUjC,YAAYkC,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGnC,UAErB,OAAO,GAAGsC;gBACRjC;gBACA,IAAI;oBACF,IAAIkC,WAAW;oBACf,OAAS;wBACPP;wBACA,MAAMQ,SAAS,MAAMzB,QAAQ0B,IAAI,CAAC;4BAC/B,IAAI,CAACnC,OAAO,AAAQ,CAAC6B,OAAO,IAAIG;4BACjCnC;yBACD;wBACD,IAAIqC,WAAWpD,WAAW,OAAOoD;wBACjC,IAAIvC,WAAWA,UAAUkC,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACRlC;oBACA2B;gBACF;YACF,IACA,AAAC,IAAI,CAAC1B,OAAO,AAAQ,CAAC6B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACpC,OAAO;QACrD;IACF;IAEAuB,MAAqC;QACnC,MAAMtC,SAAS,IAAI,CAACe,OAAO;QAC3B,IAAI,CAACf,QAAQ;YACX,MAAM,IAAIoD,MAAM;QAClB;QACArD,eAAeC;QACf,IAAI,CAACe,OAAO,GAAGC;QACf,OAAOhB,OAAOsC,GAAG;IACnB;IAEA;;GAEC,GACDe,QAAc;QACZ,IAAI,IAAI,CAACtC,OAAO,EAAE;YAChBhB,eAAe,IAAI,CAACgB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACuB,GAAG;QAClB;IACF;AACF"}