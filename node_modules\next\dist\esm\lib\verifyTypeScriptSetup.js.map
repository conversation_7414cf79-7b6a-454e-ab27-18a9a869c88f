{"version": 3, "sources": ["../../src/lib/verifyTypeScriptSetup.ts"], "names": ["chalk", "path", "hasNecessaryDependencies", "semver", "CompileError", "FatalE<PERSON>r", "log", "getTypeScriptIntent", "writeAppTypeDeclarations", "writeConfigurationDefaults", "installDependencies", "isCI", "missingDepsError", "requiredPackages", "file", "pkg", "exportsRestrict", "verifyTypeScriptSetup", "dir", "distDir", "cacheDir", "intentDirs", "tsconfigPath", "typeCheckPreflight", "disableStaticImages", "hasAppDir", "hasPagesDir", "resolvedTsConfigPath", "join", "deps", "intent", "version", "missing", "length", "console", "bold", "yellow", "cyan", "catch", "err", "error", "command", "tsPath", "resolved", "get", "ts", "Promise", "resolve", "require", "lt", "warn", "firstTimeSetup", "baseDir", "imageImportsEnabled", "isAppDirEnabled", "result", "runTypeCheck", "red", "message", "process", "exit"], "mappings": "AAAA,OAAOA,WAAW,2BAA0B;AAC5C,OAAOC,UAAU,OAAM;AAEvB,SACEC,wBAAwB,QAEnB,+BAA8B;AACrC,OAAOC,YAAY,4BAA2B;AAC9C,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,UAAU,QAAQ,gBAAe;AAC1C,YAAYC,SAAS,sBAAqB;AAE1C,SAASC,mBAAmB,QAAQ,mCAAkC;AAEtE,SAASC,wBAAwB,QAAQ,wCAAuC;AAChF,SAASC,0BAA0B,QAAQ,0CAAyC;AACpF,SAASC,mBAAmB,QAAQ,yBAAwB;AAC5D,SAASC,IAAI,QAAQ,uBAAsB;AAC3C,SAASC,gBAAgB,QAAQ,sCAAqC;AAEtE,MAAMC,mBAAmB;IACvB;QACEC,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;IACA;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAED,OAAO,eAAeC,sBAAsB,EAC1CC,GAAG,EACHC,OAAO,EACPC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,SAAS,EACTC,WAAW,EAWZ;IACC,MAAMC,uBAAuB1B,KAAK2B,IAAI,CAACV,KAAKI;IAE5C,IAAI;YAaEO;QAZJ,wCAAwC;QACxC,MAAMC,SAAS,MAAMvB,oBAAoBW,KAAKG,YAAYC;QAC1D,IAAI,CAACQ,QAAQ;YACX,OAAO;gBAAEC,SAAS;YAAK;QACzB;QAEA,4DAA4D;QAC5D,IAAIF,OAA8B,MAAM3B,yBACtCgB,KACAL;QAGF,IAAIgB,EAAAA,gBAAAA,KAAKG,OAAO,qBAAZH,cAAcI,MAAM,IAAG,GAAG;YAC5B,IAAItB,MAAM;gBACR,4DAA4D;gBAC5D,2DAA2D;gBAC3D,MAAMC,iBAAiBM,KAAKW,KAAKG,OAAO;YAC1C;YACAE,QAAQ5B,GAAG,CACTN,MAAMmC,IAAI,CAACC,MAAM,CACf,CAAC,gGAAgG,CAAC,IAElG,OACA,4BACA,SACApC,MAAMmC,IAAI,CACR,gEACEnC,MAAMqC,IAAI,CAAC,mBACX,sFAEJ;YAEJ,MAAM3B,oBAAoBQ,KAAKW,KAAKG,OAAO,EAAE,MAAMM,KAAK,CAAC,CAACC;gBACxD,IAAIA,OAAO,OAAOA,QAAQ,YAAY,aAAaA,KAAK;oBACtDL,QAAQM,KAAK,CACX,CAAC,+FAA+F,CAAC,GAC/F,AAACD,IAAYE,OAAO,GACpB;gBAEN;gBACA,MAAMF;YACR;YACAV,OAAO,MAAM3B,yBAAyBgB,KAAKL;QAC7C;QAEA,8CAA8C;QAC9C,MAAM6B,SAASb,KAAKc,QAAQ,CAACC,GAAG,CAAC;QACjC,MAAMC,KAAM,MAAMC,QAAQC,OAAO,CAC/BC,QAAQN;QAGV,IAAIvC,OAAO8C,EAAE,CAACJ,GAAGd,OAAO,EAAE,UAAU;YAClCzB,IAAI4C,IAAI,CACN,CAAC,yHAAyH,EAAEL,GAAGd,OAAO,CAAC,CAAC;QAE5I;QAEA,+DAA+D;QAC/D,MAAMtB,2BACJoC,IACAlB,sBACAG,OAAOqB,cAAc,EACrB1B,WACAN,SACAO;QAEF,qEAAqE;QACrE,kBAAkB;QAClB,MAAMlB,yBAAyB;YAC7B4C,SAASlC;YACTmC,qBAAqB,CAAC7B;YACtBE;YACA4B,iBAAiB7B;QACnB;QAEA,IAAI8B;QACJ,IAAIhC,oBAAoB;YACtB,MAAM,EAAEiC,YAAY,EAAE,GAAGR,QAAQ;YAEjC,yEAAyE;YACzEO,SAAS,MAAMC,aACbX,IACA3B,KACAC,SACAQ,sBACAP,UACAK;QAEJ;QACA,OAAO;YAAE8B;YAAQxB,SAASc,GAAGd,OAAO;QAAC;IACvC,EAAE,OAAOQ,KAAK;QACZ,+DAA+D;QAC/D,IAAIA,eAAenC,cAAc;YAC/B8B,QAAQM,KAAK,CAACxC,MAAMyD,GAAG,CAAC;YACxBvB,QAAQM,KAAK,CAACD,IAAImB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf,OAAO,IAAIrB,eAAelC,YAAY;YACpC6B,QAAQM,KAAK,CAACD,IAAImB,OAAO;YACzBC,QAAQC,IAAI,CAAC;QACf;QACA,MAAMrB;IACR;AACF"}