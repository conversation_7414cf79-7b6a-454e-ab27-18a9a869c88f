import { TFunction } from 'next-i18next';

/**
 * Utility functions for handling translations
 */

/**
 * Get translation with fallback
 * @param t - Translation function
 * @param key - Translation key
 * @param fallback - Fallback text if translation is missing
 * @param options - Translation options
 */
export function getTranslationWithFallback(
  t: TFunction,
  key: string,
  fallback: string,
  options?: any
): string {
  const translation = t(key, options);
  return translation === key ? fallback : (typeof translation === 'string' ? translation : fallback);
}

/**
 * Get nested translation safely
 * @param t - Translation function
 * @param keyPath - Dot-separated key path (e.g., 'buttons.save')
 * @param fallback - Fallback text
 */
export function getNestedTranslation(
  t: TFunction,
  keyPath: string,
  fallback?: string
): string {
  try {
    const translation = t(keyPath);
    return translation === keyPath ? (fallback || keyPath) : translation;
  } catch (error) {
    console.warn(`Translation key not found: ${keyPath}`);
    return fallback || keyPath;
  }
}

/**
 * Get translation for metadata (SEO)
 * @param t - Translation function
 * @param type - Type of metadata ('title', 'description', 'keywords')
 * @param fallback - Fallback text
 */
export function getMetadataTranslation(
  t: TFunction,
  type: 'title' | 'description' | 'keywords',
  fallback?: string
): string {
  return getNestedTranslation(t, `metadata.${type}`, fallback);
}

/**
 * Get button text translation
 * @param t - Translation function
 * @param buttonKey - Button key
 * @param fallback - Fallback text
 */
export function getButtonText(
  t: TFunction,
  buttonKey: string,
  fallback?: string
): string {
  return getNestedTranslation(t, `buttons.${buttonKey}`, fallback);
}

/**
 * Get label text translation
 * @param t - Translation function
 * @param labelKey - Label key
 * @param fallback - Fallback text
 */
export function getLabelText(
  t: TFunction,
  labelKey: string,
  fallback?: string
): string {
  return getNestedTranslation(t, `labels.${labelKey}`, fallback);
}

/**
 * Get message text translation
 * @param t - Translation function
 * @param messageKey - Message key
 * @param fallback - Fallback text
 */
export function getMessageText(
  t: TFunction,
  messageKey: string,
  fallback?: string
): string {
  return getNestedTranslation(t, `messages.${messageKey}`, fallback);
}

/**
 * Format translation with interpolation
 * @param t - Translation function
 * @param key - Translation key
 * @param values - Values to interpolate
 * @param fallback - Fallback text
 */
export function formatTranslation(
  t: TFunction,
  key: string,
  values: Record<string, any>,
  fallback?: string
): string {
  try {
    const translation = t(key, values);
    return translation === key ? (fallback || key) : (typeof translation === 'string' ? translation : (fallback || key));
  } catch (error) {
    console.warn(`Translation formatting error for key: ${key}`, error);
    return fallback || key;
  }
}

/**
 * Get array of translations
 * @param t - Translation function
 * @param keyPrefix - Key prefix (e.g., 'features')
 * @param count - Number of items expected
 */
export function getTranslationArray(
  t: TFunction,
  keyPrefix: string,
  count?: number
): string[] {
  try {
    const translations: string[] = [];
    let index = 0;
    
    while (true) {
      const key = `${keyPrefix}.${index}`;
      const translation = t(key);
      
      if (translation === key) {
        // Try alternative array format
        const altKey = `${keyPrefix}[${index}]`;
        const altTranslation = t(altKey);
        
        if (altTranslation === altKey) {
          break;
        } else {
          translations.push(altTranslation);
        }
      } else {
        translations.push(translation);
      }
      
      index++;
      
      // Safety check to prevent infinite loops
      if (count && index >= count) {
        break;
      }
      if (index > 100) {
        console.warn(`Possible infinite loop in getTranslationArray for key: ${keyPrefix}`);
        break;
      }
    }
    
    return translations;
  } catch (error) {
    console.warn(`Error getting translation array for key: ${keyPrefix}`, error);
    return [];
  }
}

/**
 * Check if translation exists
 * @param t - Translation function
 * @param key - Translation key
 */
export function hasTranslation(t: TFunction, key: string): boolean {
  try {
    const translation = t(key);
    return translation !== key;
  } catch (error) {
    return false;
  }
}

/**
 * Get language direction (LTR/RTL)
 * @param locale - Current locale
 */
export function getLanguageDirection(locale: string): 'ltr' | 'rtl' {
  const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
  return rtlLanguages.includes(locale) ? 'rtl' : 'ltr';
}

/**
 * Get language name in native script
 * @param locale - Locale code
 */
export function getLanguageName(locale: string): string {
  const languageNames: Record<string, string> = {
    en: 'English',
    ja: '日本語',
    zh: '中文',
    es: 'Español',
    fr: 'Français',
    de: 'Deutsch',
    it: 'Italiano',
    pt: 'Português',
    ru: 'Русский',
    ko: '한국어',
    ar: 'العربية',
    hi: 'हिन्दी',
  };
  
  return languageNames[locale] || locale.toUpperCase();
}
