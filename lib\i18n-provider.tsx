'use client';

import { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface Translation {
  [key: string]: any;
}

interface I18nContextType {
  locale: string;
  t: (key: string, namespace?: string, options?: { returnObjects?: boolean }) => any;
  changeLanguage: (locale: string) => void;
  translations: { [namespace: string]: Translation };
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

interface I18nProviderProps {
  children: ReactNode;
  locale: string;
  translations: { [namespace: string]: Translation };
}

export function I18nProvider({ children, locale, translations }: I18nProviderProps) {
  const [currentLocale, setCurrentLocale] = useState(locale);
  const [currentTranslations, setCurrentTranslations] = useState(translations);
  const router = useRouter();
  const pathname = usePathname();

  const t = (key: string, namespace: string = 'common', options?: { returnObjects?: boolean }): any => {
    const keys = key.split('.');
    let value: any = currentTranslations[namespace];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${namespace}.${key}`);
        return options?.returnObjects ? [] : key;
      }
    }

    if (options?.returnObjects) {
      return value;
    }

    return typeof value === 'string' ? value : key;
  };

  const changeLanguage = async (newLocale: string) => {
    if (newLocale === currentLocale) return;

    try {
      // Save to localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('locale', newLocale);
      }

      // Load new translations
      const namespaces = Object.keys(currentTranslations);
      const newTranslations = await loadTranslations(newLocale, namespaces);

      setCurrentLocale(newLocale);
      setCurrentTranslations(newTranslations);
    } catch (error) {
      console.error('Failed to change language:', error);
      // Revert to previous locale if loading fails
      if (typeof window !== 'undefined') {
        localStorage.setItem('locale', currentLocale);
      }
    }
  };

  useEffect(() => {
    setCurrentLocale(locale);
    setCurrentTranslations(translations);
  }, [locale, translations]);

  return (
    <I18nContext.Provider value={{
      locale: currentLocale,
      t,
      changeLanguage,
      translations: currentTranslations
    }}>
      {children}
    </I18nContext.Provider>
  );
}

export function useI18n() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
}

// Helper function to load translations
export async function loadTranslations(locale: string, namespaces: string[]) {
  const translations: { [namespace: string]: Translation } = {};
  
  for (const namespace of namespaces) {
    try {
      const translation = await import(`../locales/${locale}/${namespace}.json`);
      translations[namespace] = translation.default || translation;
    } catch (error) {
      console.warn(`Failed to load translation: ${locale}/${namespace}.json`);
      // Fallback to English if available
      if (locale !== 'en') {
        try {
          const fallback = await import(`../locales/en/${namespace}.json`);
          translations[namespace] = fallback.default || fallback;
        } catch (fallbackError) {
          console.warn(`Failed to load fallback translation: en/${namespace}.json`);
          translations[namespace] = {};
        }
      } else {
        translations[namespace] = {};
      }
    }
  }
  
  return translations;
}
