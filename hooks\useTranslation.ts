import { useI18n } from '@/lib/i18n-provider';

export interface TranslationHook {
  t: (key: string, options?: { returnObjects?: boolean }) => any;
  locale: string;
  ready: boolean;
}

/**
 * Custom hook for translations with enhanced functionality
 * @param namespace - Translation namespace (file name without extension)
 */
export function useTranslation(namespace: string = 'common'): TranslationHook {
  const { t: translate, locale } = useI18n();

  const t = (key: string, options?: { returnObjects?: boolean }) => translate(key, namespace, options);

  return {
    t,
    locale,
    ready: true,
  };
}

/**
 * Hook for getting current locale information
 */
export function useLocale() {
  const { locale, changeLanguage } = useI18n();

  return {
    locale,
    locales: ['en', 'ja'],
    defaultLocale: 'en',
    changeLanguage,
    isRTL: false, // Add RTL support later if needed
  };
}

/**
 * Hook for component-specific translations
 * Automatically uses the component name as namespace
 */
export function useComponentTranslation(componentName: string) {
  return useTranslation(componentName.toLowerCase());
}

/**
 * Hook for page-specific translations
 * Automatically uses the page name as namespace
 */
export function usePageTranslation(pageName: string) {
  return useTranslation(`${pageName.toLowerCase()}-page`);
}
