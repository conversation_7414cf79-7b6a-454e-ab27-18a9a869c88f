(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3219],{2401:function(e,t,n){var o={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function r(e){if(!n.o(o,e))return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=o[e],r=t[0];return n.e(t[1]).then(function(){return n.t(r,19)})}r.keys=function(){return Object.keys(o)},r.id=2401,e.exports=r},1539:function(e,t,n){var o={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function r(e){if(!n.o(o,e))return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=o[e],r=t[0];return n.e(t[1]).then(function(){return n.t(r,19)})}r.keys=function(){return Object.keys(o)},r.id=1539,e.exports=r},5531:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var o=n(2265);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=(...e)=>e.filter((e,t,n)=>!!e&&n.indexOf(e)===t).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:l="",children:c,iconNode:i,...d},u)=>(0,o.createElement)("svg",{ref:u,...s,width:t,height:t,stroke:e,strokeWidth:r?24*Number(n)/Number(t):n,className:a("lucide",l),...d},[...i.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let n=(0,o.forwardRef)(({className:n,...s},c)=>(0,o.createElement)(l,{ref:c,iconNode:t,className:a(`lucide-${r(e)}`,n),...s}));return n.displayName=`${e}`,n}},6224:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5817:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9670:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6637:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},5099:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},1841:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},1541:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},2549:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});var o=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o.Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7246:function(e,t,n){Promise.resolve().then(n.bind(n,5921))},5921:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return j}});var o=n(7437),r=n(2265),a=n(6637),s=n(9670),l=n(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),i=(0,l.Z)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),d=(0,l.Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var u=n(1841),g=n(5099),m=n(1541),p=n(6224),y=n(5817);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,l.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var f=n(2549),x=n(4346);function j(){let{t:e}=(0,x.$G)("markdown-editor"),t=()=>{let t=e("defaultContent.features.list",{returnObjects:!0}),n=Array.isArray(t)?t:[],o=e("defaultContent.gettingStarted.lists.unordered.items",{returnObjects:!0}),r=Array.isArray(o)?o:[],a=e("defaultContent.gettingStarted.lists.ordered.items",{returnObjects:!0}),s=Array.isArray(a)?a:[],l=e("defaultContent.gettingStarted.tables.headers",{returnObjects:!0}),c=Array.isArray(l)?l:[],i=e("defaultContent.gettingStarted.tables.rows",{returnObjects:!0}),d=Array.isArray(i)?i:[];return"".concat(e("defaultContent.title"),"\n\n").concat(e("defaultContent.subtitle"),"\n\n").concat(e("defaultContent.features.title"),"\n\n").concat(n.map(e=>e).join("\n"),"\n\n").concat(e("defaultContent.gettingStarted.title"),"\n\n").concat(e("defaultContent.gettingStarted.basicFormatting.title"),"\n\n").concat(e("defaultContent.gettingStarted.basicFormatting.description"),"\n\n").concat(e("defaultContent.gettingStarted.images.title"),"\n\n").concat(e("defaultContent.gettingStarted.images.description"),"\n\n![Sample Image](https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=600)\n\n").concat(e("defaultContent.gettingStarted.lists.title"),"\n\n").concat(e("defaultContent.gettingStarted.lists.unordered.title"),"\n").concat(r.join("\n"),"\n\n").concat(e("defaultContent.gettingStarted.lists.ordered.title"),"\n").concat(s.join("\n"),"\n\n").concat(e("defaultContent.gettingStarted.code.title"),"\n\n").concat(e("defaultContent.gettingStarted.code.inline"),"\n\n").concat(e("defaultContent.gettingStarted.code.block"),'\n```javascript\nfunction greetUser(name) {\n  return `Hello, ${name}! Welcome to our free online Markdown editor.`;\n}\n\nconsole.log(greetUser("Developer"));\n```\n\n').concat(e("defaultContent.gettingStarted.tables.title"),"\n\n| ").concat(c.join(" | ")," |\n|").concat(c.map(()=>"---------").join("|"),"|\n").concat(d.map(e=>"| ".concat(e.join(" | ")," |")).join("\n"),"\n\n").concat(e("defaultContent.gettingStarted.links.title"),"\n\n").concat(e("defaultContent.gettingStarted.links.example"),"\n\n").concat(e("defaultContent.gettingStarted.blockquotes.title"),"\n\n").concat(e("defaultContent.gettingStarted.blockquotes.quote"),"\n\n").concat(e("defaultContent.gettingStarted.horizontalRule.title"),"\n\n").concat(e("defaultContent.gettingStarted.horizontalRule.example"),"\n\n").concat(e("defaultContent.math.title"),"\n\nYou can write mathematical expressions using LaTeX syntax:\n\nInline math: $E = mc^2$\n\nBlock math:\n$$\n\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\cdots + x_n\n$$\n\n---\n\n**Start editing above to see the live preview in action!** \uD83D\uDE80")},[n,l]=(0,r.useState)(""),[j,b]=(0,r.useState)(""),[v,k]=(0,r.useState)(!1),[w,N]=(0,r.useState)(!1),[C,S]=(0,r.useState)(!0),[$,M]=(0,r.useState)(!1),[O,_]=(0,r.useState)([]),[Z,E]=(0,r.useState)(!1),R=(0,r.useRef)(null),T=(0,r.useRef)(null),L=(0,r.useRef)(null),U=(0,r.useRef)(null);(0,r.useEffect)(()=>{n||l(t())},[]);let I=e=>{if(!e)return"";let t=e.replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/!\[([^\]]*)\]\(([^)]+)\)/gim,'<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg shadow-sm my-4" />').replace(/\*\*\*(.*?)\*\*\*/gim,"<strong><em>$1</em></strong>").replace(/\*\*(.*?)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*?)\*/gim,"<em>$1</em>").replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>').replace(/```(\w+)?\n([\s\S]*?)```/gim,'<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-4"><code class="language-$1 text-sm">$2</code></pre>').replace(/`([^`]+)`/gim,'<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">$1</code>').replace(/^\|(.+)\|$/gim,(e,t)=>{let n=t.split("|").map(e=>e.trim()).filter(e=>e);return"<tr>"+n.map(e=>'<td class="border border-gray-300 dark:border-gray-600 px-3 py-2">'.concat(e,"</td>")).join("")+"</tr>"}).replace(/^\- (.*$)/gim,'<li class="ml-4">$1</li>').replace(/^\d+\. (.*$)/gim,'<li class="ml-4">$1</li>').replace(/^> (.*$)/gim,'<blockquote class="border-l-4 border-blue-500 pl-4 italic my-4 text-gray-600 dark:text-gray-400">$1</blockquote>').replace(/^---$/gim,'<hr class="my-6 border-gray-300 dark:border-gray-600">').replace(/\n/gim,"<br>");return(t=t.replace(RegExp('(<li class="ml-4">.*<\\/li>)',"s"),'<ul class="list-disc list-inside my-4">$1</ul>')).includes("<tr>")&&(t=t.replace(RegExp("(<tr>.*<\\/tr>)","s"),'<table class="w-full border-collapse my-4">$1</table>')),t};(0,r.useEffect)(()=>{let e=I(n);b(e)},[n]),(0,r.useEffect)(()=>{let e=setTimeout(()=>{localStorage.setItem("markdown-content",n),localStorage.setItem("uploaded-images",JSON.stringify(O))},1e3);return()=>clearTimeout(e)},[n,O]),(0,r.useEffect)(()=>{let e=localStorage.getItem("markdown-content"),t=localStorage.getItem("uploaded-images");if(e&&l(e),t)try{_(JSON.parse(t))}catch(e){console.error("Failed to parse saved images:",e)}},[]);let F=e=>{if(!R.current||!T.current)return;let t="editor"===e?R.current:T.current,n="editor"===e?T.current:R.current,o=t.scrollTop/(t.scrollHeight-t.clientHeight),r=o*(n.scrollHeight-n.clientHeight);n.scrollTop=r},z=e=>{let t="![".concat(e.name,"](").concat(e.url,")"),o=R.current;if(o){let e=o.selectionStart,r=o.selectionEnd,a=n.substring(0,e)+t+n.substring(r);l(a),setTimeout(()=>{o.focus(),o.setSelectionRange(e+t.length,e+t.length)},0)}},A=e=>{_(t=>t.filter(t=>t.id!==e))},q=e=>{if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]},D=async(e,t)=>{try{await navigator.clipboard.writeText(e),console.log("".concat(t," copied to clipboard"))}catch(e){console.error("Failed to copy to clipboard:",e)}};return(0,o.jsxs)("div",{className:"".concat(v?"fixed inset-0 z-50":"min-h-screen"," ").concat(w?"dark bg-gray-900":"bg-gray-50"),children:[(0,o.jsx)("div",{className:"".concat(w?"bg-gray-800 border-gray-700":"bg-white border-gray-200"," border-b px-4 py-3"),children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(a.Z,{className:"h-6 w-6 ".concat(w?"text-blue-400":"text-blue-600")}),(0,o.jsx)("h1",{className:"text-lg font-semibold ".concat(w?"text-white":"text-gray-900"),children:e("title")})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("button",{onClick:()=>S(!C),className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("tooltips.togglePreview"),children:(0,o.jsx)(s.Z,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>E(!Z),className:"p-2 rounded-lg transition-colors ".concat(Z?"bg-blue-600 text-white":w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("tooltips.togglePreview"),children:(0,o.jsx)(c,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>N(!w),className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:w?e("buttons.lightMode"):e("buttons.darkMode"),children:w?(0,o.jsx)(i,{className:"h-4 w-4"}):(0,o.jsx)(d,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>k(!v),className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("tooltips.fullscreen"),children:v?(0,o.jsx)(u.Z,{className:"h-4 w-4"}):(0,o.jsx)(g.Z,{className:"h-4 w-4"})})]})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("input",{ref:L,type:"file",accept:".md,.markdown",onChange:e=>{var t;let n=null===(t=e.target.files)||void 0===t?void 0:t[0];if(n&&("text/markdown"===n.type||n.name.endsWith(".md"))){let e=new FileReader;e.onload=e=>{var t;let n=null===(t=e.target)||void 0===t?void 0:t.result;l(n)},e.readAsText(n)}},className:"hidden"}),(0,o.jsx)("input",{ref:U,type:"file",accept:"image/*",multiple:!0,onChange:e=>{let t=e.target.files;t&&(Array.from(t).forEach(e=>{if(e.type.startsWith("image/")){let t=new FileReader;t.onload=t=>{var n;let o=null===(n=t.target)||void 0===n?void 0:n.result,r={id:Date.now().toString()+Math.random().toString(36).substr(2,9),name:e.name,url:o,size:e.size};_(e=>[...e,r])},t.readAsDataURL(e)}}),e.target&&(e.target.value=""))},className:"hidden"}),(0,o.jsx)("button",{onClick:()=>{var e;return null===(e=L.current)||void 0===e?void 0:e.click()},className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("tooltips.uploadFile"),children:(0,o.jsx)(m.Z,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>{var e;return null===(e=U.current)||void 0===e?void 0:e.click()},className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("buttons.addImage"),children:(0,o.jsx)(c,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>D(j,"html"),className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("tooltips.copyToClipboard"),children:(0,o.jsx)(p.Z,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>{let e='<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>Markdown Document</title>\n    <style>\n        body { font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }\n        pre { background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto; }\n        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }\n        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }\n        table { border-collapse: collapse; width: 100%; }\n        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n        th { background-color: #f2f2f2; }\n        img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin: 16px 0; }\n    </style>\n</head>\n<body>\n'.concat(j,"\n</body>\n</html>"),t=new Blob([e],{type:"text/html"}),n=URL.createObjectURL(t),o=document.createElement("a");o.href=n,o.download="document.html",o.click(),URL.revokeObjectURL(n)},className:"p-2 rounded-lg transition-colors ".concat(w?"hover:bg-gray-700 text-gray-300":"hover:bg-gray-100 text-gray-600"),title:e("tooltips.downloadHtml"),children:(0,o.jsx)(y.Z,{className:"h-4 w-4"})}),(0,o.jsx)("button",{onClick:()=>{let e=new Blob([n],{type:"text/markdown"}),t=URL.createObjectURL(e),o=document.createElement("a");o.href=t,o.download="document.md",o.click(),URL.revokeObjectURL(t)},className:"px-3 py-2 rounded-lg transition-colors ".concat("bg-blue-600 hover:bg-blue-700 text-white"),title:e("tooltips.downloadMarkdown"),children:(0,o.jsx)(a.Z,{className:"h-4 w-4"})})]})]})}),(0,o.jsxs)("div",{className:"flex",style:{height:"calc(100vh - 73px)"},children:[Z&&(0,o.jsxs)("div",{className:"w-64 ".concat(w?"bg-gray-800 border-gray-700":"bg-white border-gray-200"," border-r flex flex-col"),children:[(0,o.jsxs)("div",{className:"".concat(w?"bg-gray-700 border-gray-600":"bg-gray-100 border-gray-200"," border-b px-4 py-2 flex items-center justify-between"),children:[(0,o.jsxs)("span",{className:"text-sm font-medium ".concat(w?"text-gray-300":"text-gray-700"),children:["Images (",O.length,")"]}),(0,o.jsx)("button",{onClick:()=>{var e;return null===(e=U.current)||void 0===e?void 0:e.click()},className:"p-1 rounded transition-colors ".concat(w?"hover:bg-gray-600 text-gray-400":"hover:bg-gray-200 text-gray-600"),title:e("buttons.addImage"),children:(0,o.jsx)(h,{className:"h-4 w-4"})})]}),(0,o.jsx)("div",{className:"flex-1 overflow-y-auto p-2",children:0===O.length?(0,o.jsxs)("div",{className:"text-center py-8 ".concat(w?"text-gray-400":"text-gray-500"),children:[(0,o.jsx)(c,{className:"h-12 w-12 mx-auto mb-2 opacity-50"}),(0,o.jsx)("p",{className:"text-sm",children:e("panels.noImages")||"No images uploaded"}),(0,o.jsx)("button",{onClick:()=>{var e;return null===(e=U.current)||void 0===e?void 0:e.click()},className:"text-xs mt-2 px-3 py-1 rounded transition-colors ".concat("bg-blue-600 hover:bg-blue-700 text-white"),children:e("buttons.upload")})]}):(0,o.jsx)("div",{className:"space-y-2",children:O.map(e=>(0,o.jsxs)("div",{className:"group relative rounded-lg overflow-hidden border ".concat(w?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"," transition-colors cursor-pointer"),onClick:()=>z(e),children:[(0,o.jsx)("img",{src:e.url,alt:e.name,className:"w-full h-24 object-cover"}),(0,o.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center",children:(0,o.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:(0,o.jsx)(h,{className:"h-6 w-6 text-white"})})}),(0,o.jsx)("button",{onClick:t=>{t.stopPropagation(),A(e.id)},className:"absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600",children:(0,o.jsx)(f.Z,{className:"h-3 w-3"})}),(0,o.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 ".concat(w?"bg-gray-900":"bg-white"," bg-opacity-90 p-2"),children:[(0,o.jsx)("p",{className:"text-xs truncate ".concat(w?"text-gray-300":"text-gray-700"),children:e.name}),(0,o.jsx)("p",{className:"text-xs ".concat(w?"text-gray-400":"text-gray-500"),children:q(e.size)})]})]},e.id))})})]}),(0,o.jsxs)("div",{className:"".concat(C?"w-1/2":"w-full"," flex flex-col"),children:[(0,o.jsxs)("div",{className:"".concat(w?"bg-gray-800 border-gray-700":"bg-gray-100 border-gray-200"," border-b px-4 py-2 flex items-center justify-between"),children:[(0,o.jsx)("span",{className:"text-sm font-medium ".concat(w?"text-gray-300":"text-gray-700"),children:e("panels.editor")}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("button",{onClick:()=>{l("")},className:"text-xs px-2 py-1 rounded transition-colors ".concat(w?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-200 text-gray-600"),children:e("buttons.clear")||"Clear"}),(0,o.jsx)("button",{onClick:()=>{l(t())},className:"text-xs px-2 py-1 rounded transition-colors ".concat(w?"hover:bg-gray-700 text-gray-400":"hover:bg-gray-200 text-gray-600"),children:"Reset"})]})]}),(0,o.jsx)("textarea",{ref:R,value:n,onChange:e=>l(e.target.value),onScroll:()=>F("editor"),className:"flex-1 p-4 font-mono text-sm resize-none focus:outline-none ".concat(w?"bg-gray-900 text-gray-100 placeholder-gray-500":"bg-white text-gray-900 placeholder-gray-400"),placeholder:e("placeholders.startTyping"),spellCheck:!1})]}),C&&(0,o.jsxs)("div",{className:"".concat("w-1/2"," flex flex-col border-l border-gray-200"),children:[(0,o.jsx)("div",{className:"".concat(w?"bg-gray-800 border-gray-700":"bg-gray-100 border-gray-200"," border-b px-4 py-2"),children:(0,o.jsx)("span",{className:"text-sm font-medium ".concat(w?"text-gray-300":"text-gray-700"),children:e("panels.preview")})}),(0,o.jsx)("div",{ref:T,onScroll:()=>F("preview"),className:"flex-1 p-4 overflow-auto prose max-w-none ".concat(w?"bg-gray-900 prose-invert":"bg-white"),dangerouslySetInnerHTML:{__html:j}})]})]})]})}},4346:function(e,t,n){"use strict";n.d(t,{$G:function(){return r},bU:function(){return a}});var o=n(3275);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:t,locale:n}=(0,o.QT)();return{t:(n,o)=>t(n,e,o),locale:n,ready:!0}}function a(){let{locale:e,changeLanguage:t}=(0,o.QT)();return{locale:e,locales:["en","ja"],defaultLocale:"en",changeLanguage:t,isRTL:!1}}},3275:function(e,t,n){"use strict";n.d(t,{QT:function(){return c},XJ:function(){return i},bd:function(){return l}});var o=n(7437),r=n(2265),a=n(4033);let s=(0,r.createContext)(void 0);function l(e){let{children:t,locale:n,translations:l}=e,[c,d]=(0,r.useState)(n),[u,g]=(0,r.useState)(l);(0,a.useRouter)(),(0,a.usePathname)();let m=async e=>{if(e!==c)try{localStorage.setItem("locale",e);let t=Object.keys(u),n=await i(e,t);d(e),g(n)}catch(e){console.error("Failed to change language:",e),localStorage.setItem("locale",c)}};return(0,r.useEffect)(()=>{d(n),g(l)},[n,l]),(0,o.jsx)(s.Provider,{value:{locale:c,t:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",n=arguments.length>2?arguments[2]:void 0,o=e.split("."),r=u[t];for(let a of o){if(!r||"object"!=typeof r||!(a in r))return console.warn("Translation key not found: ".concat(t,".").concat(e)),(null==n?void 0:n.returnObjects)?[]:e;r=r[a]}return(null==n?void 0:n.returnObjects)?r:"string"==typeof r?r:e},changeLanguage:m,translations:u},children:t})}function c(){let e=(0,r.useContext)(s);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}async function i(e,t){let o={};for(let r of t)try{let t=await n(1539)("./".concat(e,"/").concat(r,".json"));o[r]=t.default||t}catch(t){if(console.warn("Failed to load translation: ".concat(e,"/").concat(r,".json")),"en"!==e)try{let e=await n(2401)("./".concat(r,".json"));o[r]=e.default||e}catch(e){console.warn("Failed to load fallback translation: en/".concat(r,".json")),o[r]={}}else o[r]={}}return o}},622:function(e,t,n){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o=n(2265),r=Symbol.for("react.element"),a=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,l=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function i(e,t,n){var o,a={},i=null,d=null;for(o in void 0!==n&&(i=""+n),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(d=t.ref),t)s.call(t,o)&&!c.hasOwnProperty(o)&&(a[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===a[o]&&(a[o]=t[o]);return{$$typeof:r,type:e,key:i,ref:d,props:a,_owner:l.current}}t.Fragment=a,t.jsx=i,t.jsxs=i},7437:function(e,t,n){"use strict";e.exports=n(622)},4033:function(e,t,n){e.exports=n(290)}},function(e){e.O(0,[2971,7864,1744],function(){return e(e.s=7246)}),_N_E=e.O()}]);