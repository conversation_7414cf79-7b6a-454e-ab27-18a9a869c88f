(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6627],{5531:function(e,n,t){"use strict";t.d(n,{Z:function(){return l}});var r=t(2265);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,n,t)=>!!e&&t.indexOf(e)===n).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.forwardRef)(({color:e="currentColor",size:n=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:s="",children:l,iconNode:c,...d},m)=>(0,r.createElement)("svg",{ref:m,...i,width:n,height:n,stroke:e,strokeWidth:a?24*Number(t)/Number(n):t,className:o("lucide",s),...d},[...c.map(([e,n])=>(0,r.createElement)(e,n)),...Array.isArray(l)?l:[l]])),l=(e,n)=>{let t=(0,r.forwardRef)(({className:t,...i},l)=>(0,r.createElement)(s,{ref:l,iconNode:n,className:o(`lucide-${a(e)}`,t),...i}));return t.displayName=`${e}`,t}},8063:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},4530:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6224:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5817:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9670:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},6637:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},5099:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},1841:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},9804:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},4280:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9409:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7346:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]])},1541:function(e,n,t){"use strict";t.d(n,{Z:function(){return a}});var r=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},3482:function(e,n,t){Promise.resolve().then(t.bind(t,685))},685:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return y}});var r=t(7437),a=t(2265),o=t(6637),i=t(9670),s=t(9409),l=t(1841),c=t(5099),d=t(4280),m=t(5817),u=t(4530),p=t(8063),g=t(9804),h=t(7346),f=t(1541),x=t(6224);function y(){let[e,n]=(0,a.useState)('# Markdown to Word Converter\n\nWelcome to our **free online Markdown to Word converter**! This powerful tool transforms your Markdown documents into professional Microsoft Word (.docx) files with advanced formatting and styling options.\n\n## Key Features\n\n### ✅ Professional Word Output\n- High-quality DOCX generation compatible with Microsoft Word\n- Multiple page size options (A4, Letter, Legal)\n- Portrait and landscape orientation support\n- Customizable margins and spacing\n- Professional typography and formatting\n\n### ✅ Advanced Document Formatting\n- **Multiple font families**: Calibri, Times New Roman, Arial, Georgia\n- **Typography control**: Font size, line spacing, paragraph spacing\n- **Document themes**: Default, Professional, Academic, Modern\n- **Page elements**: Headers, footers, and page numbers\n- **Table of contents**: Automatic generation from headings\n\n### ✅ Complete Markdown Support\n- All standard Markdown syntax elements\n- Tables with professional Word formatting\n- Code blocks with syntax highlighting\n- Images and media embedding\n- Mathematical expressions support\n- Lists, links, and emphasis formatting\n\n## How to Use This Converter\n\n1. **Input your Markdown**: Paste or type your content in the editor panel\n2. **Customize settings**: Choose page size, font, theme, and formatting options\n3. **Preview document**: See how your Word document will look before downloading\n4. **Generate DOCX**: Create and download your professional Word document\n\n## Supported Markdown Elements\n\n### Text Formatting\nMake your text stand out with various formatting options:\n- **Bold text** for emphasis\n- *Italic text* for subtle emphasis\n- ~~Strikethrough text~~ for deletions\n- `Inline code` for technical terms\n- [Hyperlinks](https://example.com) for references\n\n### Document Structure\n#### Headings\nUse headings to organize your document structure:\n- # Main Title (Heading 1)\n- ## Section Title (Heading 2)\n- ### Subsection Title (Heading 3)\n\n#### Lists\nCreate organized lists for better readability:\n\n**Unordered Lists:**\n- Feature 1: Real-time conversion\n- Feature 2: Professional formatting\n  - Sub-feature: Custom themes\n  - Sub-feature: Multiple fonts\n- Feature 3: Easy export\n\n**Ordered Lists:**\n1. Open the converter\n2. Paste your Markdown content\n3. Customize document settings\n4. Generate and download DOCX\n\n### Code Examples\nDisplay code with proper formatting:\n\n```javascript\nfunction convertToWord() {\n  console.log("Converting Markdown to Word...");\n  return {\n    status: "success",\n    format: "docx",\n    quality: "professional"\n  };\n}\n\n// Call the function\nconvertToWord();\n```\n\n### Tables\nCreate professional tables that translate perfectly to Word:\n\n| Feature | Free Version | Premium |\n|---------|-------------|---------|\n| Basic Conversion | ✅ | ✅ |\n| Custom Themes | ✅ | ✅ |\n| Advanced Settings | ✅ | ✅ |\n| Batch Processing | ❌ | ✅ |\n| Priority Support | ❌ | ✅ |\n\n### Blockquotes\nAdd emphasis with professional blockquotes:\n\n> "The best way to predict the future is to create it. This converter helps you create professional documents that stand out."\n> \n> — Document Creation Expert\n\n### Mathematical Expressions\nInclude mathematical formulas and equations:\n\nInline math: $E = mc^2$\n\nBlock math:\n$$\n\\sum_{i=1}^{n} x_i = \\frac{1}{n} \\sum_{i=1}^{n} x_i\n$$\n\n---\n\n## Why Choose Our Markdown to Word Converter?\n\n### \uD83D\uDE80 Fast and Reliable\n- **Instant conversion**: No waiting time for document generation\n- **Large document support**: Handles documents of any size efficiently\n- **Consistent output**: Reliable formatting every time\n- **Error handling**: Robust processing with helpful error messages\n\n### \uD83D\uDD12 Privacy and Security\n- **Local processing**: All conversion happens in your browser\n- **No data transmission**: Your documents never leave your device\n- **Complete privacy**: No registration or personal information required\n- **Secure environment**: Safe and protected document handling\n\n### \uD83D\uDCB0 Completely Free\n- **No registration**: Start converting immediately\n- **No watermarks**: Clean, professional output\n- **Unlimited conversions**: Convert as many documents as needed\n- **Full feature access**: All formatting options available for free\n\n### \uD83C\uDFA8 Professional Results\n- **Publication-ready**: Documents suitable for business and academic use\n- **Consistent formatting**: Professional appearance across all platforms\n- **Word compatibility**: Perfect integration with Microsoft Word\n- **Print optimization**: Optimized layouts for both screen and print\n\n### \uD83D\uDCCA Advanced Features\n- **Custom styling**: Multiple themes and formatting options\n- **Document structure**: Automatic table of contents generation\n- **Typography control**: Professional font and spacing options\n- **Image support**: Seamless image integration and formatting\n\n---\n\n## Document Formatting Options\n\n### Page Layout\n- **Page sizes**: A4, Letter, Legal formats\n- **Orientation**: Portrait or landscape modes\n- **Margins**: Narrow, normal, or wide margin settings\n- **Headers/Footers**: Custom header and footer text\n\n### Typography\n- **Font families**: Calibri, Times New Roman, Arial, Georgia\n- **Font sizes**: Small (11pt), Medium (12pt), Large (14pt)\n- **Line spacing**: Single, 1.5x, or double spacing\n- **Paragraph spacing**: Customizable paragraph separation\n\n### Document Themes\n- **Default**: Clean, modern formatting\n- **Professional**: Business-appropriate styling\n- **Academic**: Scholarly document formatting\n- **Modern**: Contemporary design elements\n\n---\n\n## Best Practices for Conversion\n\n### 1. Document Structure\n- Use proper heading hierarchy (H1 → H2 → H3)\n- Include a clear document title\n- Organize content with logical sections\n\n### 2. Formatting Consistency\n- Use consistent formatting throughout\n- Apply emphasis (bold/italic) purposefully\n- Maintain consistent list formatting\n\n### 3. Image Optimization\n- Use high-quality images for best results\n- Include descriptive alt text for accessibility\n- Consider image placement and sizing\n\n### 4. Table Design\n- Keep tables simple and readable\n- Use clear column headers\n- Avoid overly complex table structures\n\n---\n\n**Ready to convert?** Start by editing this sample content or paste your own Markdown text above. Customize the document settings to match your preferences and click "Generate Word Document" to create your professional DOCX file.\n\nTransform your Markdown into beautiful Word documents today! \uD83D\uDCC4✨'),[t,y]=(0,a.useState)(""),[b,w]=(0,a.useState)(!1),[v,k]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!0),[C,S]=(0,a.useState)(!1),[M,T]=(0,a.useState)("idle"),z=(0,a.useRef)(null),$=(0,a.useRef)(null),P=(0,a.useRef)(null),[D,F]=(0,a.useState)({pageSize:"A4",orientation:"portrait",margin:"normal",fontSize:"medium",fontFamily:"calibri",theme:"default",includeTableOfContents:!1,includePageNumbers:!0,headerText:"",footerText:"",lineSpacing:"single",paragraphSpacing:"small"}),Z=e=>{if(!e)return"";let n=e.replace(/^### (.*$)/gim,'<h3 class="word-heading-3">$1</h3>').replace(/^## (.*$)/gim,'<h2 class="word-heading-2">$1</h2>').replace(/^# (.*$)/gim,'<h1 class="word-heading-1">$1</h1>').replace(/!\[([^\]]*)\]\(([^)]+)\)/gim,'<img src="$2" alt="$1" class="word-image" />').replace(/\*\*\*(.*?)\*\*\*/gim,"<strong><em>$1</em></strong>").replace(/\*\*(.*?)\*\*/gim,'<strong class="word-bold">$1</strong>').replace(/\*(.*?)\*/gim,'<em class="word-italic">$1</em>').replace(/~~(.*?)~~/gim,'<del class="word-strikethrough">$1</del>').replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2" class="word-link">$1</a>').replace(/```(\w+)?\n([\s\S]*?)```/gim,'<pre class="word-code-block"><code class="language-$1">$2</code></pre>').replace(/`([^`]+)`/gim,'<code class="word-inline-code">$1</code>').replace(/^\|(.+)\|$/gim,(e,n)=>{let t=n.split("|").map(e=>e.trim()).filter(e=>e);return"<tr>"+t.map(e=>'<td class="word-table-cell">'.concat(e,"</td>")).join("")+"</tr>"}).replace(/^\- (.*$)/gim,'<li class="word-list-item">$1</li>').replace(/^\d+\. (.*$)/gim,'<li class="word-ordered-item">$1</li>').replace(/^> (.*$)/gim,'<blockquote class="word-blockquote">$1</blockquote>').replace(/^---$/gim,'<hr class="word-hr">').replace(/\$\$([\s\S]*?)\$\$/gim,'<div class="word-math-block">$1</div>').replace(/\$([^$]+)\$/gim,'<span class="word-math-inline">$1</span>').replace(/\n/gim,"<br>");return(n=(n=n.replace(RegExp('(<li class="word-list-item">.*<\\/li>)',"s"),'<ul class="word-unordered-list">$1</ul>')).replace(RegExp('(<li class="word-ordered-item">.*<\\/li>)',"s"),'<ol class="word-ordered-list">$1</ol>')).includes("<tr>")&&(n=n.replace(RegExp("(<tr>.*<\\/tr>)","s"),'<table class="word-table">$1</table>')),n},O=e=>{let n=e.match(/<h[1-3][^>]*>([^<]+)<\/h[1-3]>/g)||[];if(0===n.length)return"";let t='<div class="word-toc"><h2 class="word-toc-title">Table of Contents</h2><ul class="word-toc-list">';return n.forEach(e=>{var n;let r=parseInt((null===(n=e.match(/<h([1-3])/))||void 0===n?void 0:n[1])||"1"),a=e.replace(/<[^>]*>/g,"");t+='<li class="word-toc-item" '.concat(r>1?'style="margin-left: '.concat((r-1)*20,'px;"'):"",'><a href="#').concat(a,'" class="word-toc-link">').concat(a,"</a></li>")}),t+="</ul></div>"},L=()=>{let e="\n      @page {\n        size: ".concat(D.pageSize," ").concat(D.orientation,";\n        margin: ").concat("narrow"===D.margin?"0.5in":"wide"===D.margin?"1.5in":"1in",";\n      }\n      \n      body {\n        font-family: ").concat({calibri:"Calibri, sans-serif",times:"Times New Roman, serif",arial:"Arial, sans-serif",georgia:"Georgia, serif"}[D.fontFamily],";\n        font-size: ").concat({small:"11pt",medium:"12pt",large:"14pt"}[D.fontSize],";\n        line-height: ").concat({single:"1.0","1.5":"1.5",double:"2.0"}[D.lineSpacing],";\n        color: #000000;\n        margin: 0;\n        padding: 20px;\n        background: white;\n      }\n      \n      .word-heading-1 {\n        font-size: 18pt;\n        font-weight: bold;\n        color: #2c3e50;\n        margin-top: 24pt;\n        margin-bottom: 12pt;\n        page-break-after: avoid;\n      }\n      \n      .word-heading-2 {\n        font-size: 16pt;\n        font-weight: bold;\n        color: #34495e;\n        margin-top: 18pt;\n        margin-bottom: 6pt;\n        page-break-after: avoid;\n      }\n      \n      .word-heading-3 {\n        font-size: 14pt;\n        font-weight: bold;\n        color: #34495e;\n        margin-top: 12pt;\n        margin-bottom: 6pt;\n        page-break-after: avoid;\n      }\n      \n      .word-bold {\n        font-weight: bold;\n      }\n      \n      .word-italic {\n        font-style: italic;\n      }\n      \n      .word-strikethrough {\n        text-decoration: line-through;\n      }\n      \n      .word-link {\n        color: #0066cc;\n        text-decoration: underline;\n      }\n      \n      .word-image {\n        max-width: 100%;\n        height: auto;\n        display: block;\n        margin: 12pt auto;\n        border: 1px solid #ddd;\n      }\n      \n      .word-code-block {\n        background: #f8f9fa;\n        border: 1px solid #e9ecef;\n        border-radius: 4px;\n        padding: 12pt;\n        margin: 12pt 0;\n        font-family: 'Courier New', monospace;\n        font-size: 10pt;\n        overflow-x: auto;\n      }\n      \n      .word-inline-code {\n        background: #f1f3f4;\n        padding: 2pt 4pt;\n        border-radius: 2pt;\n        font-family: 'Courier New', monospace;\n        font-size: 10pt;\n      }\n      \n      .word-table {\n        width: 100%;\n        border-collapse: collapse;\n        margin: 12pt 0;\n        border: 1px solid #000;\n      }\n      \n      .word-table-cell {\n        border: 1px solid #000;\n        padding: 6pt 12pt;\n        text-align: left;\n        vertical-align: top;\n      }\n      \n      .word-table tr:first-child .word-table-cell {\n        background-color: #f8f9fa;\n        font-weight: bold;\n      }\n      \n      .word-unordered-list {\n        margin: 6pt 0;\n        padding-left: 24pt;\n      }\n      \n      .word-ordered-list {\n        margin: 6pt 0;\n        padding-left: 24pt;\n      }\n      \n      .word-list-item {\n        margin: 3pt 0;\n      }\n      \n      .word-ordered-item {\n        margin: 3pt 0;\n      }\n      \n      .word-blockquote {\n        border-left: 4pt solid #3498db;\n        margin: 12pt 0;\n        padding-left: 12pt;\n        color: #666;\n        font-style: italic;\n      }\n      \n      .word-hr {\n        border: none;\n        border-top: 1pt solid #ccc;\n        margin: 18pt 0;\n      }\n      \n      .word-toc {\n        page-break-after: always;\n        margin-bottom: 24pt;\n      }\n      \n      .word-toc-title {\n        font-size: 16pt;\n        font-weight: bold;\n        margin-bottom: 12pt;\n      }\n      \n      .word-toc-list {\n        list-style: none;\n        padding-left: 0;\n      }\n      \n      .word-toc-item {\n        margin: 6pt 0;\n      }\n      \n      .word-toc-link {\n        color: #0066cc;\n        text-decoration: none;\n      }\n      \n      .word-math-block {\n        text-align: center;\n        margin: 12pt 0;\n        font-family: 'Times New Roman', serif;\n        font-size: 14pt;\n      }\n      \n      .word-math-inline {\n        font-family: 'Times New Roman', serif;\n      }\n      \n      p {\n        margin-top: 0;\n        margin-bottom: ").concat("none"===D.paragraphSpacing?"0":"small"===D.paragraphSpacing?"6pt":"12pt",";\n      }\n    ");return e+({default:"",professional:"\n        .word-heading-1, .word-heading-2, .word-heading-3 {\n          color: #1f4e79;\n        }\n        .word-link {\n          color: #1f4e79;\n        }\n      ",academic:"\n        body {\n          font-family: 'Times New Roman', serif;\n        }\n        .word-heading-1, .word-heading-2, .word-heading-3 {\n          color: #000;\n          text-align: center;\n        }\n      ",modern:"\n        .word-heading-1, .word-heading-2, .word-heading-3 {\n          color: #e74c3c;\n        }\n        .word-link {\n          color: #e74c3c;\n        }\n        .word-blockquote {\n          border-left-color: #e74c3c;\n        }\n      "})[D.theme]};(0,a.useEffect)(()=>{let n=Z(e);y(n)},[e]),(0,a.useEffect)(()=>{let n=setTimeout(()=>{localStorage.setItem("word-markdown-content",e),localStorage.setItem("word-settings",JSON.stringify(D))},1e3);return()=>clearTimeout(n)},[e,D]),(0,a.useEffect)(()=>{let e=localStorage.getItem("word-markdown-content"),t=localStorage.getItem("word-settings");if(e&&n(e),t)try{F(JSON.parse(t))}catch(e){console.error("Failed to parse saved settings:",e)}},[]);let W=async()=>{w(!0),T("idle");try{await new Promise(e=>setTimeout(e,2500));let e=t;if(D.includeTableOfContents){let n=O(t);e=n+e}let n='\n        <!DOCTYPE html>\n        <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word">\n        <head>\n          <meta charset="UTF-8">\n          <meta name="ProgId" content="Word.Document">\n          <meta name="Generator" content="Microsoft Word 15">\n          <meta name="Originator" content="Microsoft Word 15">\n          <title>Markdown Document</title>\n          <style>'.concat(L(),"</style>\n        </head>\n        <body>\n          ").concat(D.headerText?'<div style="text-align: center; border-bottom: 1pt solid #ccc; padding-bottom: 6pt; margin-bottom: 12pt;">'.concat(D.headerText,"</div>"):"","\n          ").concat(e,"\n          ").concat(D.footerText?'<div style="text-align: center; border-top: 1pt solid #ccc; padding-top: 6pt; margin-top: 12pt;">'.concat(D.footerText,"</div>"):"","\n        </body>\n        </html>\n      "),r=new Blob([n],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),a=URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download="document.doc",o.click(),URL.revokeObjectURL(a),T("success")}catch(e){console.error("Word document generation failed:",e),T("error")}finally{w(!1)}},A=async()=>{try{await navigator.clipboard.writeText(e),console.log("Markdown copied to clipboard")}catch(e){console.error("Failed to copy to clipboard:",e)}};return(0,r.jsxs)("div",{className:"".concat(C?"fixed inset-0 z-50":"min-h-screen"," bg-gray-50"),children:[(0,r.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"bg-blue-100 p-2 rounded-lg",children:(0,r.jsx)(o.Z,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Markdown to Word Converter"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Convert your Markdown documents to Microsoft Word files"})]})]})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("button",{onClick:()=>N(!j),className:"p-2 rounded-lg transition-colors ".concat(j?"bg-blue-100 text-blue-600":"hover:bg-gray-100 text-gray-600"),title:j?"Hide Preview":"Show Preview",children:(0,r.jsx)(i.Z,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>k(!v),className:"p-2 rounded-lg transition-colors ".concat(v?"bg-blue-100 text-blue-600":"hover:bg-gray-100 text-gray-600"),title:"Word Settings",children:(0,r.jsx)(s.Z,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:()=>S(!C),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600",title:C?"Exit Fullscreen":"Enter Fullscreen",children:C?(0,r.jsx)(l.Z,{className:"h-5 w-5"}):(0,r.jsx)(c.Z,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:W,disabled:b||!e.trim(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:b?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.Z,{className:"h-4 w-4 animate-spin"}),(0,r.jsx)("span",{children:"Generating..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Generate Word"})]})})]})]}),"success"===M&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2",children:[(0,r.jsx)(u.Z,{className:"h-5 w-5 text-green-600"}),(0,r.jsx)("span",{className:"text-green-800",children:"Word document generated successfully!"})]}),"error"===M&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[(0,r.jsx)(p.Z,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("span",{className:"text-red-800",children:"Failed to generate Word document. Please try again."})]})]}),(0,r.jsxs)("div",{className:"flex",style:{height:"calc(100vh - 89px)"},children:[v&&(0,r.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Word Document Settings"}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,r.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Page Layout"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Page Size"}),(0,r.jsxs)("select",{value:D.pageSize,onChange:e=>F(n=>({...n,pageSize:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"A4",children:"A4"}),(0,r.jsx)("option",{value:"Letter",children:"Letter"}),(0,r.jsx)("option",{value:"Legal",children:"Legal"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Orientation"}),(0,r.jsxs)("select",{value:D.orientation,onChange:e=>F(n=>({...n,orientation:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"portrait",children:"Portrait"}),(0,r.jsx)("option",{value:"landscape",children:"Landscape"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Margins"}),(0,r.jsxs)("select",{value:D.margin,onChange:e=>F(n=>({...n,margin:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"narrow",children:"Narrow"}),(0,r.jsx)("option",{value:"normal",children:"Normal"}),(0,r.jsx)("option",{value:"wide",children:"Wide"})]})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,r.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Typography"]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Font Family"}),(0,r.jsxs)("select",{value:D.fontFamily,onChange:e=>F(n=>({...n,fontFamily:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"calibri",children:"Calibri"}),(0,r.jsx)("option",{value:"times",children:"Times New Roman"}),(0,r.jsx)("option",{value:"arial",children:"Arial"}),(0,r.jsx)("option",{value:"georgia",children:"Georgia"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Font Size"}),(0,r.jsxs)("select",{value:D.fontSize,onChange:e=>F(n=>({...n,fontSize:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"small",children:"Small (11pt)"}),(0,r.jsx)("option",{value:"medium",children:"Medium (12pt)"}),(0,r.jsx)("option",{value:"large",children:"Large (14pt)"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Line Spacing"}),(0,r.jsxs)("select",{value:D.lineSpacing,onChange:e=>F(n=>({...n,lineSpacing:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"single",children:"Single"}),(0,r.jsx)("option",{value:"1.5",children:"1.5 Lines"}),(0,r.jsx)("option",{value:"double",children:"Double"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Paragraph Spacing"}),(0,r.jsxs)("select",{value:D.paragraphSpacing,onChange:e=>F(n=>({...n,paragraphSpacing:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"none",children:"None"}),(0,r.jsx)("option",{value:"small",children:"Small"}),(0,r.jsx)("option",{value:"medium",children:"Medium"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Theme"}),(0,r.jsxs)("select",{value:D.theme,onChange:e=>F(n=>({...n,theme:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,r.jsx)("option",{value:"default",children:"Default"}),(0,r.jsx)("option",{value:"professional",children:"Professional"}),(0,r.jsx)("option",{value:"academic",children:"Academic"}),(0,r.jsx)("option",{value:"modern",children:"Modern"})]})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Document Options"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:D.includeTableOfContents,onChange:e=>F(n=>({...n,includeTableOfContents:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include Table of Contents"})]}),(0,r.jsxs)("label",{className:"flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",checked:D.includePageNumbers,onChange:e=>F(n=>({...n,includePageNumbers:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,r.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include Page Numbers"})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Headers & Footers"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Header Text"}),(0,r.jsx)("input",{type:"text",value:D.headerText,onChange:e=>F(n=>({...n,headerText:e.target.value})),placeholder:"Optional header text",className:"w-full p-2 border border-gray-300 rounded-md text-sm"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Footer Text"}),(0,r.jsx)("input",{type:"text",value:D.footerText,onChange:e=>F(n=>({...n,footerText:e.target.value})),placeholder:"Optional footer text",className:"w-full p-2 border border-gray-300 rounded-md text-sm"})]})]})]})]})}),(0,r.jsxs)("div",{className:"".concat(j?"w-1/2":"w-full"," flex flex-col"),children:[(0,r.jsxs)("div",{className:"bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Markdown Editor"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{ref:P,type:"file",accept:".md,.markdown",onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];if(r&&("text/markdown"===r.type||r.name.endsWith(".md"))){let e=new FileReader;e.onload=e=>{var t;let r=null===(t=e.target)||void 0===t?void 0:t.result;n(r)},e.readAsText(r)}},className:"hidden"}),(0,r.jsx)("button",{onClick:()=>{var e;return null===(e=P.current)||void 0===e?void 0:e.click()},className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Upload Markdown File",children:(0,r.jsx)(f.Z,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:A,className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Copy Markdown",children:(0,r.jsx)(x.Z,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>n(""),className:"text-xs px-2 py-1 hover:bg-gray-200 rounded transition-colors text-gray-600",children:"Clear"})]})]}),(0,r.jsx)("textarea",{ref:z,value:e,onChange:e=>n(e.target.value),className:"flex-1 p-4 font-mono text-sm resize-none focus:outline-none bg-white",placeholder:"Paste your Markdown content here or start typing...",spellCheck:!1})]}),j&&(0,r.jsxs)("div",{className:"".concat("w-1/2"," flex flex-col border-l border-gray-200"),children:[(0,r.jsx)("div",{className:"bg-gray-100 border-b border-gray-200 px-4 py-2",children:(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Word Document Preview"})}),(0,r.jsxs)("div",{ref:$,className:"flex-1 p-4 overflow-auto bg-white",style:{fontFamily:"calibri"===D.fontFamily?"Calibri, sans-serif":"times"===D.fontFamily?"Times New Roman, serif":"arial"===D.fontFamily?"Arial, sans-serif":"Georgia, serif",fontSize:"small"===D.fontSize?"11pt":"large"===D.fontSize?"14pt":"12pt",lineHeight:"single"===D.lineSpacing?"1.0":"double"===D.lineSpacing?"2.0":"1.5"},children:[D.includeTableOfContents&&t&&(0,r.jsx)("div",{className:"mb-8 pb-8 border-b border-gray-200",dangerouslySetInnerHTML:{__html:O(t)}}),(0,r.jsx)("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:t}})]})]})]})]})}},622:function(e,n,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(2265),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,n,t){var r,o={},c=null,d=null;for(r in void 0!==t&&(c=""+t),void 0!==n.key&&(c=""+n.key),void 0!==n.ref&&(d=n.ref),n)i.call(n,r)&&!l.hasOwnProperty(r)&&(o[r]=n[r]);if(e&&e.defaultProps)for(r in n=e.defaultProps)void 0===o[r]&&(o[r]=n[r]);return{$$typeof:a,type:e,key:c,ref:d,props:o,_owner:s.current}}n.Fragment=o,n.jsx=c,n.jsxs=c},7437:function(e,n,t){"use strict";e.exports=t(622)}},function(e){e.O(0,[2971,7864,1744],function(){return e(e.s=3482)}),_N_E=e.O()}]);