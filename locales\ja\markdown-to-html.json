{"title": "Markdown to HTMLコンバーター", "subtitle": "高度なカスタマイズオプションでMarkdownコンテンツをクリーンでプロフェッショナルなHTMLに変換", "buttons": {"htmlSettings": "HTML設定", "enterFullscreen": "フルスクリーン表示", "exitFullscreen": "フルスクリーン終了", "generateHtml": "HTML生成", "generating": "生成中...", "uploadFile": "Markdownファイルアップロード", "copyMarkdown": "Markdownをコピー", "copyHtml": "HTMLをコピー", "clear": "クリア"}, "panels": {"markdownEditor": "Markdownエディター", "htmlPreview": "HTMLプレビュー"}, "placeholders": {"markdownInput": "ここにMarkdownコンテンツを貼り付けるか入力してください...", "customCss": "/* ここにカスタムCSSを追加 */"}, "status": {"success": "HTMLが正常に生成されました！", "error": "HTMLの生成に失敗しました。もう一度お試しください。"}, "settings": {"title": "HTML出力設定", "outputFormat": {"title": "出力形式", "formatType": "形式タイプ", "options": {"fragment": "HTMLフラグメント", "clean": "クリーンHTML", "standalone": "スタンドアロンページ"}, "cssFramework": "CSSフレームワーク", "cssOptions": {"none": "なし", "bootstrap": "Bootstrap", "tailwind": "Tailwind CSS", "bulma": "<PERSON><PERSON><PERSON>"}, "theme": "テーマ", "themeOptions": {"default": "デフォルト", "github": "GitHub", "material": "マテリアルデザイン", "dark": "ダークテーマ", "minimal": "ミニマル"}}, "features": {"title": "機能", "includeCss": "CSSスタイルを含める", "includeJs": "JavaScriptを含める", "syntaxHighlighting": "構文ハイライト", "mathRendering": "数式レンダリング", "responsiveDesign": "レスポンシブデザイン", "accessibilityFeatures": "アクセシビリティ機能", "semanticHtml": "セマンティックHTML", "minifyOutput": "出力を最小化"}, "customCss": "カスタムCSS"}, "previewModes": {"desktop": "デスクトップ表示", "tablet": "タブレット表示", "mobile": "モバイル表示"}}