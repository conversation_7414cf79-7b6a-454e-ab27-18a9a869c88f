(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7618],{2401:function(e,n,t){var a={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function r(e){if(!t.o(a,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=a[e],r=n[0];return t.e(n[1]).then(function(){return t.t(r,19)})}r.keys=function(){return Object.keys(a)},r.id=2401,e.exports=r},1539:function(e,n,t){var a={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function r(e){if(!t.o(a,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=a[e],r=n[0];return t.e(n[1]).then(function(){return t.t(r,19)})}r.keys=function(){return Object.keys(a)},r.id=1539,e.exports=r},5531:function(e,n,t){"use strict";t.d(n,{Z:function(){return l}});var a=t(2265);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,n,t)=>!!e&&t.indexOf(e)===n).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,a.forwardRef)(({color:e="currentColor",size:n=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:i="",children:l,iconNode:c,...d},m)=>(0,a.createElement)("svg",{ref:m,...s,width:n,height:n,stroke:e,strokeWidth:r?24*Number(t)/Number(n):t,className:o("lucide",i),...d},[...c.map(([e,n])=>(0,a.createElement)(e,n)),...Array.isArray(l)?l:[l]])),l=(e,n)=>{let t=(0,a.forwardRef)(({className:t,...s},l)=>(0,a.createElement)(i,{ref:l,iconNode:n,className:o(`lucide-${r(e)}`,t),...s}));return t.displayName=`${e}`,t}},8063:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},4530:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2455:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},6224:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5817:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9670:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5099:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},1841:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},9804:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},4280:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9409:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1271:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},1541:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},2369:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},2125:function(e,n,t){Promise.resolve().then(t.bind(t,8251))},8251:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return w}});var a=t(7437),r=t(2265),o=t(2455),s=t(9670),i=t(9409),l=t(1841),c=t(5099),d=t(4280),m=t(5817),u=t(4530),h=t(8063),p=t(9804),g=t(2369),f=t(1541),x=t(6224),y=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let j=(0,y.Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]),b=(0,y.Z)("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]]);var v=t(1271),k=t(4346);function w(){let{t:e}=(0,k.$G)("markdown-to-html"),[n,t]=(0,r.useState)('# Markdown to HTML Converter\n\nWelcome to our **free online Markdown to HTML converter**! This powerful tool transforms your Markdown documents into clean, semantic HTML with advanced customization options and professional styling.\n\n## Key Features\n\n### ✅ Professional HTML Output\n- Clean, semantic HTML5 markup\n- W3C compliant code generation\n- SEO-optimized structure\n- Accessibility features built-in\n- Cross-browser compatibility\n\n### ✅ Advanced Customization Options\n- **Multiple output formats**: Clean HTML, Standalone page, Fragment\n- **CSS frameworks**: Bootstrap, Tailwind CSS, Bulma integration\n- **Custom themes**: GitHub, Material Design, Dark mode, Minimal\n- **Responsive design**: Mobile-first approach\n- **Syntax highlighting**: Beautiful code block formatting\n\n### ✅ Developer-Friendly Features\n- Minified output for production\n- Custom CSS injection\n- JavaScript integration\n- Mathematical expression rendering\n- Image optimization and lazy loading\n\n## How to Use This Converter\n\n1. **Input your Markdown**: Paste or type your content in the editor panel\n2. **Customize settings**: Choose output format, CSS framework, and styling options\n3. **Preview HTML**: See how your HTML will render in real-time\n4. **Generate & Download**: Create and download your optimized HTML file\n\n## Supported Markdown Elements\n\n### Text Formatting\nTransform your text with various formatting options:\n- **Bold text** for strong emphasis\n- *Italic text* for subtle emphasis\n- ~~Strikethrough text~~ for deletions\n- `Inline code` for technical terms\n- [Hyperlinks](https://example.com) for references\n\n### Document Structure\n#### Headings\nCreate well-structured documents with proper heading hierarchy:\n- # Main Title (H1)\n- ## Section Title (H2)\n- ### Subsection Title (H3)\n- #### Sub-subsection Title (H4)\n\n#### Lists\nOrganize information with clean list structures:\n\n**Unordered Lists:**\n- Feature 1: Real-time HTML conversion\n- Feature 2: Multiple CSS frameworks\n  - Sub-feature: Bootstrap integration\n  - Sub-feature: Tailwind CSS support\n  - Sub-feature: Custom styling options\n- Feature 3: Responsive design output\n\n**Ordered Lists:**\n1. Open the HTML converter\n2. Paste your Markdown content\n3. Select output format and styling\n4. Customize CSS framework settings\n5. Generate and download HTML\n\n### Code Examples\nDisplay code with beautiful syntax highlighting:\n\n```html\n<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>Generated from Markdown</title>\n</head>\n<body>\n    <h1>Hello, World!</h1>\n    <p>This HTML was generated from <strong>Markdown</strong>!</p>\n</body>\n</html>\n```\n\n```css\n/* Custom CSS for enhanced styling */\n.markdown-content {\n    max-width: 800px;\n    margin: 0 auto;\n    padding: 2rem;\n    font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;\n}\n\n.code-block {\n    background: #f8f9fa;\n    border-radius: 8px;\n    padding: 1rem;\n    overflow-x: auto;\n}\n```\n\n```javascript\n// JavaScript for interactive features\ndocument.addEventListener(\'DOMContentLoaded\', function() {\n    // Add smooth scrolling to anchor links\n    const links = document.querySelectorAll(\'a[href^="#"]\');\n    \n    links.forEach(link => {\n        link.addEventListener(\'click\', function(e) {\n            e.preventDefault();\n            const target = document.querySelector(this.getAttribute(\'href\'));\n            if (target) {\n                target.scrollIntoView({ behavior: \'smooth\' });\n            }\n        });\n    });\n});\n```\n\n### Tables\nCreate professional tables with responsive design:\n\n| Feature | Free Version | Pro Version | Enterprise |\n|---------|-------------|-------------|------------|\n| Basic HTML Conversion | ✅ | ✅ | ✅ |\n| CSS Framework Integration | ✅ | ✅ | ✅ |\n| Custom Themes | ✅ | ✅ | ✅ |\n| Responsive Design | ✅ | ✅ | ✅ |\n| Syntax Highlighting | ✅ | ✅ | ✅ |\n| Custom CSS Injection | ❌ | ✅ | ✅ |\n| JavaScript Integration | ❌ | ✅ | ✅ |\n| Batch Processing | ❌ | ❌ | ✅ |\n| API Access | ❌ | ❌ | ✅ |\n\n### Blockquotes\nAdd emphasis with styled blockquotes:\n\n> "The web is more a social creation than a technical one. I designed it for a social effect — to help people work together — and not as a technical toy."\n> \n> — Tim Berners-Lee, Inventor of the World Wide Web\n\n### Mathematical Expressions\nInclude mathematical formulas with MathJax rendering:\n\nInline math: $E = mc^2$\n\nBlock math:\n$$\n\\sum_{i=1}^{n} x_i = \\frac{1}{n} \\sum_{i=1}^{n} x_i\n$$\n\nComplex equation:\n$$\n\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}\n$$\n\n---\n\n## Output Format Options\n\n### 1. Clean HTML Fragment\nPerfect for embedding in existing websites:\n- Pure HTML markup without `<html>`, `<head>`, or `<body>` tags\n- Minimal CSS classes for easy styling\n- Lightweight and fast loading\n\n### 2. Standalone HTML Page\nComplete HTML document ready for deployment:\n- Full HTML5 document structure\n- Embedded CSS and JavaScript\n- Meta tags for SEO optimization\n- Responsive viewport configuration\n\n### 3. HTML with CSS Framework\nProfessional styling with popular frameworks:\n- **Bootstrap**: Mobile-first responsive design\n- **Tailwind CSS**: Utility-first CSS framework\n- **Bulma**: Modern CSS framework based on Flexbox\n\n## CSS Framework Integration\n\n### Bootstrap Integration\n```html\n<!-- Bootstrap CSS -->\n<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n\n<!-- Your Markdown content with Bootstrap classes -->\n<div class="container">\n    <div class="row">\n        <div class="col-md-8 mx-auto">\n            <!-- Generated HTML content -->\n        </div>\n    </div>\n</div>\n```\n\n### Tailwind CSS Integration\n```html\n<!-- Tailwind CSS -->\n<script src="https://cdn.tailwindcss.com"></script>\n\n<!-- Your Markdown content with Tailwind classes -->\n<div class="max-w-4xl mx-auto px-4 py-8">\n    <div class="prose prose-lg">\n        <!-- Generated HTML content -->\n    </div>\n</div>\n```\n\n---\n\n## Why Choose Our Markdown to HTML Converter?\n\n### \uD83D\uDE80 Fast and Reliable\n- **Instant conversion**: Real-time HTML generation\n- **Large document support**: Handles documents of any size\n- **Optimized output**: Clean, efficient HTML code\n- **Error handling**: Robust processing with helpful feedback\n\n### \uD83D\uDD12 Privacy and Security\n- **Local processing**: All conversion happens in your browser\n- **No data transmission**: Your content never leaves your device\n- **Complete privacy**: No registration or tracking\n- **Secure environment**: Safe document handling\n\n### \uD83D\uDCB0 Completely Free\n- **No registration**: Start converting immediately\n- **No limitations**: Unlimited conversions\n- **Full feature access**: All options available for free\n- **No watermarks**: Clean, professional output\n\n### \uD83C\uDFA8 Professional Results\n- **W3C compliant**: Valid HTML5 markup\n- **SEO optimized**: Proper semantic structure\n- **Accessibility ready**: WCAG 2.1 compliant features\n- **Cross-browser compatible**: Works everywhere\n\n### \uD83D\uDCF1 Responsive Design\n- **Mobile-first**: Optimized for all devices\n- **Flexible layouts**: Adapts to any screen size\n- **Touch-friendly**: Perfect for mobile interaction\n- **Print optimized**: Beautiful printed output\n\n---\n\n**Ready to convert?** Start by editing this sample content or paste your own Markdown text above. Customize the HTML output settings to match your needs and click "Generate HTML" to create your optimized web content.\n\nTransform your Markdown into beautiful, semantic HTML today! \uD83C\uDF10✨'),[y,w]=(0,r.useState)(""),[S,C]=(0,r.useState)(!1),[N,M]=(0,r.useState)(!1),[T,L]=(0,r.useState)(!0),[H,F]=(0,r.useState)(!1),[$,O]=(0,r.useState)("idle"),[D,Z]=(0,r.useState)("desktop"),E=(0,r.useRef)(null),R=(0,r.useRef)(null),P=(0,r.useRef)(null),[I,_]=(0,r.useState)({outputFormat:"standalone",cssFramework:"none",theme:"default",includeCSS:!0,includeJS:!1,minifyOutput:!1,semanticHTML:!0,accessibilityFeatures:!0,responsiveDesign:!0,syntaxHighlighting:!0,mathRendering:!0,customCSS:""}),z=e=>{if(!e)return"";let n=e.replace(/^#### (.*$)/gim,'<h4 id="$1">$1</h4>').replace(/^### (.*$)/gim,'<h3 id="$1">$1</h3>').replace(/^## (.*$)/gim,'<h2 id="$1">$1</h2>').replace(/^# (.*$)/gim,'<h1 id="$1">$1</h1>').replace(/!\[([^\]]*)\]\(([^)]+)\)/gim,'<img src="$2" alt="$1" class="responsive-image" loading="lazy" />').replace(/\*\*\*(.*?)\*\*\*/gim,"<strong><em>$1</em></strong>").replace(/\*\*(.*?)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*?)\*/gim,"<em>$1</em>").replace(/~~(.*?)~~/gim,"<del>$1</del>").replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2" rel="noopener noreferrer">$1</a>').replace(/```(\w+)?\n([\s\S]*?)```/gim,'<pre class="code-block"><code class="language-$1">$2</code></pre>').replace(/`([^`]+)`/gim,'<code class="inline-code">$1</code>').replace(/^\|(.+)\|$/gim,(e,n)=>{let t=n.split("|").map(e=>e.trim()).filter(e=>e);return"<tr>"+t.map((e,n)=>{let t=0===n?"th":"td";return"<".concat(t,' class="table-cell">').concat(e,"</").concat(t,">")}).join("")+"</tr>"}).replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/^\d+\. (.*$)/gim,"<li>$1</li>").replace(/^> (.*$)/gim,'<blockquote class="blockquote">$1</blockquote>').replace(/^---$/gim,'<hr class="divider">').replace(/\$\$([\s\S]*?)\$\$/gim,'<div class="math-block">$1</div>').replace(/\$([^$]+)\$/gim,'<span class="math-inline">$1</span>').replace(/\n/gim,"<br>");return(n=n.replace(RegExp("(<li>.*<\\/li>)","s"),'<ul class="list">$1</ul>')).includes("<tr>")&&(n=n.replace(RegExp("(<tr>.*<\\/tr>)","s"),'<table class="responsive-table">$1</table>')),n},A=e=>({default:"/* Default theme styles */",github:"/* GitHub theme styles */",material:"/* Material theme styles */",dark:"/* Dark theme styles */",minimal:"/* Minimal theme styles */"})[e],B=()=>{switch(I.cssFramework){case"bootstrap":return'<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">';case"tailwind":return'<script src="https://cdn.tailwindcss.com"></script>';case"bulma":return'<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">';default:return""}},q=()=>{let e=B(),n=I.includeCSS?"<style>".concat(A(I.theme),"</style>"):"",t=I.mathRendering?'\n      <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>\n      <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>\n    ':"",a=I.syntaxHighlighting?'\n      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">\n      <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>\n      <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>\n    ':"";return"fragment"===I.outputFormat?y:"clean"===I.outputFormat?'<div class="markdown-content">'.concat(y,"</div>"):'<!DOCTYPE html>\n<html lang="en">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <meta name="description" content="Generated from Markdown">\n    <title>Markdown Document</title>\n    '.concat(e,"\n    ").concat(n,"\n    ").concat(a,"\n    ").concat(t,'\n</head>\n<body>\n    <main class="markdown-content" role="main">\n        ').concat(y,"\n    </main>\n    ").concat(I.includeJS?"\n    <script>\n        // Smooth scrolling for anchor links\n        document.addEventListener('DOMContentLoaded', function() {\n            const links = document.querySelectorAll('a[href^=\"#\"]');\n            links.forEach(link => {\n                link.addEventListener('click', function(e) {\n                    e.preventDefault();\n                    const target = document.querySelector(this.getAttribute('href'));\n                    if (target) {\n                        target.scrollIntoView({ behavior: 'smooth' });\n                    }\n                });\n            });\n        });\n    </script>\n    ":"","\n</body>\n</html>")};(0,r.useEffect)(()=>{let e=z(n);w(e)},[n]),(0,r.useEffect)(()=>{let e=setTimeout(()=>{localStorage.setItem("html-markdown-content",n),localStorage.setItem("html-settings",JSON.stringify(I))},1e3);return()=>clearTimeout(e)},[n,I]),(0,r.useEffect)(()=>{let e=localStorage.getItem("html-markdown-content"),n=localStorage.getItem("html-settings");if(e&&t(e),n)try{_(JSON.parse(n))}catch(e){console.error("Failed to parse saved settings:",e)}},[]);let U=async()=>{C(!0),O("idle");try{await new Promise(e=>setTimeout(e,1500));let e=q(),n=I.minifyOutput?e.replace(/\s+/g," ").replace(/>\s+</g,"><").trim():e,t=new Blob([n],{type:"text/html"}),a=URL.createObjectURL(t),r=document.createElement("a");r.href=a,r.download="document.html",r.click(),URL.revokeObjectURL(a),O("success")}catch(e){console.error("HTML generation failed:",e),O("error")}finally{C(!1)}},G=async e=>{try{await navigator.clipboard.writeText(e),console.log("Content copied to clipboard")}catch(e){console.error("Failed to copy to clipboard:",e)}};return(0,a.jsxs)("div",{className:"".concat(H?"fixed inset-0 z-50":"min-h-screen"," bg-gray-50"),children:[(0,a.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"bg-orange-100 p-2 rounded-lg",children:(0,a.jsx)(o.Z,{className:"h-6 w-6 text-orange-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Markdown to HTML Converter"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Convert your Markdown documents to clean, semantic HTML"})]})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>L(!T),className:"p-2 rounded-lg transition-colors ".concat(T?"bg-orange-100 text-orange-600":"hover:bg-gray-100 text-gray-600"),title:T?"Hide Preview":"Show Preview",children:(0,a.jsx)(s.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>M(!N),className:"p-2 rounded-lg transition-colors ".concat(N?"bg-orange-100 text-orange-600":"hover:bg-gray-100 text-gray-600"),title:"HTML Settings",children:(0,a.jsx)(i.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>F(!H),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600",title:H?"Exit Fullscreen":"Enter Fullscreen",children:H?(0,a.jsx)(l.Z,{className:"h-5 w-5"}):(0,a.jsx)(c.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:U,disabled:S||!n.trim(),className:"bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:S?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.Z,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Generating..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Generate HTML"})]})})]})]}),"success"===$&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2",children:[(0,a.jsx)(u.Z,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"text-green-800",children:"HTML generated successfully!"})]}),"error"===$&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[(0,a.jsx)(h.Z,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-800",children:"Failed to generate HTML. Please try again."})]})]}),(0,a.jsxs)("div",{className:"flex",style:{height:"calc(100vh - 89px)"},children:[N&&(0,a.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"HTML Output Settings"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 mr-2"}),"Output Format"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Format Type"}),(0,a.jsxs)("select",{value:I.outputFormat,onChange:e=>_(n=>({...n,outputFormat:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"fragment",children:"HTML Fragment"}),(0,a.jsx)("option",{value:"clean",children:"Clean HTML"}),(0,a.jsx)("option",{value:"standalone",children:"Standalone Page"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"CSS Framework"}),(0,a.jsxs)("select",{value:I.cssFramework,onChange:e=>_(n=>({...n,cssFramework:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"none",children:"None"}),(0,a.jsx)("option",{value:"bootstrap",children:"Bootstrap"}),(0,a.jsx)("option",{value:"tailwind",children:"Tailwind CSS"}),(0,a.jsx)("option",{value:"bulma",children:"Bulma"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Theme"}),(0,a.jsxs)("select",{value:I.theme,onChange:e=>_(n=>({...n,theme:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"default",children:"Default"}),(0,a.jsx)("option",{value:"github",children:"GitHub"}),(0,a.jsx)("option",{value:"material",children:"Material Design"}),(0,a.jsx)("option",{value:"dark",children:"Dark Theme"}),(0,a.jsx)("option",{value:"minimal",children:"Minimal"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4 mr-2"}),"Features"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.includeCSS,onChange:e=>_(n=>({...n,includeCSS:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include CSS Styles"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.includeJS,onChange:e=>_(n=>({...n,includeJS:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include JavaScript"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.syntaxHighlighting,onChange:e=>_(n=>({...n,syntaxHighlighting:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Syntax Highlighting"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.mathRendering,onChange:e=>_(n=>({...n,mathRendering:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Math Rendering"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.responsiveDesign,onChange:e=>_(n=>({...n,responsiveDesign:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Responsive Design"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.accessibilityFeatures,onChange:e=>_(n=>({...n,accessibilityFeatures:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Accessibility Features"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.semanticHTML,onChange:e=>_(n=>({...n,semanticHTML:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Semantic HTML"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.minifyOutput,onChange:e=>_(n=>({...n,minifyOutput:e.target.checked})),className:"rounded border-gray-300 text-orange-600 focus:ring-orange-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Minify Output"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Custom CSS"}),(0,a.jsx)("textarea",{value:I.customCSS,onChange:e=>_(n=>({...n,customCSS:e.target.value})),placeholder:"/* Add your custom CSS here */",className:"w-full p-2 border border-gray-300 rounded-md text-sm font-mono",rows:6})]})]})}),(0,a.jsxs)("div",{className:"".concat(T?"w-1/2":"w-full"," flex flex-col"),children:[(0,a.jsxs)("div",{className:"bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Markdown Editor"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{ref:P,type:"file",accept:".md,.markdown",onChange:e=>{var n;let a=null===(n=e.target.files)||void 0===n?void 0:n[0];if(a&&("text/markdown"===a.type||a.name.endsWith(".md"))){let e=new FileReader;e.onload=e=>{var n;let a=null===(n=e.target)||void 0===n?void 0:n.result;t(a)},e.readAsText(a)}},className:"hidden"}),(0,a.jsx)("button",{onClick:()=>{var e;return null===(e=P.current)||void 0===e?void 0:e.click()},className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Upload Markdown File",children:(0,a.jsx)(f.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>G(n),className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Copy Markdown",children:(0,a.jsx)(x.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>G(q()),className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Copy HTML",children:(0,a.jsx)(o.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>t(""),className:"text-xs px-2 py-1 hover:bg-gray-200 rounded transition-colors text-gray-600",children:"Clear"})]})]}),(0,a.jsx)("textarea",{ref:E,value:n,onChange:e=>t(e.target.value),className:"flex-1 p-4 font-mono text-sm resize-none focus:outline-none bg-white",placeholder:"Paste your Markdown content here or start typing...",spellCheck:!1})]}),T&&(0,a.jsxs)("div",{className:"".concat("w-1/2"," flex flex-col border-l border-gray-200"),children:[(0,a.jsxs)("div",{className:"bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"HTML Preview"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>Z("desktop"),className:"p-1 rounded transition-colors ".concat("desktop"===D?"bg-orange-100 text-orange-600":"hover:bg-gray-200 text-gray-600"),title:"Desktop View",children:(0,a.jsx)(j,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>Z("tablet"),className:"p-1 rounded transition-colors ".concat("tablet"===D?"bg-orange-100 text-orange-600":"hover:bg-gray-200 text-gray-600"),title:"Tablet View",children:(0,a.jsx)(b,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>Z("mobile"),className:"p-1 rounded transition-colors ".concat("mobile"===D?"bg-orange-100 text-orange-600":"hover:bg-gray-200 text-gray-600"),title:"Mobile View",children:(0,a.jsx)(v.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"flex-1 overflow-auto bg-gray-100 p-4",children:(0,a.jsx)("div",{className:"mx-auto bg-white shadow-sm border border-gray-200 transition-all duration-300",style:{width:(()=>{switch(D){case"mobile":return"375px";case"tablet":return"768px";default:return"100%"}})(),minHeight:"100%"},children:(0,a.jsxs)("div",{ref:R,className:"p-4",style:{fontFamily:"minimal"===I.theme?"Georgia, serif":"material"===I.theme?"Roboto, sans-serif":'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',background:"dark"===I.theme?"#1a1a1a":"#fff",color:"dark"===I.theme?"#e0e0e0":"#333"},children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:A(I.theme)}}),(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:y}})]})})})]})]})]})}},4346:function(e,n,t){"use strict";t.d(n,{$G:function(){return r},bU:function(){return o}});var a=t(3275);function r(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:n,locale:t}=(0,a.QT)();return{t:(t,a)=>n(t,e,a),locale:t,ready:!0}}function o(){let{locale:e,changeLanguage:n}=(0,a.QT)();return{locale:e,locales:["en","ja"],defaultLocale:"en",changeLanguage:n,isRTL:!1}}},3275:function(e,n,t){"use strict";t.d(n,{QT:function(){return l},XJ:function(){return c},bd:function(){return i}});var a=t(7437),r=t(2265),o=t(4033);let s=(0,r.createContext)(void 0);function i(e){let{children:n,locale:t,translations:i}=e,[l,d]=(0,r.useState)(t),[m,u]=(0,r.useState)(i);(0,o.useRouter)(),(0,o.usePathname)();let h=async e=>{if(e!==l)try{localStorage.setItem("locale",e);let n=Object.keys(m),t=await c(e,n);d(e),u(t)}catch(e){console.error("Failed to change language:",e),localStorage.setItem("locale",l)}};return(0,r.useEffect)(()=>{d(t),u(i)},[t,i]),(0,a.jsx)(s.Provider,{value:{locale:l,t:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",t=arguments.length>2?arguments[2]:void 0,a=e.split("."),r=m[n];for(let o of a){if(!r||"object"!=typeof r||!(o in r))return console.warn("Translation key not found: ".concat(n,".").concat(e)),(null==t?void 0:t.returnObjects)?[]:e;r=r[o]}return(null==t?void 0:t.returnObjects)?r:"string"==typeof r?r:e},changeLanguage:h,translations:m},children:n})}function l(){let e=(0,r.useContext)(s);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}async function c(e,n){let a={};for(let r of n)try{let n=await t(1539)("./".concat(e,"/").concat(r,".json"));a[r]=n.default||n}catch(n){if(console.warn("Failed to load translation: ".concat(e,"/").concat(r,".json")),"en"!==e)try{let e=await t(2401)("./".concat(r,".json"));a[r]=e.default||e}catch(e){console.warn("Failed to load fallback translation: en/".concat(r,".json")),a[r]={}}else a[r]={}}return a}},622:function(e,n,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=t(2265),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,i=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,n,t){var a,o={},c=null,d=null;for(a in void 0!==t&&(c=""+t),void 0!==n.key&&(c=""+n.key),void 0!==n.ref&&(d=n.ref),n)s.call(n,a)&&!l.hasOwnProperty(a)&&(o[a]=n[a]);if(e&&e.defaultProps)for(a in n=e.defaultProps)void 0===o[a]&&(o[a]=n[a]);return{$$typeof:r,type:e,key:c,ref:d,props:o,_owner:i.current}}n.Fragment=o,n.jsx=c,n.jsxs=c},7437:function(e,n,t){"use strict";e.exports=t(622)},4033:function(e,n,t){e.exports=t(290)}},function(e){e.O(0,[2971,7864,1744],function(){return e(e.s=2125)}),_N_E=e.O()}]);