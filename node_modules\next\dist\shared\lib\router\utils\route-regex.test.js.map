{"version": 3, "sources": ["../../../../../src/shared/lib/router/utils/route-regex.test.ts"], "names": ["describe", "it", "regex", "getNamedRouteRegex", "expect", "routeKeys", "toEqual", "nxtIauthor", "nxtPid", "groups", "pos", "repeat", "optional", "re", "test", "toBe", "toBeUndefined"], "mappings": ";;;;4BAAmC;AAEnCA,SAAS,sBAAsB;IAC7BC,GAAG,wEAAwE;QACzE,MAAMC,QAAQC,IAAAA,8BAAkB,EAAC,4BAA4B;QAE7DC,OAAOF,MAAMG,SAAS,EAAEC,OAAO,CAAC;YAC9BC,YAAY;YACZC,QAAQ;QACV;QAEAJ,OAAOF,MAAMO,MAAM,CAAC,SAAS,EAAEH,OAAO,CAAC;YACrCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOF,MAAMO,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOF,MAAMW,EAAE,CAACC,IAAI,CAAC,wBAAwBC,IAAI,CAAC;IACpD;IAEAd,GAAG,kDAAkD;QACnD,MAAMC,QAAQC,IAAAA,8BAAkB,EAAC,iCAAiC;QAElEC,OAAOF,MAAMG,SAAS,EAAEC,OAAO,CAAC;YAC9BC,YAAY;YACZC,QAAQ;QACV;QAEAJ,OAAOF,MAAMO,MAAM,CAAC,SAAS,EAAEH,OAAO,CAAC;YACrCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOF,MAAMO,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOF,MAAMW,EAAE,CAACC,IAAI,CAAC,6BAA6BC,IAAI,CAAC;IACzD;IAEAd,GAAG,4EAA4E;QAC7E,MAAMC,QAAQC,IAAAA,8BAAkB,EAAC,0BAA0B;QAE3DC,OAAOF,MAAMG,SAAS,EAAEC,OAAO,CAAC;YAC9BE,QAAQ;QACV;QAEAJ,OAAOF,MAAMO,MAAM,CAAC,SAAS,EAAEO,aAAa;QAE5CZ,OAAOF,MAAMO,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOF,MAAMW,EAAE,CAACC,IAAI,CAAC,0BAA0BC,IAAI,CAAC;IACtD;IAEAd,GAAG,gDAAgD;QACjD,MAAMC,QAAQC,IAAAA,8BAAkB,EAAC,kBAAkB;QAEnDC,OAAOF,MAAMG,SAAS,EAAEC,OAAO,CAAC;YAC9BE,QAAQ;QACV;QAEAJ,OAAOF,MAAMO,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;IACF;IAEAX,GAAG,0DAA0D;QAC3D,MAAMC,QAAQC,IAAAA,8BAAkB,EAAC,qBAAqB;QAEtDC,OAAOF,MAAMG,SAAS,EAAEC,OAAO,CAAC;YAC9BE,QAAQ;QACV;QAEAJ,OAAOF,MAAMO,MAAM,CAAC,KAAK,EAAEH,OAAO,CAAC;YACjCI,KAAK;YACLC,QAAQ;YACRC,UAAU;QACZ;QAEAR,OAAOF,MAAMW,EAAE,CAACC,IAAI,CAAC,cAAcC,IAAI,CAAC;QACxCX,OAAOF,MAAMW,EAAE,CAACC,IAAI,CAAC,kBAAkBC,IAAI,CAAC;QAC5CX,OAAOF,MAAMW,EAAE,CAACC,IAAI,CAAC,YAAYC,IAAI,CAAC;IACxC;AACF"}