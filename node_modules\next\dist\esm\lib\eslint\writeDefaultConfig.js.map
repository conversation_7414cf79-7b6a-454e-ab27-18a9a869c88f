{"version": 3, "sources": ["../../../src/lib/eslint/writeDefaultConfig.ts"], "names": ["promises", "fs", "chalk", "os", "path", "CommentJson", "Log", "writeDefaultConfig", "baseDir", "exists", "emptyEslintrc", "emptyPkgJsonConfig", "selectedConfig", "eslintrcFile", "pkgJsonPath", "packageJsonConfig", "ext", "extname", "newFileContent", "stringify", "writeFile", "EOL", "info", "bold", "basename", "eslintConfig", "join", "console", "log", "green"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,WAAW,2BAA0B;AAC5C,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,YAAYC,iBAAiB,kCAAiC;AAG9D,YAAYC,SAAS,yBAAwB;AAE7C,OAAO,eAAeC,mBACpBC,OAAe,EACf,EAAEC,MAAM,EAAEC,aAAa,EAAEC,kBAAkB,EAAmB,EAC9DC,cAAmB,EACnBC,YAA2B,EAC3BC,WAA0B,EAC1BC,iBAA+C;IAE/C,IAAI,CAACN,UAAUC,iBAAiBG,cAAc;QAC5C,MAAMG,MAAMZ,KAAKa,OAAO,CAACJ;QAEzB,IAAIK;QACJ,IAAIF,QAAQ,WAAWA,QAAQ,QAAQ;YACrCE,iBAAiB;QACnB,OAAO;YACLA,iBAAiBb,YAAYc,SAAS,CAACP,gBAAgB,MAAM;YAE7D,IAAII,QAAQ,OAAO;gBACjBE,iBAAiB,sBAAsBA;YACzC;QACF;QAEA,MAAMjB,GAAGmB,SAAS,CAACP,cAAcK,iBAAiBf,GAAGkB,GAAG;QAExDf,IAAIgB,IAAI,CACN,CAAC,gDAAgD,EAAEpB,MAAMqB,IAAI,CAC3DnB,KAAKoB,QAAQ,CAACX,eACd,yBAAyB,CAAC;IAEhC,OAAO,IAAI,CAACJ,UAAUE,sBAAsBI,mBAAmB;QAC7DA,kBAAkBU,YAAY,GAAGb;QAEjC,IAAIE,aACF,MAAMb,GAAGmB,SAAS,CAChBN,aACAT,YAAYc,SAAS,CAACJ,mBAAmB,MAAM,KAAKZ,GAAGkB,GAAG;QAG9Df,IAAIgB,IAAI,CACN,CAAC,qBAAqB,EAAEpB,MAAMqB,IAAI,CAChC,gBACA,8CAA8C,CAAC;IAErD,OAAO,IAAI,CAACd,QAAQ;QAClB,MAAMR,GAAGmB,SAAS,CAChBhB,KAAKsB,IAAI,CAAClB,SAAS,mBACnBH,YAAYc,SAAS,CAACP,gBAAgB,MAAM,KAAKT,GAAGkB,GAAG;QAGzDM,QAAQC,GAAG,CACT1B,MAAM2B,KAAK,CACT,CAAC,eAAe,EAAE3B,MAAMqB,IAAI,CAC1B,kBACA,uDAAuD,CAAC;IAGhE;AACF"}