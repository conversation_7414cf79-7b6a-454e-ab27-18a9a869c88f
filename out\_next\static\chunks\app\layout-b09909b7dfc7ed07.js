(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{2401:function(e,n,t){var o={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function s(e){if(!t.o(o,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=o[e],s=n[0];return t.e(n[1]).then(function(){return t.t(s,19)})}s.keys=function(){return Object.keys(o)},s.id=2401,e.exports=s},1539:function(e,n,t){var o={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function s(e){if(!t.o(o,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=o[e],s=n[0];return t.e(n[1]).then(function(){return t.t(s,19)})}s.keys=function(){return Object.keys(o)},s.id=1539,e.exports=s},3523:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},8956:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},2549:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7672:function(e,n,t){Promise.resolve().then(t.bind(t,3932)),Promise.resolve().then(t.t.bind(t,936,23)),Promise.resolve().then(t.t.bind(t,8877,23))},3932:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return N}});var o=t(7437),s=t(2265),a=t(4033),r=t(3275),i=t(1396),c=t.n(i),l=t(6637),d=t(2549),m=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,m.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var x=t(4346),h=t(8956),j=t(3523);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,m.Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),p=[{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"ja",name:"日本語",flag:"\uD83C\uDDEF\uD83C\uDDF5"}];function g(){let[e,n]=(0,s.useState)(!1),t=(0,s.useRef)(null),{locale:a,changeLanguage:r}=(0,x.bU)(),i=p.find(e=>e.code===a)||p[0];(0,s.useEffect)(()=>{function e(e){t.current&&!t.current.contains(e.target)&&n(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let c=e=>{r(e),n(!1)};return(0,o.jsxs)("div",{className:"relative",ref:t,children:[(0,o.jsxs)("button",{onClick:()=>n(!e),className:"flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors text-gray-700","aria-label":"Change language",children:[(0,o.jsx)(h.Z,{className:"h-4 w-4"}),(0,o.jsx)("span",{className:"hidden sm:inline text-sm font-medium",children:i.name}),(0,o.jsx)("span",{className:"sm:hidden text-lg",children:i.flag}),(0,o.jsx)(j.Z,{className:"h-4 w-4 transition-transform ".concat(e?"rotate-180":"")})]}),e&&(0,o.jsx)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50",children:p.map(e=>(0,o.jsxs)("button",{onClick:()=>c(e.code),className:"w-full flex items-center justify-between px-4 py-2 text-sm hover:bg-gray-50 transition-colors ".concat(a===e.code?"bg-blue-50 text-blue-600":"text-gray-700"),children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,o.jsx)("span",{className:"text-lg",children:e.flag}),(0,o.jsx)("span",{className:"font-medium",children:e.name})]}),a===e.code&&(0,o.jsx)(f,{className:"h-4 w-4 text-blue-600"})]},e.code))})]})}function b(){let[e,n]=(0,s.useState)(!1),{t}=(0,x.$G)("navbar");return(0,o.jsxs)("nav",{className:"sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200",children:[(0,o.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,o.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,o.jsx)("div",{className:"flex items-center",children:(0,o.jsxs)(c(),{href:"/",className:"flex items-center space-x-2",children:[(0,o.jsx)(l.Z,{className:"h-8 w-8 text-blue-600"}),(0,o.jsx)("span",{className:"font-bold text-xl text-gray-900",children:t("brand")})]})}),(0,o.jsx)("div",{className:"hidden md:block",children:(0,o.jsxs)("div",{className:"ml-10 flex items-baseline space-x-4",children:[(0,o.jsx)(c(),{href:"/",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.home")}),(0,o.jsx)(c(),{href:"/demo",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.demo")}),(0,o.jsx)(c(),{href:"/editor",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.editor")}),(0,o.jsx)(c(),{href:"/markdown-to-html",className:"bg-orange-600 text-white hover:bg-orange-700 px-4 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.mdToHtml")}),(0,o.jsx)(c(),{href:"/markdown-to-pdf",className:"bg-green-600 text-white hover:bg-green-700 px-4 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.mdToPdf")}),(0,o.jsx)(c(),{href:"/markdown-to-word",className:"bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.mdToWord")}),(0,o.jsx)(c(),{href:"/faq",className:"text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors",children:t("navigation.faq")}),(0,o.jsx)(g,{})]})}),(0,o.jsx)("div",{className:"md:hidden",children:(0,o.jsx)("button",{onClick:()=>n(!e),className:"inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:e?(0,o.jsx)(d.Z,{className:"h-6 w-6"}):(0,o.jsx)(u,{className:"h-6 w-6"})})})]})}),e&&(0,o.jsx)("div",{className:"md:hidden",children:(0,o.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-b border-gray-200",children:[(0,o.jsx)(c(),{href:"/",className:"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:t("navigation.home")}),(0,o.jsx)(c(),{href:"/demo",className:"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:t("navigation.demo")}),(0,o.jsx)(c(),{href:"/editor",className:"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:t("navigation.editor")}),(0,o.jsx)(c(),{href:"/markdown-to-html",className:"bg-orange-600 text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"MD to HTML"}),(0,o.jsx)(c(),{href:"/markdown-to-pdf",className:"bg-green-600 text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"MD to PDF"}),(0,o.jsx)(c(),{href:"/markdown-to-word",className:"bg-blue-600 text-white block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"MD to Word"}),(0,o.jsx)(c(),{href:"/privacy",className:"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"Privacy"}),(0,o.jsx)(c(),{href:"/faq",className:"text-gray-700 hover:text-blue-600 block px-3 py-2 rounded-md text-base font-medium",onClick:()=>n(!1),children:"FAQ"})]})})]})}/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,m.Z)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),v=(0,m.Z)("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),k=(0,m.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);function w(){let{t:e}=(0,x.$G)("footer");return(0,o.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,o.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,o.jsx)(l.Z,{className:"h-8 w-8 text-blue-400"}),(0,o.jsx)("span",{className:"font-bold text-xl",children:e("brand")})]}),(0,o.jsx)("p",{className:"text-gray-300 mb-4",children:e("description")}),(0,o.jsxs)("div",{className:"flex space-x-4",children:[(0,o.jsx)("a",{href:"#",className:"text-gray-400 hover:text-blue-400 transition-colors",children:(0,o.jsx)(y,{className:"h-5 w-5"})}),(0,o.jsx)("a",{href:"#",className:"text-gray-400 hover:text-blue-400 transition-colors",children:(0,o.jsx)(v,{className:"h-5 w-5"})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-4",children:e("sections.quickLinks.title")}),(0,o.jsxs)("ul",{className:"space-y-2",children:[(0,o.jsx)("li",{children:(0,o.jsx)(c(),{href:"/",className:"text-gray-300 hover:text-white transition-colors",children:e("sections.quickLinks.links.home")})}),(0,o.jsx)("li",{children:(0,o.jsx)(c(),{href:"/privacy",className:"text-gray-300 hover:text-white transition-colors",children:e("sections.quickLinks.links.privacy")})}),(0,o.jsx)("li",{children:(0,o.jsx)(c(),{href:"/faq",className:"text-gray-300 hover:text-white transition-colors",children:e("sections.quickLinks.links.faq")})})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-4",children:e("sections.contact.title")}),(0,o.jsx)("ul",{className:"space-y-2",children:(0,o.jsx)("li",{children:(0,o.jsxs)(c(),{href:"/contact",className:"text-gray-300 hover:text-white transition-colors flex items-center",children:[(0,o.jsx)(k,{className:"h-4 w-4 mr-2"}),e("sections.contact.contactUs")]})})})]})]}),(0,o.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center",children:(0,o.jsx)("p",{className:"text-gray-400",children:e("copyright")})})]})})}function N(e){let{children:n}=e,t=(0,a.usePathname)(),[i,c]=(0,s.useState)("en"),[l,d]=(0,s.useState)({}),[m,u]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{let e="en";{let n=localStorage.getItem("locale"),t=["en","ja"];if(n&&t.includes(n))e=n;else{let n=navigator.language.split("-")[0];t.includes(n)&&(e=n)}}c(e);let n=async()=>{try{let n=await (0,r.XJ)(e,["common","navbar","footer","hero","faq","faq-page","contact","not-found","markdown-editor","demo","demo-player","editor","layout","benefits","comparison","features","introduction","tutorial","use-cases","privacy","markdown-to-html","markdown-to-pdf","markdown-to-word","markdown-to-html-page","markdown-to-pdf-page","markdown-to-word-page"]);d(n)}catch(e){console.error("Failed to load translations:",e),d({})}finally{u(!1)}};n()},[t]),m)?(0,o.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,o.jsxs)(r.bd,{locale:i,translations:l,children:[(0,o.jsx)(b,{}),(0,o.jsx)("main",{children:n}),(0,o.jsx)(w,{})]})}},4346:function(e,n,t){"use strict";t.d(n,{$G:function(){return s},bU:function(){return a}});var o=t(3275);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:n,locale:t}=(0,o.QT)();return{t:(t,o)=>n(t,e,o),locale:t,ready:!0}}function a(){let{locale:e,changeLanguage:n}=(0,o.QT)();return{locale:e,locales:["en","ja"],defaultLocale:"en",changeLanguage:n,isRTL:!1}}},3275:function(e,n,t){"use strict";t.d(n,{QT:function(){return c},XJ:function(){return l},bd:function(){return i}});var o=t(7437),s=t(2265),a=t(4033);let r=(0,s.createContext)(void 0);function i(e){let{children:n,locale:t,translations:i}=e,[c,d]=(0,s.useState)(t),[m,u]=(0,s.useState)(i);(0,a.useRouter)(),(0,a.usePathname)();let x=async e=>{if(e!==c)try{localStorage.setItem("locale",e);let n=Object.keys(m),t=await l(e,n);d(e),u(t)}catch(e){console.error("Failed to change language:",e),localStorage.setItem("locale",c)}};return(0,s.useEffect)(()=>{d(t),u(i)},[t,i]),(0,o.jsx)(r.Provider,{value:{locale:c,t:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",t=arguments.length>2?arguments[2]:void 0,o=e.split("."),s=m[n];for(let a of o){if(!s||"object"!=typeof s||!(a in s))return console.warn("Translation key not found: ".concat(n,".").concat(e)),(null==t?void 0:t.returnObjects)?[]:e;s=s[a]}return(null==t?void 0:t.returnObjects)?s:"string"==typeof s?s:e},changeLanguage:x,translations:m},children:n})}function c(){let e=(0,s.useContext)(r);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}async function l(e,n){let o={};for(let s of n)try{let n=await t(1539)("./".concat(e,"/").concat(s,".json"));o[s]=n.default||n}catch(n){if(console.warn("Failed to load translation: ".concat(e,"/").concat(s,".json")),"en"!==e)try{let e=await t(2401)("./".concat(s,".json"));o[s]=e.default||e}catch(e){console.warn("Failed to load fallback translation: en/".concat(s,".json")),o[s]={}}else o[s]={}}return o}},8877:function(){},936:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}}},function(e){e.O(0,[6347,2971,7864,1744],function(){return e(e.s=7672)}),_N_E=e.O()}]);