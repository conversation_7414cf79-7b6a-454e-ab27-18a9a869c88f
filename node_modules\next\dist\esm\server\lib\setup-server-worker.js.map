{"version": 3, "sources": ["../../../src/server/lib/setup-server-worker.ts"], "names": ["v8", "http", "warn", "process", "on", "err", "console", "error", "RESTART_EXIT_CODE", "MAXIMUM_HEAP_SIZE_ALLOWED", "getHeapStatistics", "heap_size_limit", "initializeServerWorker", "requestHandler", "upgradeHandler", "opts", "server", "createServer", "req", "res", "catch", "statusCode", "end", "finally", "memoryUsage", "heapUsed", "close", "exit", "keepAliveTimeout", "Promise", "resolve", "reject", "socket", "upgrade", "hostname", "addr", "address", "host", "undefined", "port", "listen"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAA+C,OAAM;AAE5D,6EAA6E;AAC7E,OAAO,kBAAiB;AACxB,OAAO,yBAAwB;AAE/B,SAASC,IAAI,QAAQ,yBAAwB;AAG7CC,QAAQC,EAAE,CAAC,sBAAsB,CAACC;IAChCC,QAAQC,KAAK,CAACF;AAChB;AAEAF,QAAQC,EAAE,CAAC,qBAAqB,CAACC;IAC/BC,QAAQC,KAAK,CAACF;AAChB;AAEA,OAAO,MAAMG,oBAAoB,GAAE;AAEnC,MAAMC,4BACJ,AAACT,GAAGU,iBAAiB,GAAGC,eAAe,GAAG,OAAO,OAAQ;AAa3D,OAAO,eAAeC,uBACpBC,cAAoC,EACpCC,cAAoC,EACpCC,IASC;IAMD,MAAMC,SAASf,KAAKgB,YAAY,CAAC,CAACC,KAAKC;QACrC,OAAON,eAAeK,KAAKC,KACxBC,KAAK,CAAC,CAACf;YACNc,IAAIE,UAAU,GAAG;YACjBF,IAAIG,GAAG,CAAC;YACRhB,QAAQC,KAAK,CAACF;QAChB,GACCkB,OAAO,CAAC;YACP,IACEpB,QAAQqB,WAAW,GAAGC,QAAQ,GAAG,OAAO,OACxChB,2BACA;gBACAP,KACE;gBAEFc,OAAOU,KAAK;gBACZvB,QAAQwB,IAAI,CAACnB;YACf;QACF;IACJ;IAEA,IAAIO,KAAKa,gBAAgB,EAAE;QACzBZ,OAAOY,gBAAgB,GAAGb,KAAKa,gBAAgB;IACjD;IAEA,OAAO,IAAIC,QAAQ,OAAOC,SAASC;QACjCf,OAAOZ,EAAE,CAAC,SAAS,CAACC;YAClBC,QAAQC,KAAK,CAAC,CAAC,wCAAwC,CAAC,EAAEF;YAC1DF,QAAQwB,IAAI,CAAC;QACf;QAEA,IAAIb,gBAAgB;YAClBE,OAAOZ,EAAE,CAAC,WAAW,CAACc,KAAKc,QAAQC;gBACjCnB,eAAeI,KAAKc,QAAQC;YAC9B;QACF;QACA,IAAIC,WAAWnB,KAAKmB,QAAQ,IAAI;QAEhClB,OAAOZ,EAAE,CAAC,aAAa;YACrB,IAAI;gBACF,MAAM+B,OAAOnB,OAAOoB,OAAO;gBAC3B,MAAMC,OAAOF,OACT,OAAOA,SAAS,WACdA,KAAKC,OAAO,GACZD,OACFG;gBACJ,MAAMC,OAAOJ,QAAQ,OAAOA,SAAS,WAAWA,KAAKI,IAAI,GAAG;gBAE5D,IAAI,CAACA,QAAQ,CAACF,MAAM;oBAClB/B,QAAQC,KAAK,CACX,CAAC,kDAAkD,CAAC,EACpD4B;oBAEFhC,QAAQwB,IAAI,CAAC;gBACf;gBAEAG,QAAQ;oBACNd;oBACAuB;oBACAL,UAAUG;gBACZ;YACF,EAAE,OAAOhC,KAAK;gBACZ,OAAO0B,OAAO1B;YAChB;QACF;QACAW,OAAOwB,MAAM,CAAC,GAAGN;IACnB;AACF"}