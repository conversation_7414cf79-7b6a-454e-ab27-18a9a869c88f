(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1931],{2401:function(e,t,s){var r={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function n(e){if(!s.o(r,e))return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return s.e(t[1]).then(function(){return s.t(n,19)})}n.keys=function(){return Object.keys(r)},n.id=2401,e.exports=n},1539:function(e,t,s){var r={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function n(e){if(!s.o(r,e))return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return s.e(t[1]).then(function(){return s.t(n,19)})}n.keys=function(){return Object.keys(r)},n.id=1539,e.exports=n},8291:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},3523:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},4530:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2455:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},5817:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9670:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},8956:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},9804:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},4900:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},4280:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},1827:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9036:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},1271:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},5750:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2369:function(e,t,s){"use strict";s.d(t,{Z:function(){return n}});var r=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r.Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1344:function(e,t,s){Promise.resolve().then(s.bind(s,1063)),Promise.resolve().then(s.bind(s,3272)),Promise.resolve().then(s.bind(s,6954)),Promise.resolve().then(s.bind(s,6825))},6954:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});var r=s(7437),n=s(2265),a=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var i=s(3523),o=s(4346);function c(){let[e,t]=(0,n.useState)(null),{t:s}=(0,o.$G)("faq"),a=s("faqs",{returnObjects:!0}),c=Array.isArray(a)?a:[];return(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:s("title")}),(0,r.jsx)("p",{className:"text-xl text-gray-600",children:s("subtitle")})]}),(0,r.jsx)("div",{className:"space-y-4",children:c.map((s,n)=>(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg border border-gray-200",children:[(0,r.jsxs)("button",{className:"w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-100 transition-colors",onClick:()=>t(e===n?null:n),children:[(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:s.question}),e===n?(0,r.jsx)(l,{className:"h-5 w-5 text-gray-500"}):(0,r.jsx)(i.Z,{className:"h-5 w-5 text-gray-500"})]}),e===n&&(0,r.jsx)("div",{className:"px-6 pb-4",children:(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:s.answer})})]},n))}),(0,r.jsxs)("div",{className:"mt-16 bg-blue-50 rounded-2xl p-8 text-center",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:s("support.title")}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:s("support.description")}),(0,r.jsx)("button",{className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:s("support.button")})]})]})})}},6825:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return m}});var r=s(7437),n=s(1396),a=s.n(n),l=s(8291),i=s(4900),o=s(6637),c=s(9670),d=s(4346);function m(){let{t:e}=(0,d.$G)("hero");return(0,r.jsxs)("section",{className:"relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-20 lg:py-32",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-5"}),(0,r.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("h1",{className:"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6",children:[e("title.prefix"),(0,r.jsxs)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600",children:[" ",e("title.highlight")]})]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:e("subtitle")}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center mb-12",children:[(0,r.jsxs)(a(),{href:"/editor",className:"bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center group",children:[e("buttons.startEditing"),(0,r.jsx)(l.Z,{className:"ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform"})]}),(0,r.jsxs)(a(),{href:"/demo",className:"border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-lg font-semibold hover:bg-gray-50 transition-colors flex items-center justify-center group",children:[(0,r.jsx)(i.Z,{className:"mr-2 h-5 w-5 group-hover:scale-110 transition-transform"}),e("buttons.watchDemo")]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20",children:[(0,r.jsx)(o.Z,{className:"h-12 w-12 text-blue-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e("features.realTimeEditing.title")}),(0,r.jsx)("p",{className:"text-gray-600",children:e("features.realTimeEditing.description")})]}),(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20",children:[(0,r.jsx)(c.Z,{className:"h-12 w-12 text-purple-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e("features.livePreview.title")}),(0,r.jsx)("p",{className:"text-gray-600",children:e("features.livePreview.description")})]}),(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20",children:[(0,r.jsx)(l.Z,{className:"h-12 w-12 text-green-600 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e("features.exportReady.title")}),(0,r.jsx)("p",{className:"text-gray-600",children:e("features.exportReady.description")})]})]})]})})]})}},1063:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return f}});var r=s(7437),n=s(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,n.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),l=(0,n.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var i=s(9036),o=s(2369),c=s(5750);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,n.Z)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var m=s(4530);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,n.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]),x=(0,n.Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),h=(0,n.Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var p=s(4346);function f(){let{t:e}=(0,p.$G)("benefits"),t={clock:a,dollarSign:l,shield:i.Z,zap:o.Z,users:c.Z,award:d},s=e("benefits",{returnObjects:!0}),n=Array.isArray(s)?s:[],f=e("metrics.data",{returnObjects:!0}),g=Array.isArray(f)?f:[];return(0,r.jsx)("section",{className:"py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:e("title")}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:e("subtitle")})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:n.map((e,s)=>(0,r.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:shadow-lg transition-shadow",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-lg",children:(()=>{let s=t[e.icon];return(0,r.jsx)(s,{className:"h-6 w-6 text-white"})})()}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 ml-3",children:e.title})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4 leading-relaxed",children:e.description}),(0,r.jsx)("div",{className:"bg-blue-50 rounded-lg p-3",children:(0,r.jsx)("div",{className:"text-sm font-medium text-blue-800",children:e.stats})})]},s))}),(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-12 border border-white/20 mb-16",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:e("metrics.title")}),(0,r.jsx)("p",{className:"text-gray-600 max-w-3xl mx-auto",children:"Our free online Markdown live preview editor delivers measurable improvements in productivity, user satisfaction, and content quality across diverse use cases."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:g.map((e,t)=>(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-3xl font-bold text-blue-600 mb-2",children:e.value}),(0,r.jsx)("div",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.label}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:e.description})]},t))})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16",children:[(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 shadow-sm",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Productivity Improvements"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 text-green-500 mr-3"}),(0,r.jsx)("span",{className:"text-gray-700",children:"70% reduction in editing time"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 text-green-500 mr-3"}),(0,r.jsx)("span",{className:"text-gray-700",children:"85% fewer formatting errors"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 text-green-500 mr-3"}),(0,r.jsx)("span",{className:"text-gray-700",children:"60% faster document creation"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.Z,{className:"h-5 w-5 text-green-500 mr-3"}),(0,r.jsx)("span",{className:"text-gray-700",children:"90% improvement in workflow efficiency"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-600 to-blue-600 rounded-2xl p-8 text-white",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-6",children:"User Satisfaction Highlights"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u,{className:"h-5 w-5 text-yellow-300 mr-3"}),(0,r.jsx)("span",{children:"98% of users recommend our platform"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x,{className:"h-5 w-5 text-yellow-300 mr-3"}),(0,r.jsx)("span",{children:"95% accuracy in Markdown rendering"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h,{className:"h-5 w-5 text-yellow-300 mr-3"}),(0,r.jsx)("span",{children:"300% increase in user productivity"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d,{className:"h-5 w-5 text-yellow-300 mr-3"}),(0,r.jsx)("span",{children:"Industry-leading performance standards"})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl p-12 text-white text-center",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Transform Your Markdown Workflow?"}),(0,r.jsx)("p",{className:"text-lg opacity-90 mb-8 max-w-3xl mx-auto",children:"Join millions of satisfied users who have revolutionized their content creation process with our free online Markdown live preview editor. Experience the benefits that industry leaders rely on daily."}),(0,r.jsx)("button",{className:"bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors",children:"Start Creating Now - It's Free!"})]})]})})}},3272:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return g}});var r=s(7437),n=s(9670),a=s(6637),l=s(5817),i=s(1271),o=s(2369),c=s(9036),d=s(8956),m=s(5750),u=s(2455),x=s(4280),h=s(9804),p=s(1827),f=s(4346);function g(){let{t:e}=(0,f.$G)("features"),t=[{icon:n.Z,title:"Real-Time Live Preview",description:"Experience instant Markdown to HTML conversion as you type. Our advanced rendering engine provides immediate visual feedback, eliminating the need to switch between edit and preview modes."},{icon:a.Z,title:"Complete Markdown Support",description:"Full compatibility with GitHub Flavored Markdown, including tables, code blocks, strikethrough, task lists, and syntax highlighting for over 100 programming languages."},{icon:l.Z,title:"Export Flexibility",description:"Download your content as HTML, PDF, or Markdown files. Copy formatted HTML or plain text with a single click. Multiple export options for various use cases."},{icon:i.Z,title:"Mobile-Responsive Design",description:"Edit and preview your Markdown content on any device. Our responsive interface adapts perfectly to smartphones, tablets, and desktop computers."},{icon:o.Z,title:"Lightning-Fast Performance",description:"Optimized for speed with millisecond rendering times. Advanced caching and efficient algorithms ensure smooth performance even with large documents."},{icon:c.Z,title:"Privacy-First Approach",description:"Your content never leaves your browser. All processing happens locally, ensuring complete privacy and security of your sensitive documents."},{icon:d.Z,title:"No Installation Required",description:"Access our free online Markdown live preview editor from any web browser. No downloads, installations, or software updates needed."},{icon:m.Z,title:"Free for Everyone",description:"Completely free to use with no hidden costs, registration requirements, or usage limits. Professional-grade features available to all users."},{icon:u.Z,title:"Syntax Highlighting",description:"Advanced syntax highlighting for code blocks with support for popular programming languages including JavaScript, Python, Java, C++, and many more."},{icon:x.Z,title:"Auto-Save & Recovery",description:"Automatic saving of your work with browser storage. Resume editing exactly where you left off, even after closing the browser."},{icon:h.Z,title:"Customizable Interface",description:"Personalize your editing experience with adjustable panel sizes, theme options, and layout preferences. Create the perfect writing environment."},{icon:p.Z,title:"Advanced Find & Replace",description:"Powerful search and replace functionality with regular expression support. Navigate large documents quickly with instant text searching."}];return(0,r.jsx)("section",{className:"py-20 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl sm:text-4xl font-bold text-gray-900 mb-4",children:"Comprehensive Free Online Markdown Live Preview Features"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Discover why our free online Markdown live preview editor is the preferred choice for writers, developers, and content creators worldwide. Every feature is designed to enhance your productivity."})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow border border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"bg-blue-100 p-3 rounded-lg",children:(0,r.jsx)(e.icon,{className:"h-6 w-6 text-blue-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 ml-3",children:e.title})]}),(0,r.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]},t))}),(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-12 shadow-sm border border-gray-100",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Advanced Features for Professional Use"}),(0,r.jsx)("p",{className:"text-gray-600 max-w-3xl mx-auto",children:"Our free online Markdown live preview editor includes enterprise-grade features that professional writers and developers rely on for their daily work."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Enhanced Editing Capabilities"}),(0,r.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Multi-cursor editing for simultaneous content modification"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Intelligent auto-completion for Markdown syntax"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Bracket matching and automatic indentation"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Line numbers and code folding for better navigation"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Preview Enhancements"}),(0,r.jsxs)("ul",{className:"space-y-3 text-gray-600",children:[(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Synchronized scrolling between editor and preview"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Multiple preview themes and styling options"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Table of contents generation for long documents"]}),(0,r.jsxs)("li",{className:"flex items-start",children:[(0,r.jsx)("span",{className:"bg-blue-100 text-blue-600 rounded-full p-1 mr-3 mt-0.5",children:(0,r.jsx)("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),"Mathematical equation rendering with LaTeX support"]})]})]})]})]})]})})}},4346:function(e,t,s){"use strict";s.d(t,{$G:function(){return n},bU:function(){return a}});var r=s(3275);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:t,locale:s}=(0,r.QT)();return{t:(s,r)=>t(s,e,r),locale:s,ready:!0}}function a(){let{locale:e,changeLanguage:t}=(0,r.QT)();return{locale:e,locales:["en","ja"],defaultLocale:"en",changeLanguage:t,isRTL:!1}}},3275:function(e,t,s){"use strict";s.d(t,{QT:function(){return o},XJ:function(){return c},bd:function(){return i}});var r=s(7437),n=s(2265),a=s(4033);let l=(0,n.createContext)(void 0);function i(e){let{children:t,locale:s,translations:i}=e,[o,d]=(0,n.useState)(s),[m,u]=(0,n.useState)(i);(0,a.useRouter)(),(0,a.usePathname)();let x=async e=>{if(e!==o)try{localStorage.setItem("locale",e);let t=Object.keys(m),s=await c(e,t);d(e),u(s)}catch(e){console.error("Failed to change language:",e),localStorage.setItem("locale",o)}};return(0,n.useEffect)(()=>{d(s),u(i)},[s,i]),(0,r.jsx)(l.Provider,{value:{locale:o,t:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",s=arguments.length>2?arguments[2]:void 0,r=e.split("."),n=m[t];for(let a of r){if(!n||"object"!=typeof n||!(a in n))return console.warn("Translation key not found: ".concat(t,".").concat(e)),(null==s?void 0:s.returnObjects)?[]:e;n=n[a]}return(null==s?void 0:s.returnObjects)?n:"string"==typeof n?n:e},changeLanguage:x,translations:m},children:t})}function o(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}async function c(e,t){let r={};for(let n of t)try{let t=await s(1539)("./".concat(e,"/").concat(n,".json"));r[n]=t.default||t}catch(t){if(console.warn("Failed to load translation: ".concat(e,"/").concat(n,".json")),"en"!==e)try{let e=await s(2401)("./".concat(n,".json"));r[n]=e.default||e}catch(e){console.warn("Failed to load fallback translation: en/".concat(n,".json")),r[n]={}}else r[n]={}}return r}}},function(e){e.O(0,[6347,2971,7864,1744],function(){return e(e.s=1344)}),_N_E=e.O()}]);