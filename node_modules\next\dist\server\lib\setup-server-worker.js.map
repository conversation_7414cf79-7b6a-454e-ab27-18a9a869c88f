{"version": 3, "sources": ["../../../src/server/lib/setup-server-worker.ts"], "names": ["RESTART_EXIT_CODE", "initializeServerWorker", "process", "on", "err", "console", "error", "MAXIMUM_HEAP_SIZE_ALLOWED", "v8", "getHeapStatistics", "heap_size_limit", "requestHandler", "upgradeHandler", "opts", "server", "http", "createServer", "req", "res", "catch", "statusCode", "end", "finally", "memoryUsage", "heapUsed", "warn", "close", "exit", "keepAliveTimeout", "Promise", "resolve", "reject", "socket", "upgrade", "hostname", "addr", "address", "host", "undefined", "port", "listen"], "mappings": ";;;;;;;;;;;;;;;IAkBaA,iBAAiB;eAAjBA;;IAgBSC,sBAAsB;eAAtBA;;;2DAlCP;6DACuC;QAG/C;QACA;qBAEc;;;;;;AAGrBC,QAAQC,EAAE,CAAC,sBAAsB,CAACC;IAChCC,QAAQC,KAAK,CAACF;AAChB;AAEAF,QAAQC,EAAE,CAAC,qBAAqB,CAACC;IAC/BC,QAAQC,KAAK,CAACF;AAChB;AAEO,MAAMJ,oBAAoB;AAEjC,MAAMO,4BACJ,AAACC,WAAE,CAACC,iBAAiB,GAAGC,eAAe,GAAG,OAAO,OAAQ;AAapD,eAAeT,uBACpBU,cAAoC,EACpCC,cAAoC,EACpCC,IASC;IAMD,MAAMC,SAASC,aAAI,CAACC,YAAY,CAAC,CAACC,KAAKC;QACrC,OAAOP,eAAeM,KAAKC,KACxBC,KAAK,CAAC,CAACf;YACNc,IAAIE,UAAU,GAAG;YACjBF,IAAIG,GAAG,CAAC;YACRhB,QAAQC,KAAK,CAACF;QAChB,GACCkB,OAAO,CAAC;YACP,IACEpB,QAAQqB,WAAW,GAAGC,QAAQ,GAAG,OAAO,OACxCjB,2BACA;gBACAkB,IAAAA,SAAI,EACF;gBAEFX,OAAOY,KAAK;gBACZxB,QAAQyB,IAAI,CAAC3B;YACf;QACF;IACJ;IAEA,IAAIa,KAAKe,gBAAgB,EAAE;QACzBd,OAAOc,gBAAgB,GAAGf,KAAKe,gBAAgB;IACjD;IAEA,OAAO,IAAIC,QAAQ,OAAOC,SAASC;QACjCjB,OAAOX,EAAE,CAAC,SAAS,CAACC;YAClBC,QAAQC,KAAK,CAAC,CAAC,wCAAwC,CAAC,EAAEF;YAC1DF,QAAQyB,IAAI,CAAC;QACf;QAEA,IAAIf,gBAAgB;YAClBE,OAAOX,EAAE,CAAC,WAAW,CAACc,KAAKe,QAAQC;gBACjCrB,eAAeK,KAAKe,QAAQC;YAC9B;QACF;QACA,IAAIC,WAAWrB,KAAKqB,QAAQ,IAAI;QAEhCpB,OAAOX,EAAE,CAAC,aAAa;YACrB,IAAI;gBACF,MAAMgC,OAAOrB,OAAOsB,OAAO;gBAC3B,MAAMC,OAAOF,OACT,OAAOA,SAAS,WACdA,KAAKC,OAAO,GACZD,OACFG;gBACJ,MAAMC,OAAOJ,QAAQ,OAAOA,SAAS,WAAWA,KAAKI,IAAI,GAAG;gBAE5D,IAAI,CAACA,QAAQ,CAACF,MAAM;oBAClBhC,QAAQC,KAAK,CACX,CAAC,kDAAkD,CAAC,EACpD6B;oBAEFjC,QAAQyB,IAAI,CAAC;gBACf;gBAEAG,QAAQ;oBACNhB;oBACAyB;oBACAL,UAAUG;gBACZ;YACF,EAAE,OAAOjC,KAAK;gBACZ,OAAO2B,OAAO3B;YAChB;QACF;QACAU,OAAO0B,MAAM,CAAC,GAAGN;IACnB;AACF"}