{"version": 3, "sources": ["../../../src/server/app-render/parse-and-validate-flight-router-state.tsx"], "names": ["flightRouterStateSchema", "parseAndValidateFlightRouterState", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Array", "isArray", "Error", "length", "parse", "JSON", "decodeURIComponent"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,UAAS;AAEjD,OAAO,SAASC,kCACdC,WAA0C;IAE1C,IAAI,OAAOA,gBAAgB,aAAa;QACtC,OAAOC;IACT;IACA,IAAIC,MAAMC,OAAO,CAACH,cAAc;QAC9B,MAAM,IAAII,MACR;IAEJ;IAEA,4EAA4E;IAC5E,yEAAyE;IACzE,iCAAiC;IACjC,gEAAgE;IAChE,wCAAwC;IACxC,IAAIJ,YAAYK,MAAM,GAAG,KAAK,MAAM;QAClC,MAAM,IAAID,MAAM;IAClB;IAEA,IAAI;QACF,OAAON,wBAAwBQ,KAAK,CAClCC,KAAKD,KAAK,CAACE,mBAAmBR;IAElC,EAAE,OAAM;QACN,MAAM,IAAII,MAAM;IAClB;AACF"}