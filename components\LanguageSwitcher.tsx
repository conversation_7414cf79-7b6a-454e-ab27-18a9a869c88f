'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Globe, ChevronDown, Check } from 'lucide-react';
import { useLocale } from '@/hooks/useTranslation';
import { getLanguageName } from '@/lib/translationUtils';

interface Language {
  code: string;
  name: string;
  flag: string;
}

const languages: Language[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
];

export default function LanguageSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { locale, changeLanguage } = useLocale();
  
  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (languageCode: string) => {
    changeLanguage(languageCode);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors text-gray-700"
        aria-label="Change language"
      >
        <Globe className="h-4 w-4" />
        <span className="hidden sm:inline text-sm font-medium">
          {currentLanguage.name}
        </span>
        <span className="sm:hidden text-lg">
          {currentLanguage.flag}
        </span>
        <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`w-full flex items-center justify-between px-4 py-2 text-sm hover:bg-gray-50 transition-colors ${
                locale === language.code ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">{language.flag}</span>
                <span className="font-medium">{language.name}</span>
              </div>
              {locale === language.code && (
                <Check className="h-4 w-4 text-blue-600" />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

// Compact version for mobile
export function LanguageSwitcherCompact() {
  const { locale, changeLanguage } = useLocale();
  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  const handleToggle = () => {
    const currentIndex = languages.findIndex(lang => lang.code === locale);
    const nextIndex = (currentIndex + 1) % languages.length;
    changeLanguage(languages[nextIndex].code);
  };

  return (
    <button
      onClick={handleToggle}
      className="flex items-center space-x-1 px-2 py-1 rounded hover:bg-gray-100 transition-colors"
      aria-label={`Current language: ${currentLanguage.name}. Click to switch.`}
      title={`Switch to ${languages.find(lang => lang.code !== locale)?.name}`}
    >
      <span className="text-lg">{currentLanguage.flag}</span>
      <span className="text-xs font-medium text-gray-600 hidden sm:inline">
        {currentLanguage.code.toUpperCase()}
      </span>
    </button>
  );
}

// Inline language switcher for footer
export function LanguageSwitcherInline() {
  const { locale, changeLanguage } = useLocale();

  return (
    <div className="flex items-center space-x-2">
      <Globe className="h-4 w-4 text-gray-400" />
      <div className="flex space-x-1">
        {languages.map((language, index) => (
          <span key={language.code} className="flex items-center">
            <button
              onClick={() => changeLanguage(language.code)}
              className={`text-sm transition-colors ${
                locale === language.code
                  ? 'text-blue-400 font-medium'
                  : 'text-gray-400 hover:text-gray-300'
              }`}
            >
              {language.name}
            </button>
            {index < languages.length - 1 && (
              <span className="text-gray-500 mx-1">|</span>
            )}
          </span>
        ))}
      </div>
    </div>
  );
}
