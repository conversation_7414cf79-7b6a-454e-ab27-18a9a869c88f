{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["NextWebServer", "BaseServer", "constructor", "options", "Object", "assign", "renderOpts", "webServerConfig", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "IncrementalCache", "requestProtocol", "appDir", "hasAppDir", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "serverOptions", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "getResponseCache", "WebResponseCache", "hasPage", "page", "getBuildId", "buildId", "getHasAppDir", "pagesType", "getPagesManifest", "pathname", "getAppPathsManifest", "attachRequestMeta", "req", "parsedUrl", "addRequestMeta", "query", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "handleCatchallRenderRequest", "res", "Error", "normalizedPage", "isDynamicRoute", "routeRegex", "getNamedRouteRegex", "interpolateDynamicPath", "normalizeVercelUrl", "keys", "routeKeys", "removeTrailingSlash", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "isAPIRoute", "render", "finished", "err", "NoFallbackError", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "isDynamic", "writer", "transformStream", "writable", "getWriter", "innerClose", "target", "write", "chunk", "end", "close", "on", "_event", "cb", "off", "_cb", "undefined", "onClose", "closed", "then", "pipe", "payload", "toUnchunkedString", "String", "byteLength", "generateEtags", "generateETag", "body", "send", "findPageComponents", "params", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": ";;;;+BA2CA;;;eAAqBA;;;qBAjCM;oEAMpB;sBACsB;6BACE;6DACF;4BACF;qCACS;uBACL;6BAC4B;4BACxB;kCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBlB,MAAMA,sBAAsBC,mBAAU;IACnDC,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;QAEN,uBAAuB;QACvBC,OAAOC,MAAM,CAAC,IAAI,CAACC,UAAU,EAAEH,QAAQI,eAAe,CAACC,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACL,UAAU,CAACK,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIC,kCAAgB,CAAC;YAC1BD;YACAD;YACAG,iBAAiB;YACjBC,QAAQ,IAAI,CAACC,SAAS;YACtBC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAACC,aAAa,CAACnB,eAAe,CAACoB,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;QACvD;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAIC,aAAgB,CAAC,IAAI,CAACX,WAAW;IAC9C;IAEA,MAAgBY,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAACN,aAAa,CAACnB,eAAe,CAACyB,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAACP,aAAa,CAACnB,eAAe,CAACC,gBAAgB,CAAC0B,OAAO;IACpE;IAEUC,eAAe;QACvB,OAAO,IAAI,CAACT,aAAa,CAACnB,eAAe,CAAC6B,SAAS,KAAK;IAC1D;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAACX,aAAa,CAACnB,eAAe,CAChC+B,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACZ,aAAa,CAACnB,eAAe,CAACyB,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUO,sBAAsB;QAC9B,MAAMP,OAAO,IAAI,CAACN,aAAa,CAACnB,eAAe,CAACyB,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAACN,aAAa,CAACnB,eAAe,CAACyB,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUQ,kBACRC,GAAmB,EACnBC,SAAiC,EACjC;QACAC,IAAAA,2BAAc,EAACF,KAAK,qBAAqB;YAAE,GAAGC,UAAUE,KAAK;QAAC;IAChE;IAEUhB,uBAAuB;YAE3B;QADJ,MAAM,EAAEiB,iBAAiB,EAAE,GAAG,IAAI,CAACnB,aAAa,CAACnB,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACD,UAAU,qBAAf,iBAAiBK,GAAG,KAAI,CAACkC,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAAC1B,aAAa,CAACnB,eAAe,CAACC,gBAAgB,CAAC6C,gBAAgB;IAC7E;IAEA,MAAgBC,4BACdb,GAAoB,EACpBc,GAAqB,EACrBb,SAAiC,EACD;QAChC,IAAI,EAAEJ,QAAQ,EAAEM,KAAK,EAAE,GAAGF;QAC1B,IAAI,CAACJ,UAAU;YACb,MAAM,IAAIkB,MAAM;QAClB;QAEA,4DAA4D;QAC5D,+CAA+C;QAC/C,MAAMC,iBAAiB,IAAI,CAAC/B,aAAa,CAACnB,eAAe,CAAC+B,QAAQ;QAElE,IAAIA,aAAamB,gBAAgB;YAC/BnB,WAAWmB;YAEX,IAAIC,IAAAA,qBAAc,EAACpB,WAAW;gBAC5B,MAAMqB,aAAaC,IAAAA,8BAAkB,EAACtB,UAAU;gBAChDA,WAAWuB,IAAAA,mCAAsB,EAACvB,UAAUM,OAAOe;gBACnDG,IAAAA,+BAAkB,EAChBrB,KACA,MACArC,OAAO2D,IAAI,CAACJ,WAAWK,SAAS,GAChC,MACAL;YAEJ;QACF;QAEA,wDAAwD;QACxDrB,WAAW2B,IAAAA,wCAAmB,EAAC3B;QAE/B,IAAI,IAAI,CAAC4B,YAAY,EAAE;YACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAAC9B;YAC3D,IAAI6B,gBAAgB;gBAClBzB,UAAUE,KAAK,CAACyB,YAAY,GAAGF;YACjC;QACF;QAEA,MAAMG,mBAAmB,CAAC,CAAC1B,MAAM2B,qBAAqB;QAEtD,IAAIC,IAAAA,sBAAU,EAAClC,WAAW;YACxB,OAAOM,MAAM2B,qBAAqB;QACpC;QAEA,IAAI;YACF,MAAM,IAAI,CAACE,MAAM,CAAChC,KAAKc,KAAKjB,UAAUM,OAAOF,WAAW;YAExD,OAAO;gBACLgC,UAAU;YACZ;QACF,EAAE,OAAOC,KAAK;YACZ,IAAIA,eAAeC,2BAAe,IAAIN,kBAAkB;gBACtD,OAAO;oBACLI,UAAU;gBACZ;YACF;YACA,MAAMC;QACR;IACF;IAEUE,WACRpC,GAAmB,EACnBc,GAAoB,EACpBjB,QAAgB,EAChBM,KAAyB,EACzBtC,UAAsB,EACC;QACvB,MAAM,EAAEwE,YAAY,EAAE,GAAG,IAAI,CAACpD,aAAa,CAACnB,eAAe;QAC3D,IAAI,CAACuE,cAAc;YACjB,MAAM,IAAItB,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIlB,aAAchC,CAAAA,WAAWK,GAAG,GAAG,eAAe,aAAY,GAAI;YAChE2B,WAAW;QACb;QACA,OAAOwC,aACLrC,KACAc,KACAjB,UACAM,OACAxC,OAAOC,MAAM,CAACC,YAAY;YACxByE,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpB3B,GAAoB,EACpBpD,OAMC,EACc;QACfoD,IAAI4B,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAIhF,QAAQiF,eAAe,IAAIjF,QAAQkF,IAAI,KAAK,QAAQ;YACtD9B,IAAI4B,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAAC5B,IAAI+B,SAAS,CAAC,iBAAiB;YAClC/B,IAAI4B,SAAS,CACX,gBACAhF,QAAQoF,MAAM,CAACC,WAAW,GACtBrF,QAAQoF,MAAM,CAACC,WAAW,GAC1BrF,QAAQkF,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAIlF,QAAQoF,MAAM,CAACE,SAAS,EAAE;YAC5B,MAAMC,SAASnC,IAAIoC,eAAe,CAACC,QAAQ,CAACC,SAAS;YAErD,IAAIC;YACJ,MAAMC,SAAS;gBACbC,OAAO,CAACC,QAAsBP,OAAOM,KAAK,CAACC;gBAC3CC,KAAK,IAAMR,OAAOS,KAAK;gBAEvBC,IAAGC,MAAe,EAAEC,EAAc;oBAChCR,aAAaQ;gBACf;gBACAC,KAAIF,MAAe,EAAEG,GAAe;oBAClCV,aAAaW;gBACf;YACF;YACA,MAAMC,UAAU;gBACdZ,8BAAAA;YACF;YACA,uEAAuE;YACvE,wEAAwE;YACxE,uBAAuB;YACvBJ,OAAOiB,MAAM,CAACC,IAAI,CAACF,SAASA;YAC5BvG,QAAQoF,MAAM,CAACsB,IAAI,CAACd;QACtB,OAAO;YACL,MAAMe,UAAU,MAAM3G,QAAQoF,MAAM,CAACwB,iBAAiB;YACtDxD,IAAI4B,SAAS,CAAC,kBAAkB6B,OAAOC,IAAAA,eAAU,EAACH;YAClD,IAAI3G,QAAQ+G,aAAa,EAAE;gBACzB3D,IAAI4B,SAAS,CAAC,QAAQgC,IAAAA,kBAAY,EAACL;YACrC;YACAvD,IAAI6D,IAAI,CAACN;QACX;QAEAvD,IAAI8D,IAAI;IACV;IAEA,MAAgBC,mBAAmB,EACjCtF,IAAI,EACJY,KAAK,EACL2E,MAAM,EAMP,EAAE;QACD,MAAMhC,SAAS,MAAM,IAAI,CAAC7D,aAAa,CAACnB,eAAe,CAACiH,aAAa,CAACxF;QACtE,IAAI,CAACuD,QAAQ,OAAO;QAEpB,OAAO;YACL3C,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAI2E,UAAU,CAAC,CAAC;YAClB;YACAE,YAAYlC;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBmC,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOvB;IACT;IAEUwB,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,6BAAuE;QACrF,wEAAwE;QACxE,OAAO;IACT;IAEUC,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAO3B;IACT;IAEU4B,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAO5B;IACT;IAEU6B,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}