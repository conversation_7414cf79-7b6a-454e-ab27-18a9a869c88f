{"version": 3, "sources": ["../../../../../src/server/future/route-modules/helpers/response-handlers.ts"], "names": ["appendMutableCookies", "handleTemporaryRedirectResponse", "url", "mutableCookies", "headers", "Headers", "location", "Response", "status", "handleBadRequestResponse", "handleNotFoundResponse", "handleMethodNotAllowedResponse", "handleInternalServerErrorResponse"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,uDAAsD;AAG3F,OAAO,SAASC,gCACdC,GAAW,EACXC,cAA+B;IAE/B,MAAMC,UAAU,IAAIC,QAAQ;QAAEC,UAAUJ;IAAI;IAE5CF,qBAAqBI,SAASD;IAE9B,OAAO,IAAII,SAAS,MAAM;QAAEC,QAAQ;QAAKJ;IAAQ;AACnD;AAEA,OAAO,SAASK;IACd,OAAO,IAAIF,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASE;IACd,OAAO,IAAIH,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASG;IACd,OAAO,IAAIJ,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C;AAEA,OAAO,SAASI;IACd,OAAO,IAAIL,SAAS,MAAM;QAAEC,QAAQ;IAAI;AAC1C"}