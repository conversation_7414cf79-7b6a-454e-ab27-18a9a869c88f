{"version": 3, "sources": ["../../src/lib/turbopack-warning.ts"], "names": ["path", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "supportedTurbopackNextConfigOptions", "prodSpecificTurboNextConfigOptions", "validateTurboNextConfig", "dir", "isCustomTurbopack", "isDev", "getPkgManager", "require", "getBabelConfigFile", "defaultConfig", "chalk", "interopDefault", "isTTY", "process", "stdout", "turbopackGradient", "bold", "dim", "thankYouMessage", "join", "unsupportedParts", "babelrc", "basename", "hasWebpack", "hasTurbo", "unsupportedConfig", "rawNextConfig", "rawConfig", "flatten<PERSON>eys", "obj", "prefix", "keys", "key", "pre", "length", "Array", "isArray", "concat", "push", "getDeepValue", "split", "slice", "customKeys", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isSupported", "some", "<PERSON><PERSON><PERSON>", "e", "console", "error", "hasWarningOrError", "log", "feedbackMessage", "underline", "warn", "yellow", "cyan", "map", "name", "red", "pkgManager", "exit"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,gBAAgB,mBAAkB;AAEzC,SAASC,wBAAwB,QAAQ,0BAAyB;AAElE,MAAMC,sCAAsC;IAC1C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,sDAAsD;IACtD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kEAAkE;AAClE,MAAMC,qCAAqC;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,iCAAiC;AACjC,OAAO,eAAeC,wBAAwB,EAC5CC,GAAG,EACHC,iBAAiB,EACjBC,KAAK,EAQN;IACC,MAAM,EAAEC,aAAa,EAAE,GACrBC,QAAQ;IACV,MAAM,EAAEC,kBAAkB,EAAE,GAC1BD,QAAQ;IACV,MAAM,EAAEE,aAAa,EAAE,GACrBF,QAAQ;IACV,MAAMG,QACJH,QAAQ;IACV,MAAM,EAAEI,cAAc,EAAE,GACtBJ,QAAQ;IAEV,kGAAkG;IAClG,MAAMK,QAAQC,QAAQC,MAAM,CAACF,KAAK;IAElC,MAAMG,oBAAoB,CAAC,EAAEL,MAAMM,IAAI,CACrCJ,QACI,sVACA,iBACJ,CAAC,EAAEF,MAAMO,GAAG,CAAC,UAAU,IAAI,CAAC;IAE9B,IAAIC,kBACF;QACE;QACA;QACA;QACA;KACD,CAACC,IAAI,CAAC,QAAQ;IAEjB,IAAIC,mBAAmB;IACvB,IAAIC,UAAU,MAAMb,mBAAmBL;IACvC,IAAIkB,SAASA,UAAUxB,KAAKyB,QAAQ,CAACD;IAErC,IAAIE,aAAa;IACjB,IAAIC,WAAW;IAEf,IAAIC,oBAA8B,EAAE;IACpC,IAAIC,gBAA4B,CAAC;IAEjC,IAAI;QACFA,gBAAgBf,eACd,MAAMb,WAAWC,0BAA0BI,KAAK;YAC9CwB,WAAW;QACb;QAGF,IAAI,OAAOD,kBAAkB,YAAY;YACvCA,gBAAgB,AAACA,cAAsB3B,0BAA0B;gBAC/DU;YACF;QACF;QAEA,MAAMmB,cAAc,CAACC,KAAUC,SAAiB,EAAE;YAChD,IAAIC,OAAiB,EAAE;YAEvB,IAAK,MAAMC,OAAOH,IAAK;gBACrB,IAAI,QAAOA,uBAAAA,GAAK,CAACG,IAAI,MAAK,aAAa;oBACrC;gBACF;gBAEA,MAAMC,MAAMH,OAAOI,MAAM,GAAG,CAAC,EAAEJ,OAAO,CAAC,CAAC,GAAG;gBAE3C,IACE,OAAOD,GAAG,CAACG,IAAI,KAAK,YACpB,CAACG,MAAMC,OAAO,CAACP,GAAG,CAACG,IAAI,KACvBH,GAAG,CAACG,IAAI,KAAK,MACb;oBACAD,OAAOA,KAAKM,MAAM,CAACT,YAAYC,GAAG,CAACG,IAAI,EAAEC,MAAMD;gBACjD,OAAO;oBACLD,KAAKO,IAAI,CAACL,MAAMD;gBAClB;YACF;YAEA,OAAOD;QACT;QAEA,MAAMQ,eAAe,CAACV,KAAUE;YAC9B,IAAI,OAAOA,SAAS,UAAU;gBAC5BA,OAAOA,KAAKS,KAAK,CAAC;YACpB;YACA,IAAIT,KAAKG,MAAM,KAAK,GAAG;gBACrB,OAAOL,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC;YACzB;YACA,OAAOQ,aAAaV,uBAAAA,GAAK,CAACE,wBAAAA,IAAM,CAAC,EAAE,CAAC,EAAEA,KAAKU,KAAK,CAAC;QACnD;QAEA,MAAMC,aAAad,YAAYF;QAE/B,IAAIiB,gBAAgBtC,QAChB;eACKL;eACAC;SACJ,GACDD;QAEJ,KAAK,MAAMgC,OAAOU,WAAY;YAC5B,IAAIV,IAAIY,UAAU,CAAC,YAAY;gBAC7BrB,aAAa;YACf;YACA,IAAIS,IAAIY,UAAU,CAAC,uBAAuB;gBACxCpB,WAAW;YACb;YAEA,IAAIqB,cACFF,cAAcG,IAAI,CAAC,CAACC,eAAiBf,IAAIY,UAAU,CAACG,kBACpDR,aAAab,eAAeM,SAASO,aAAa9B,eAAeuB;YACnE,IAAI,CAACa,aAAa;gBAChBpB,kBAAkBa,IAAI,CAACN;YACzB;QACF;IACF,EAAE,OAAOgB,GAAG;QACVC,QAAQC,KAAK,CAAC,mDAAmDF;IACnE;IAEA,MAAMG,oBAAoB9B,WAAWI,kBAAkBS,MAAM;IAC7D,IAAI,CAACiB,mBAAmB;QACtBjC,kBAAkBR,MAAMO,GAAG,CAACC;IAC9B;IACA,IAAI,CAACd,mBAAmB;QACtB6C,QAAQG,GAAG,CAACrC,oBAAoBG;IAClC;IAEA,IAAImC,kBAAkB,CAAC,4CAA4C,EAAE3C,MAAM4C,SAAS,CAClF,sCACA,6BAA6B,EAAE5C,MAAM4C,SAAS,CAC9C,0CACA,EAAE,CAAC;IAEL,IAAI/B,cAAc,CAACC,UAAU;QAC3ByB,QAAQM,IAAI,CACV,CAAC,EAAE,EAAE7C,MAAM8C,MAAM,CACf,YACA;EACN,EAAE,CAAC,sHAAsH,CAAC,CAAC,CAAC;IAE5H;IAEA,IAAInC,SAAS;QACXD,oBAAoB,CAAC,oBAAoB,EAAEV,MAAM+C,IAAI,CACnDpC,SACA,8GAA8G,CAAC;IACnH;IAEA,IACEI,kBAAkBS,MAAM,KAAK,KAC7BT,iBAAiB,CAAC,EAAE,KAAK,uCACzB;QACAwB,QAAQM,IAAI,CACV,CAAC,EAAE,EAAE7C,MAAM8C,MAAM,CAAC,YAAY,CAAC,EAAE9C,MAAM+C,IAAI,CACzC,uCACA,uDAAuD,CAAC;IAE9D,OAAO,IAAIhC,kBAAkBS,MAAM,EAAE;QACnCd,oBAAoB,CAAC,mDAAmD,EAAEV,MAAM+C,IAAI,CAClF,kBACA,oEAAoE,EAAEhC,kBACrEiC,GAAG,CAAC,CAACC,OAAS,CAAC,MAAM,EAAEjD,MAAMkD,GAAG,CAACD,MAAM,EAAE,CAAC,EAC1CxC,IAAI,CAAC,IAAI,CAAC;IACf;IAEA,IAAIC,oBAAoB,CAAChB,mBAAmB;QAC1C,MAAMyD,aAAavD,cAAcH;QAEjC8C,QAAQC,KAAK,CACX,CAAC,4GAA4G,EAAE9B,iBAAiB;;;EAGpI,EAAEV,MAAMM,IAAI,CAACyC,IAAI,CACf,CAAC,EACCI,eAAe,QACX,wBACA,CAAC,EAAEA,WAAW,gBAAgB,CAAC,CACpC,4CAA4C,CAAC,EAC9C,6BAA6B,EAAEA,WAAW;QACtC,CAAC;QAGL,IAAI,CAACzD,mBAAmB;YACtB6C,QAAQM,IAAI,CAACF;YAEbxC,QAAQiD,IAAI,CAAC;QACf,OAAO;YACLb,QAAQM,IAAI,CAAC;YACbN,QAAQM,IAAI,CACV,CAAC,EAAE7C,MAAMM,IAAI,CAACwC,MAAM,CAClB,YACA,yEAAyE,CAAC;QAEhF;IACF;IAEA,IAAI,CAACpD,mBAAmB;QACtB6C,QAAQG,GAAG,CAACC;IACd;IAEA,OAAO3B;AACT"}