{"version": 3, "sources": ["../../../src/lib/eslint/runLintCheck.ts"], "names": ["promises", "fs", "chalk", "path", "findUp", "semver", "CommentJson", "formatResults", "writeDefaultConfig", "hasEslintConfiguration", "writeOutputFile", "ESLINT_PROMPT_VALUES", "existsSync", "findPagesDir", "installDependencies", "hasNecessaryDependencies", "Log", "isError", "getProperError", "getPkgManager", "VALID_SEVERITY", "isValidSeverity", "severity", "includes", "requiredPackages", "file", "pkg", "exportsRestrict", "cliPrompt", "console", "log", "bold", "cyan", "cliSelect", "Promise", "resolve", "require", "default", "value", "values", "valueR<PERSON><PERSON>", "title", "recommended", "selected", "name", "underline", "yellow", "unselected", "config", "lint", "baseDir", "lintDirs", "eslintrcFile", "pkgJsonPath", "lintDuringBuild", "eslintOptions", "reportErrorsOnly", "maxWarnings", "formatter", "outputFile", "mod", "ESLint", "deps", "packageManager", "missing", "some", "dep", "error", "resolved", "get", "eslintVersion", "version", "CLIEngine", "lt", "red", "options", "useEslintrc", "baseConfig", "errorOnUnmatchedPattern", "extensions", "cache", "eslint", "nextEslintPluginIsEnabled", "nextRulesEnabled", "Map", "configFile", "completeConfig", "calculateConfigForFile", "plugins", "Object", "entries", "rules", "startsWith", "length", "set", "pagesDir", "pagesDirRules", "updatedPagesDir", "rule", "replace", "warn", "lintStart", "process", "hrtime", "results", "lintFiles", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fix", "outputFixes", "getErrorResults", "loadFormatter", "formattedResult", "format", "lintEnd", "totalWarnings", "reduce", "sum", "warningCount", "output", "outputWithMessages", "eventInfo", "durationInSeconds", "lintedFilesCount", "lintFix", "nextEslintPluginVersion", "has", "join", "dirname", "nextEslintPluginErrorsCount", "totalNextPluginErrorCount", "nextEslintPluginWarningsCount", "totalNextPluginWarningCount", "fromEntries", "err", "message", "runLintCheck", "opts", "strict", "cwd", "packageJsonConfig", "pkgJsonContent", "readFile", "encoding", "parse", "exists", "emptyPkgJsonConfig", "emptyEslintrc", "selectedConfig", "find", "opt", "ready"], "mappings": "AAAA,SAASA,YAAYC,EAAE,QAAQ,KAAI;AACnC,OAAOC,WAAW,2BAA0B;AAC5C,OAAOC,UAAU,OAAM;AAEvB,OAAOC,YAAY,6BAA4B;AAC/C,OAAOC,YAAY,4BAA2B;AAC9C,YAAYC,iBAAiB,kCAAiC;AAE9D,SAAqBC,aAAa,QAAQ,oBAAmB;AAC7D,SAASC,kBAAkB,QAAQ,uBAAsB;AACzD,SAASC,sBAAsB,QAAQ,2BAA0B;AACjE,SAASC,eAAe,QAAQ,oBAAmB;AAEnD,SAASC,oBAAoB,QAAQ,eAAc;AACnD,SAASC,UAAU,EAAEC,YAAY,QAAQ,oBAAmB;AAC5D,SAASC,mBAAmB,QAAQ,0BAAyB;AAC7D,SAASC,wBAAwB,QAAQ,gCAA+B;AAExE,YAAYC,SAAS,yBAAwB;AAE7C,OAAOC,WAAWC,cAAc,QAAQ,cAAa;AACrD,SAASC,aAAa,QAAQ,6BAA4B;AAO1D,8GAA8G;AAC9G,MAAMC,iBAAiB;IAAC;IAAO;IAAQ;CAAQ;AAG/C,SAASC,gBAAgBC,QAAgB;IACvC,OAAOF,eAAeG,QAAQ,CAACD;AACjC;AAEA,MAAME,mBAAmB;IACvB;QAAEC,MAAM;QAAUC,KAAK;QAAUC,iBAAiB;IAAM;IACxD;QACEF,MAAM;QACNC,KAAK;QACLC,iBAAiB;IACnB;CACD;AAED,eAAeC;IACbC,QAAQC,GAAG,CACT5B,MAAM6B,IAAI,CACR,CAAC,EAAE7B,MAAM8B,IAAI,CACX,KACA,sFAAsF,CAAC;IAI7F,IAAI;QACF,MAAMC,YAAY,AAChB,CAAA,MAAMC,QAAQC,OAAO,CAACC,QAAQ,iCAAgC,EAC9DC,OAAO;QACT,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAML,UAAU;YAChCM,QAAQ5B;YACR6B,eAAe,CACb,EACEC,KAAK,EACLC,WAAW,EAC2C,EACxDC;gBAEA,MAAMC,OAAOD,WAAWzC,MAAM6B,IAAI,CAACc,SAAS,CAACb,IAAI,CAACS,SAASA;gBAC3D,OAAOG,OAAQF,CAAAA,cAAcxC,MAAM6B,IAAI,CAACe,MAAM,CAAC,oBAAoB,EAAC;YACtE;YACAH,UAAUzC,MAAM8B,IAAI,CAAC;YACrBe,YAAY;QACd;QAEA,OAAO;YAAEC,QAAQV,CAAAA,yBAAAA,MAAOU,MAAM,KAAI;QAAK;IACzC,EAAE,OAAM;QACN,OAAO;YAAEA,QAAQ;QAAK;IACxB;AACF;AAEA,eAAeC,KACbC,OAAe,EACfC,QAAkB,EAClBC,YAA2B,EAC3BC,WAA0B,EAC1B,EACEC,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EAQlB;IAUD,IAAI;YAuBqCC,gBA8GnCC;QApIJ,0CAA0C;QAC1C,MAAMC,OAAO,MAAM/C,yBAAyBmC,SAAS1B;QACrD,MAAMuC,iBAAiB5C,cAAc+B;QAErC,IAAIY,KAAKE,OAAO,CAACC,IAAI,CAAC,CAACC,MAAQA,IAAIxC,GAAG,KAAK,WAAW;YACpDV,IAAImD,KAAK,CACP,CAAC,wBAAwB,EACvBb,kBAAkB,oCAAoC,IACvD,CAAC,EAAEpD,MAAM6B,IAAI,CAACC,IAAI,CACjB,AAAC+B,CAAAA,mBAAmB,SAChB,mBACAA,mBAAmB,SACnB,4BACA,wBAAuB,IAAK,WAChC,CAAC;YAEL,OAAO;QACT;QAEA,MAAMH,MAAM,MAAM1B,QAAQC,OAAO,CAACC,QAAQ0B,KAAKM,QAAQ,CAACC,GAAG,CAAC;QAE5D,MAAM,EAAER,MAAM,EAAE,GAAGD;QACnB,IAAIU,gBAAgBT,CAAAA,0BAAAA,OAAQU,OAAO,OAAIX,iBAAAA,IAAIY,SAAS,qBAAbZ,eAAeW,OAAO;QAE7D,IAAI,CAACD,iBAAiBjE,OAAOoE,EAAE,CAACH,eAAe,UAAU;YACvD,OAAO,CAAC,EAAEpE,MAAMwE,GAAG,CACjB,SACA,wDAAwD,EACxDJ,gBAAgB,OAAOA,gBAAgB,MAAM,GAC9C,6CAA6C,CAAC;QACjD;QAEA,IAAIK,UAAe;YACjBC,aAAa;YACbC,YAAY,CAAC;YACbC,yBAAyB;YACzBC,YAAY;gBAAC;gBAAO;gBAAQ;gBAAO;aAAO;YAC1CC,OAAO;YACP,GAAGzB,aAAa;QAClB;QAEA,IAAI0B,SAAS,IAAIpB,OAAOc;QAExB,IAAIO,4BAA4B;QAChC,MAAMC,mBAAmB,IAAIC;QAE7B,KAAK,MAAMC,cAAc;YAACjC;YAAcC;SAAY,CAAE;gBAOhDiC;YANJ,IAAI,CAACD,YAAY;YAEjB,MAAMC,iBAAyB,MAAML,OAAOM,sBAAsB,CAChEF;YAGF,KAAIC,0BAAAA,eAAeE,OAAO,qBAAtBF,wBAAwB/D,QAAQ,CAAC,eAAe;gBAClD2D,4BAA4B;gBAC5B,KAAK,MAAM,CAACtC,MAAM,CAACtB,SAAS,CAAC,IAAImE,OAAOC,OAAO,CAACJ,eAAeK,KAAK,EAAG;oBACrE,IAAI,CAAC/C,KAAKgD,UAAU,CAAC,gBAAgB;wBACnC;oBACF;oBACA,IACE,OAAOtE,aAAa,YACpBA,YAAY,KACZA,WAAWF,eAAeyE,MAAM,EAChC;wBACAV,iBAAiBW,GAAG,CAAClD,MAAMxB,cAAc,CAACE,SAAS;oBACrD,OAAO,IACL,OAAOA,aAAa,YACpBD,gBAAgBC,WAChB;wBACA6D,iBAAiBW,GAAG,CAAClD,MAAMtB;oBAC7B;gBACF;gBACA;YACF;QACF;QAEA,MAAMyE,WAAWlF,aAAaqC,SAAS6C,QAAQ;QAC/C,MAAMC,gBAAgBD,WAAW;YAAC;SAAoC,GAAG,EAAE;QAE3E,IAAIb,2BAA2B;YAC7B,IAAIe,kBAAkB;YAEtB,KAAK,MAAMC,QAAQF,cAAe;oBAE7BrB,2BACAA;gBAFH,IACE,GAACA,4BAAAA,QAAQE,UAAU,CAAEc,KAAK,qBAAzBhB,yBAA2B,CAACuB,KAAK,KAClC,GAACvB,6BAAAA,QAAQE,UAAU,CAAEc,KAAK,qBAAzBhB,0BAA2B,CAC1BuB,KAAKC,OAAO,CAAC,cAAc,2BAC5B,GACD;oBACA,IAAI,CAACxB,QAAQE,UAAU,CAAEc,KAAK,EAAE;wBAC9BhB,QAAQE,UAAU,CAAEc,KAAK,GAAG,CAAC;oBAC/B;oBACAhB,QAAQE,UAAU,CAAEc,KAAK,CAACO,KAAK,GAAG;wBAAC;wBAAGH;qBAAS;oBAC/CE,kBAAkB;gBACpB;YACF;YAEA,IAAIA,iBAAiB;gBACnBhB,SAAS,IAAIpB,OAAOc;YACtB;QACF,OAAO;YACL3D,IAAIoF,IAAI,CAAC;YACTpF,IAAIoF,IAAI,CACN;QAEJ;QAEA,MAAMC,YAAYC,QAAQC,MAAM;QAEhC,IAAIC,UAAU,MAAMvB,OAAOwB,SAAS,CAACtD;QACrC,IAAIuD,oBAAoB;QAExB,IAAI/B,QAAQgC,GAAG,EAAE,MAAM9C,OAAO+C,WAAW,CAACJ;QAC1C,IAAIhD,kBAAkBgD,UAAU,MAAM3C,OAAOgD,eAAe,CAACL,SAAS,6CAA6C;;QAEnH,IAAI9C,WAAWgD,oBAAoB,MAAMzB,OAAO6B,aAAa,CAACpD;QAC9D,MAAMqD,kBAAkBxG,cACtB2C,SACAsD,SACAE,qCAAAA,kBAAmBM,MAAM;QAE3B,MAAMC,UAAUX,QAAQC,MAAM,CAACF;QAC/B,MAAMa,gBAAgBV,QAAQW,MAAM,CAClC,CAACC,KAAa3F,OAAqB2F,MAAM3F,KAAK4F,YAAY,EAC1D;QAGF,IAAI1D,YAAY,MAAMjD,gBAAgBiD,YAAYoD,gBAAgBO,MAAM;QAExE,OAAO;YACLA,QAAQP,gBAAgBQ,kBAAkB;YAC1CtG,SACE4C,EAAAA,0BAAAA,OAAOgD,eAAe,CAACL,6BAAvB3C,wBAAiCgC,MAAM,IAAG,KACzCpC,eAAe,KAAKyD,gBAAgBzD;YACvC+D,WAAW;gBACTC,mBAAmBR,OAAO,CAAC,EAAE;gBAC7B3C,eAAeA;gBACfoD,kBAAkBlB,QAAQX,MAAM;gBAChC8B,SAAS,CAAC,CAAChD,QAAQgC,GAAG;gBACtBiB,yBACE1C,6BAA6BpB,KAAKM,QAAQ,CAACyD,GAAG,CAAC,wBAC3CzF,QAAQjC,KAAK2H,IAAI,CACf3H,KAAK4H,OAAO,CAACjE,KAAKM,QAAQ,CAACC,GAAG,CAAC,wBAC/B,iBACCE,OAAO,GACV;gBACNyD,6BAA6BjB,gBAAgBkB,yBAAyB;gBACtEC,+BACEnB,gBAAgBoB,2BAA2B;gBAC7ChD,kBAAkBM,OAAO2C,WAAW,CAACjD;YACvC;QACF;IACF,EAAE,OAAOkD,KAAK;QACZ,IAAI/E,iBAAiB;YACnBtC,IAAImD,KAAK,CACP,CAAC,QAAQ,EACPlD,QAAQoH,QAAQA,IAAIC,OAAO,GAAGD,IAAIC,OAAO,CAACnC,OAAO,CAAC,OAAO,OAAOkC,IACjE,CAAC;YAEJ,OAAO;QACT,OAAO;YACL,MAAMnH,eAAemH;QACvB;IACF;AACF;AAEA,OAAO,eAAeE,aACpBrF,OAAe,EACfC,QAAkB,EAClBqF,IAQC;IAED,MAAM,EACJlF,kBAAkB,KAAK,EACvBC,gBAAgB,IAAI,EACpBC,mBAAmB,KAAK,EACxBC,cAAc,CAAC,CAAC,EAChBC,YAAY,IAAI,EAChBC,aAAa,IAAI,EACjB8E,SAAS,KAAK,EACf,GAAGD;IACJ,IAAI;QACF,6BAA6B;QAC7B,qGAAqG;QACrG,MAAMpF,eACJ,AAAC,MAAMhD,OACL;YACE;YACA;YACA;YACA;YACA;YACA;SACD,EACD;YACEsI,KAAKxF;QACP,MACI;QAER,MAAMG,cAAc,AAAC,MAAMjD,OAAO,gBAAgB;YAAEsI,KAAKxF;QAAQ,MAAO;QACxE,IAAIyF,oBAAoB;QACxB,IAAItF,aAAa;YACf,MAAMuF,iBAAiB,MAAM3I,GAAG4I,QAAQ,CAACxF,aAAa;gBACpDyF,UAAU;YACZ;YACAH,oBAAoBrI,YAAYyI,KAAK,CAACH;QACxC;QAEA,MAAM5F,SAAS,MAAMvC,uBAAuB2C,cAAcuF;QAC1D,IAAI7E;QAEJ,IAAId,OAAOgG,MAAM,EAAE;YACjB,8BAA8B;YAC9B,OAAO,MAAM/F,KAAKC,SAASC,UAAUC,cAAcC,aAAa;gBAC9DC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;YACF;QACF,OAAO;YACL,+DAA+D;YAC/D,4DAA4D;YAC5D,8BAA8B;YAC9B,IAAIL,iBAAiB;gBACnB,IAAIN,OAAOiG,kBAAkB,IAAIjG,OAAOkG,aAAa,EAAE;oBACrDlI,IAAIoF,IAAI,CACN,CAAC,sCAAsC,EAAElG,MAAM6B,IAAI,CAACC,IAAI,CACtD,aACA,eAAe,CAAC;gBAEtB;gBACA,OAAO;YACT,OAAO;gBACL,sFAAsF;gBACtF,MAAM,EAAEgB,QAAQmG,cAAc,EAAE,GAAGV,SAC/B9H,qBAAqByI,IAAI,CACvB,CAACC,MAA2BA,IAAI5G,KAAK,KAAK,YAE5C,MAAMb;gBAEV,IAAIuH,kBAAkB,MAAM;oBAC1B,oDAAoD;oBACpDnI,IAAIoF,IAAI,CACN;oBAEF,OAAO;gBACT,OAAO;oBACL,sEAAsE;oBACtEtC,OAAO,MAAM/C,yBAAyBmC,SAAS1B;oBAC/C,IAAIsC,KAAKE,OAAO,CAAC6B,MAAM,GAAG,GACxB,MAAM/E,oBAAoBoC,SAASY,KAAKE,OAAO,EAAE;oBAEnD,+BAA+B;oBAC/B,gFAAgF;oBAChF,IACEpD,WAAWT,KAAK2H,IAAI,CAAC5E,SAAS,aAC9BtC,WAAWT,KAAK2H,IAAI,CAAC5E,SAAS,eAC9B;wBACA,MAAM1C,mBACJ0C,SACAF,QACAmG,gBACA/F,cACAC,aACAsF;oBAEJ;gBACF;gBAEA3H,IAAIsI,KAAK,CACP,CAAC,6CAA6C,EAAEpJ,MAAM6B,IAAI,CAACC,IAAI,CAC7D,aACA,mCAAmC,CAAC;gBAGxC,OAAO;YACT;QACF;IACF,EAAE,OAAOqG,KAAK;QACZ,MAAMA;IACR;AACF"}