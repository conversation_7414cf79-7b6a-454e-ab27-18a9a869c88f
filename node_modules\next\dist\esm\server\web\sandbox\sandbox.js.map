{"version": 3, "sources": ["../../../../src/server/web/sandbox/sandbox.ts"], "names": ["getServerError", "getModuleContext", "requestToBodyStream", "NEXT_RSC_UNION_QUERY", "ErrorSource", "Symbol", "FORBIDDEN_HEADERS", "withTaggedErrors", "fn", "params", "then", "result", "waitUntil", "catch", "error", "getRuntimeContext", "runtime", "evaluateInContext", "moduleName", "name", "onWarning", "useCache", "edgeFunctionEntry", "distDir", "incrementalCache", "context", "globalThis", "__incrementalCache", "<PERSON><PERSON><PERSON><PERSON>", "paths", "run", "runWithTaggedErrors", "subreq", "request", "headers", "subrequests", "split", "includes", "Promise", "resolve", "response", "Response", "edgeFunction", "_ENTRIES", "default", "cloned", "method", "body", "cloneBodyStream", "undefined", "KUint8Array", "evaluate", "urlInstance", "URL", "url", "searchParams", "delete", "toString", "headerName", "finalize"], "mappings": "AACA,SAASA,cAAc,QAAQ,6DAA4D;AAC3F,SAASC,gBAAgB,QAAQ,YAAW;AAE5C,SAASC,mBAAmB,QAAQ,qBAAoB;AAExD,SAASC,oBAAoB,QAAQ,gDAA+C;AAEpF,OAAO,MAAMC,cAAcC,OAAO,gBAAe;AAEjD,MAAMC,oBAAoB;IACxB;IACA;IACA;CACD;AAaD;;;CAGC,GACD,SAASC,iBAAiBC,EAAY;IACpC,OAAO,CAACC,SACND,GAAGC,QACAC,IAAI,CAAC,CAACC;gBAEMA;mBAFM;gBACjB,GAAGA,MAAM;gBACTC,SAAS,EAAED,2BAAAA,oBAAAA,OAAQC,SAAS,qBAAjBD,kBAAmBE,KAAK,CAAC,CAACC;oBACnC,mGAAmG;oBACnG,MAAMd,eAAec,OAAO;gBAC9B;YACF;WACCD,KAAK,CAAC,CAACC;YACN,+CAA+C;YAC/C,MAAMd,eAAec,OAAO;QAC9B;AACN;AAEA,OAAO,eAAeC,kBAAkBN,MAQvC;IACC,MAAM,EAAEO,OAAO,EAAEC,iBAAiB,EAAE,GAAG,MAAMhB,iBAAiB;QAC5DiB,YAAYT,OAAOU,IAAI;QACvBC,WAAWX,OAAOW,SAAS,IAAK,CAAA,KAAO,CAAA;QACvCC,UAAUZ,OAAOY,QAAQ,KAAK;QAC9BC,mBAAmBb,OAAOa,iBAAiB;QAC3CC,SAASd,OAAOc,OAAO;IACzB;IAEA,IAAId,OAAOe,gBAAgB,EAAE;QAC3BR,QAAQS,OAAO,CAACC,UAAU,CAACC,kBAAkB,GAAGlB,OAAOe,gBAAgB;IACzE;IAEA,KAAK,MAAMI,aAAanB,OAAOoB,KAAK,CAAE;QACpCZ,kBAAkBW;IACpB;IACA,OAAOZ;AACT;AAEA,OAAO,MAAMc,MAAMvB,iBAAiB,eAAewB,oBAAoBtB,MAAM;QAqBvEA;IApBJ,MAAMO,UAAU,MAAMD,kBAAkBN;IACxC,MAAMuB,SAASvB,OAAOwB,OAAO,CAACC,OAAO,CAAC,CAAC,uBAAuB,CAAC,CAAC;IAChE,MAAMC,cAAc,OAAOH,WAAW,WAAWA,OAAOI,KAAK,CAAC,OAAO,EAAE;IACvE,IAAID,YAAYE,QAAQ,CAAC5B,OAAOU,IAAI,GAAG;QACrC,OAAO;YACLP,WAAW0B,QAAQC,OAAO;YAC1BC,UAAU,IAAIxB,QAAQS,OAAO,CAACgB,QAAQ,CAAC,MAAM;gBAC3CP,SAAS;oBACP,qBAAqB;gBACvB;YACF;QACF;IACF;IAEA,MAAMQ,eAGJ1B,QAAQS,OAAO,CAACkB,QAAQ,CAAC,CAAC,WAAW,EAAElC,OAAOU,IAAI,CAAC,CAAC,CAAC,CAACyB,OAAO;IAE/D,MAAMC,SAAS,CAAC;QAAC;QAAQ;KAAM,CAACR,QAAQ,CAAC5B,OAAOwB,OAAO,CAACa,MAAM,KAC1DrC,uBAAAA,OAAOwB,OAAO,CAACc,IAAI,qBAAnBtC,qBAAqBuC,eAAe,KACpCC;IAEJ,MAAMC,cAAclC,QAAQmC,QAAQ,CAAC;IACrC,MAAMC,cAAc,IAAIC,IAAI5C,OAAOwB,OAAO,CAACqB,GAAG;IAC9CF,YAAYG,YAAY,CAACC,MAAM,CAACrD;IAEhCM,OAAOwB,OAAO,CAACqB,GAAG,GAAGF,YAAYK,QAAQ;IAEzC,IAAI;QACF,MAAM9C,SAAS,MAAM+B,aAAa;YAChCT,SAAS;gBACP,GAAGxB,OAAOwB,OAAO;gBACjBc,MACEF,UAAU3C,oBAAoBc,QAAQS,OAAO,EAAEyB,aAAaL;YAChE;QACF;QACA,KAAK,MAAMa,cAAcpD,kBAAmB;YAC1CK,OAAO6B,QAAQ,CAACN,OAAO,CAACsB,MAAM,CAACE;QACjC;QACA,OAAO/C;IACT,SAAU;YACFF;QAAN,QAAMA,wBAAAA,OAAOwB,OAAO,CAACc,IAAI,qBAAnBtC,sBAAqBkD,QAAQ;IACrC;AACF,GAAE"}