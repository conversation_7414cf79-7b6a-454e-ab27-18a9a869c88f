'use client';

import { useState, useEffect, useRef } from 'react';
import { 
  <PERSON>, 
  Pause, 
  RotateCcw, 
  <PERSON><PERSON><PERSON>or<PERSON>, 
  SkipBack,
  Volume2,
  VolumeX,
  Maximize2,
  X,
  ArrowRight,
  FileText,
  Eye,
  Download,
  <PERSON><PERSON>,
  Settings,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { useTranslation } from '@/hooks/useTranslation';

interface DemoStep {
  id: number;
  title: string;
  description: string;
  duration: number;
  action: string;
  markdown: string;
  highlight?: string;
}



export default function DemoPlayer() {
  const { t } = useTranslation('demo-player');

  // Generate demo steps from translations
  const generateDemoSteps = (): DemoStep[] => {
    const stepsData = t('steps', { returnObjects: true });
    const steps = Array.isArray(stepsData) ? stepsData as Array<{title: string, description: string, action: string}> : [];

    const markdownContent = [
      "",
      "# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.",
      "# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)",
      "# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)\n\n### Code Example\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```",
      "# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)\n\n### Code Example\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```\n\n### Comparison Table\n\n| Feature | Free | Premium |\n|---------|------|--------|\n| Real-time Preview | ✅ | ✅ |\n| Export Options | ✅ | ✅ |\n| Advanced Themes | ❌ | ✅ |",
      "# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)\n\n### Code Example\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```\n\n### Comparison Table\n\n| Feature | Free | Premium |\n|---------|------|--------|\n| Real-time Preview | ✅ | ✅ |\n| Export Options | ✅ | ✅ |\n| Advanced Themes | ❌ | ✅ |\n\n---\n\n**Ready to start?** Click the button below to begin editing!"
    ];

    const highlights = ["interface", "editing", "formatting", "code", "tables", "export"];
    const durations = [3000, 4000, 4000, 5000, 4000, 3000];

    return steps.map((step, index) => ({
      id: index + 1,
      title: step.title,
      description: step.description,
      duration: durations[index] || 3000,
      action: step.action,
      markdown: markdownContent[index] || "",
      highlight: highlights[index]
    }));
  };

  const [demoSteps, setDemoSteps] = useState<DemoStep[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [displayedMarkdown, setDisplayedMarkdown] = useState('');
  const [typingProgress, setTypingProgress] = useState(0);
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const typingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize demo steps when component mounts
  useEffect(() => {
    setDemoSteps(generateDemoSteps());
  }, []);

  // Ensure currentStep is within bounds and get current step data
  const safeCurrentStep = Math.max(0, Math.min(currentStep, demoSteps.length - 1));
  const currentStepData = demoSteps[safeCurrentStep] || demoSteps[0];
  const totalDuration = demoSteps.reduce((sum, step) => sum + step.duration, 0);

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && safeCurrentStep < demoSteps.length && currentStepData) {
      const stepDuration = currentStepData.duration;
      const updateInterval = 50; // Update every 50ms for smooth progress
      
      intervalRef.current = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + (updateInterval / stepDuration) * 100;
          if (newProgress >= 100) {
            // Move to next step
            if (safeCurrentStep < demoSteps.length - 1) {
              setCurrentStep(prev => prev + 1);
              return 0;
            } else {
              setIsPlaying(false);
              return 100;
            }
          }
          return newProgress;
        });
      }, updateInterval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying, safeCurrentStep, currentStepData]);

  // Typing animation effect
  useEffect(() => {
    if (isPlaying && currentStepData) {
      const targetMarkdown = currentStepData.markdown;
      const typingSpeed = currentStepData.duration / Math.max(targetMarkdown.length, 1);
      
      setDisplayedMarkdown('');
      setTypingProgress(0);
      
      typingIntervalRef.current = setInterval(() => {
        setTypingProgress(prev => {
          const newProgress = prev + 1;
          if (newProgress <= targetMarkdown.length) {
            setDisplayedMarkdown(targetMarkdown.slice(0, newProgress));
            return newProgress;
          } else {
            if (typingIntervalRef.current) {
              clearInterval(typingIntervalRef.current);
            }
            return prev;
          }
        });
      }, typingSpeed);
    } else if (currentStepData) {
      setDisplayedMarkdown(currentStepData.markdown);
      if (typingIntervalRef.current) {
        clearInterval(typingIntervalRef.current);
      }
    }

    return () => {
      if (typingIntervalRef.current) {
        clearInterval(typingIntervalRef.current);
      }
    };
  }, [safeCurrentStep, isPlaying, currentStepData]);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const restart = () => {
    setCurrentStep(0);
    setProgress(0);
    setIsPlaying(false);
    setDisplayedMarkdown(demoSteps[0]?.markdown || '');
  };

  const nextStep = () => {
    if (safeCurrentStep < demoSteps.length - 1) {
      setCurrentStep(prev => prev + 1);
      setProgress(0);
    }
  };

  const prevStep = () => {
    if (safeCurrentStep > 0) {
      setCurrentStep(prev => prev - 1);
      setProgress(0);
    }
  };

  const goToStep = (stepIndex: number) => {
    const validIndex = Math.max(0, Math.min(stepIndex, demoSteps.length - 1));
    setCurrentStep(validIndex);
    setProgress(0);
    setIsPlaying(false);
  };

  // Convert markdown to HTML (simplified for demo)
  const markdownToHtml = (markdown: string) => {
    if (!markdown) return '';
    
    return markdown
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*)\*/gim, '<em>$1</em>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      .replace(/^- (.*$)/gim, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code>$2</code></pre>')
      .replace(/`([^`]+)`/gim, '<code>$1</code>')
      .replace(/^\| (.*) \|$/gim, '<tr><td>$1</td></tr>')
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
      .replace(/\n/gim, '<br>');
  };

  // Early return if no steps available
  if (!demoSteps.length || !currentStepData) {
    return (
      <div className="min-h-screen bg-gray-900 text-white flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">{t('errors.notAvailable')}</h2>
          <p className="text-gray-400 mb-6">{t('errors.contentUnavailable')}</p>
          <Link 
            href="/editor"
            className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Try Editor Instead
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'min-h-screen'} bg-gray-900 text-white`}>
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Play className="h-6 w-6 text-blue-400" />
              <h1 className="text-xl font-bold">{t('title')}</h1>
            </div>
            <span className="text-gray-400 text-sm">
              {t('subtitle')}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              title={t('controls.fullscreen')}
            >
              {isFullscreen ? <X className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
            </button>
            
            <Link 
              href="/editor"
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              {t('controls.tryNow')}
            </Link>
          </div>
        </div>
      </div>

      {/* Demo Content */}
      <div className="flex flex-1 h-full">
        {/* Demo Player */}
        <div className="flex-1 flex flex-col">
          {/* Step Info */}
          <div className="bg-gray-800 border-b border-gray-700 px-6 py-4">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-lg font-semibold">{currentStepData.title}</h2>
              <span className="text-sm text-gray-400">
                {t('progress.step')} {safeCurrentStep + 1} {t('progress.of')} {demoSteps.length}
              </span>
            </div>
            <p className="text-gray-300 text-sm mb-3">{currentStepData.description}</p>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-700 rounded-full h-2 mb-3">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-100"
                style={{ width: `${progress}%` }}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-400">{currentStepData.action}</span>
              <span className="text-xs text-gray-400">
                {Math.round((currentStepData.duration * progress) / 100 / 1000)}s / {currentStepData.duration / 1000}s
              </span>
            </div>
          </div>

          {/* Editor Simulation */}
          <div className="flex-1 flex">
            {/* Editor Panel */}
            <div className="w-1/2 flex flex-col border-r border-gray-700">
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-blue-400" />
                  <span className="text-sm font-medium">{t('panels.markdownEditor')}</span>
                </div>
                <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-400 animate-pulse' : 'bg-gray-500'}`} />
              </div>
              
              <div className="flex-1 p-4 bg-gray-900 font-mono text-sm overflow-auto">
                <pre className="whitespace-pre-wrap text-gray-100">
                  {displayedMarkdown}
                  {isPlaying && typingProgress < currentStepData.markdown.length && (
                    <span className="animate-pulse">|</span>
                  )}
                </pre>
              </div>
            </div>

            {/* Preview Panel */}
            <div className="w-1/2 flex flex-col">
              <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center space-x-2">
                <Eye className="h-4 w-4 text-green-400" />
                <span className="text-sm font-medium">{t('panels.livePreview')}</span>
              </div>
              
              <div className="flex-1 p-4 bg-white text-gray-900 overflow-auto">
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: markdownToHtml(displayedMarkdown) }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
          {/* Controls */}
          <div className="p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold mb-4">{t('controls.title')}</h3>
            
            <div className="flex items-center justify-center space-x-3 mb-6">
              <button
                onClick={prevStep}
                disabled={safeCurrentStep === 0}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title={t('controls.previous')}
              >
                <SkipBack className="h-5 w-5" />
              </button>
              
              <button
                onClick={togglePlay}
                className="p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                title={isPlaying ? t('controls.pause') : t('controls.play')}
              >
                {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
              </button>
              
              <button
                onClick={nextStep}
                disabled={safeCurrentStep === demoSteps.length - 1}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title={t('controls.next')}
              >
                <SkipForward className="h-5 w-5" />
              </button>
              
              <button
                onClick={restart}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title={t('controls.restart')}
              >
                <RotateCcw className="h-5 w-5" />
              </button>
            </div>

            <div className="flex items-center justify-center space-x-3">
              <button
                onClick={() => setIsMuted(!isMuted)}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                title={isMuted ? t('controls.unmute') : t('controls.mute')}
              >
                {isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
              </button>
            </div>
          </div>

          {/* Step Navigation */}
          <div className="flex-1 p-6">
            <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-4">
              {t('navigation.steps')}
            </h4>
            
            <div className="space-y-2">
              {demoSteps.map((step, index) => (
                <button
                  key={step.id}
                  onClick={() => goToStep(index)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    index === safeCurrentStep 
                      ? 'bg-blue-600 text-white' 
                      : 'hover:bg-gray-700 text-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium">{step.title}</span>
                    <span className="text-xs opacity-75">{step.duration / 1000}s</span>
                  </div>
                  <p className="text-xs opacity-75 line-clamp-2">{step.description}</p>
                </button>
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="p-6 border-t border-gray-700">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 text-center">
              <Zap className="h-8 w-8 mx-auto mb-2 text-yellow-300" />
              <h4 className="font-semibold mb-2">{t('cta.title')}</h4>
              <p className="text-sm opacity-90 mb-3">
                {t('cta.description')}
              </p>
              <Link 
                href="/editor"
                className="inline-flex items-center bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
              >
                {t('cta.button')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}