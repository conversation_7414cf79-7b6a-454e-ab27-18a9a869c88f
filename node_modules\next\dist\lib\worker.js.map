{"version": 3, "sources": ["../../src/lib/worker.ts"], "names": ["Worker", "RESTARTED", "Symbol", "cleanupWorkers", "worker", "cur<PERSON><PERSON><PERSON>", "_workerPool", "_workers", "_child", "kill", "constructor", "worker<PERSON><PERSON>", "options", "timeout", "onRestart", "farmOptions", "restartPromise", "resolveRestartPromise", "activeTasks", "_worker", "undefined", "createWorker", "JestWorker", "forkOptions", "env", "process", "NODE_OPTIONS", "getNodeOptionsWithoutInspect", "replace", "trim", "Promise", "resolve", "enableWorkerThreads", "on", "code", "signal", "console", "error", "getStdout", "pipe", "stdout", "getStderr", "stderr", "onHanging", "end", "then", "hanging<PERSON><PERSON>r", "onActivity", "clearTimeout", "setTimeout", "method", "exposedMethods", "startsWith", "args", "attempts", "result", "race", "bind", "Error", "close"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAdwB;uBACQ;AAG7C,MAAMC,YAAYC,OAAO;AAEzB,MAAMC,iBAAiB,CAACC;QACG;IAAzB,KAAK,MAAMC,aAAc,EAAA,sBAAA,AAACD,OAAeE,WAAW,qBAA3B,oBAA6BC,QAAQ,KAAI,EAAE,CAE/D;YACHF;SAAAA,oBAAAA,UAAUG,MAAM,qBAAhBH,kBAAkBI,IAAI,CAAC;IACzB;AACF;AAEO,MAAMT;IAGXU,YACEC,UAAkB,EAClBC,OAKC,CACD;QACA,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAE,GAAGC,aAAa,GAAGH;QAE7C,IAAII;QACJ,IAAIC;QACJ,IAAIC,cAAc;QAElB,IAAI,CAACC,OAAO,GAAGC;QAEf,MAAMC,eAAe;gBAMRN;YALX,IAAI,CAACI,OAAO,GAAG,IAAIG,kBAAU,CAACX,YAAY;gBACxC,GAAGI,WAAW;gBACdQ,aAAa;oBACX,GAAGR,YAAYQ,WAAW;oBAC1BC,KAAK;wBACH,GAAKT,EAAAA,2BAAAA,YAAYQ,WAAW,qBAAvBR,yBAAyBS,GAAG,KAAI,CAAC,CAAC;wBACvC,GAAGC,QAAQD,GAAG;wBACd,4CAA4C;wBAC5C,qBAAqB;wBACrBE,cAAcC,IAAAA,mCAA4B,IACvCC,OAAO,CAAC,iCAAiC,IACzCC,IAAI;oBACT;gBACF;YACF;YACAb,iBAAiB,IAAIc,QACnB,CAACC,UAAad,wBAAwBc;YAGxC;;;;;;;;OAQC,GACD,IAAI,CAAChB,YAAYiB,mBAAmB,EAAE;oBACd;gBAAtB,KAAK,MAAM5B,UAAW,EAAA,4BAAA,AAAC,IAAI,CAACe,OAAO,CAASb,WAAW,qBAAjC,0BAAmCC,QAAQ,KAC/D,EAAE,CAEC;wBACHH;qBAAAA,iBAAAA,OAAOI,MAAM,qBAAbJ,eAAe6B,EAAE,CAAC,QAAQ,CAACC,MAAMC;wBAC/B,8CAA8C;wBAC9C,IAAI,AAACD,CAAAA,QAAQC,MAAK,KAAM,IAAI,CAAChB,OAAO,EAAE;4BACpCiB,QAAQC,KAAK,CACX,CAAC,6CAA6C,EAAEH,KAAK,aAAa,EAAEC,OAAO,CAAC;wBAEhF;oBACF;gBACF;YACF;YAEA,IAAI,CAAChB,OAAO,CAACmB,SAAS,GAAGC,IAAI,CAACd,QAAQe,MAAM;YAC5C,IAAI,CAACrB,OAAO,CAACsB,SAAS,GAAGF,IAAI,CAACd,QAAQiB,MAAM;QAC9C;QACArB;QAEA,MAAMsB,YAAY;YAChB,MAAMvC,SAAS,IAAI,CAACe,OAAO;YAC3B,IAAI,CAACf,QAAQ;YACb,MAAM2B,UAAUd;YAChBI;YACAjB,OAAOwC,GAAG,GAAGC,IAAI,CAAC;gBAChBd,QAAQ9B;YACV;QACF;QAEA,IAAI6C,eAAuC;QAE3C,MAAMC,aAAa;YACjB,IAAID,cAAcE,aAAaF;YAC/BA,eAAe5B,cAAc,KAAK+B,WAAWN,WAAW9B;QAC1D;QAEA,KAAK,MAAMqC,UAAUnC,YAAYoC,cAAc,CAAE;YAC/C,IAAID,OAAOE,UAAU,CAAC,MAAM;YAC3B,AAAC,IAAI,AAAQ,CAACF,OAAO,GAAGrC,UAErB,OAAO,GAAGwC;gBACRnC;gBACA,IAAI;oBACF,IAAIoC,WAAW;oBACf,OAAS;wBACPP;wBACA,MAAMQ,SAAS,MAAMzB,QAAQ0B,IAAI,CAAC;4BAC/B,IAAI,CAACrC,OAAO,AAAQ,CAAC+B,OAAO,IAAIG;4BACjCrC;yBACD;wBACD,IAAIuC,WAAWtD,WAAW,OAAOsD;wBACjC,IAAIzC,WAAWA,UAAUoC,QAAQG,MAAM,EAAEC;oBAC3C;gBACF,SAAU;oBACRpC;oBACA6B;gBACF;YACF,IACA,AAAC,IAAI,CAAC5B,OAAO,AAAQ,CAAC+B,OAAO,CAACO,IAAI,CAAC,IAAI,CAACtC,OAAO;QACrD;IACF;IAEAyB,MAAqC;QACnC,MAAMxC,SAAS,IAAI,CAACe,OAAO;QAC3B,IAAI,CAACf,QAAQ;YACX,MAAM,IAAIsD,MAAM;QAClB;QACAvD,eAAeC;QACf,IAAI,CAACe,OAAO,GAAGC;QACf,OAAOhB,OAAOwC,GAAG;IACnB;IAEA;;GAEC,GACDe,QAAc;QACZ,IAAI,IAAI,CAACxC,OAAO,EAAE;YAChBhB,eAAe,IAAI,CAACgB,OAAO;YAC3B,IAAI,CAACA,OAAO,CAACyB,GAAG;QAClB;IACF;AACF"}