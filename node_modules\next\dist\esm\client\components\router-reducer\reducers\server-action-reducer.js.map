{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["callServer", "ACTION", "NEXT_ROUTER_STATE_TREE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "createRecordFromThenable", "readRecordValue", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "addBasePath", "createHrefFromUrl", "handleExternalUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "CacheStates", "handleMutable", "fillLazyItemsTillLeafWithHead", "fetchServerAction", "state", "actionId", "actionArgs", "body", "res", "fetch", "method", "headers", "Accept", "encodeURIComponent", "JSON", "stringify", "tree", "__NEXT_ACTIONS_DEPLOYMENT_ID", "NEXT_DEPLOYMENT_ID", "nextUrl", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "actionFlightData", "actionResult", "serverActionReducer", "action", "mutable", "cache", "reject", "currentTree", "isForCurrentTree", "previousTree", "inFlightServerAction", "globalMutable", "pendingNavigatePath", "then", "actionResultResolved", "refresh", "flightData", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "subTreeData", "head", "slice", "status", "READY", "prefetchCache", "Map", "patchedTree", "newHref", "value"], "mappings": "AAKA,SAASA,UAAU,QAAQ,2BAA0B;AACrD,SACEC,MAAM,EACNC,sBAAsB,EACtBC,QAAQ,EACRC,uBAAuB,QAClB,2BAA0B;AACjC,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,eAAe,QAAQ,uBAAsB;AACtD,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAQd,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,WAAW,QAAQ,2DAA0D;AACtF,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,6BAA6B,QAAQ,yCAAwC;AAatF,eAAeC,kBACbC,KAA2B,EAC3B,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMjB,YAAYgB;IAE/B,MAAME,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQ1B;YACR,CAACH,OAAO,EAAEsB;YACV,CAACrB,uBAAuB,EAAE6B,mBAAmBC,KAAKC,SAAS,CAACX,MAAMY,IAAI;YACtE,GAAIzB,QAAQC,GAAG,CAACyB,4BAA4B,IAC5C1B,QAAQC,GAAG,CAAC0B,kBAAkB,GAC1B;gBACE,mBAAmB3B,QAAQC,GAAG,CAAC0B,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAId,MAAMe,OAAO,GACb;gBACE,CAAClC,SAAS,EAAEmB,MAAMe,OAAO;YAC3B,IACA,CAAC,CAAC;QACR;QACAZ;IACF;IAEA,MAAMa,WAAWZ,IAAIG,OAAO,CAACU,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBT,KAAKU,KAAK,CAClChB,IAAIG,OAAO,CAACU,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFnC,YAAYyB,WACZ,sFAAsF;IACtF,IAAIU,IAAI1B,MAAM2B,YAAY,EAAEC,OAAOZ,QAAQ,CAACa,IAAI,KAElDC;IAEJ,IAAIC,mBACF3B,IAAIG,OAAO,CAACU,GAAG,CAAC,oBAAoBnC;IAEtC,IAAIiD,kBAAkB;QACpB,MAAMC,WAAiC,MAAM/C,gBAC3CgD,QAAQC,OAAO,CAAC9B,MAChB;YACE1B;QACF;QAGF,IAAIsC,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGmB,iBAAiB,GAAG,AAACH,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLG,kBAAkBA;gBAClBV;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACkB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACH,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLI;YACAD;YACAV;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAEA;;;CAGC,GACD,OAAO,SAASmB,oBACdrC,KAA2B,EAC3BsC,MAA0B;IAE1B,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEN,OAAO,EAAEO,MAAM,EAAE,GAAGH;IAC5C,MAAMT,OAAO7B,MAAM2B,YAAY;IAE/B,IAAIe,cAAc1C,MAAMY,IAAI;IAE5B,MAAM+B,mBACJjC,KAAKC,SAAS,CAAC4B,QAAQK,YAAY,MAAMlC,KAAKC,SAAS,CAAC+B;IAE1D,IAAIC,kBAAkB;QACpB,OAAO9C,cAAcG,OAAOuC;IAC9B;IAEA,IAAIA,QAAQM,oBAAoB,EAAE;QAChC,8CAA8C;QAC9C,qCAAqC;QACrC,IACEN,QAAQO,aAAa,CAACC,mBAAmB,IACzCR,QAAQO,aAAa,CAACC,mBAAmB,KAAKlB,MAC9C;YACAU,QAAQM,oBAAoB,CAACG,IAAI,CAAC;gBAChC,IAAIT,QAAQU,oBAAoB,EAAE;gBAElC,+DAA+D;gBAC/D,2FAA2F;gBAC3FV,QAAQM,oBAAoB,GAAG;gBAC/BN,QAAQO,aAAa,CAACC,mBAAmB,GAAGjB;gBAC5CS,QAAQO,aAAa,CAACI,OAAO;gBAC7BX,QAAQU,oBAAoB,GAAG;YACjC;YAEA,OAAOjD;QACT;IACF,OAAO;QACLuC,QAAQM,oBAAoB,GAAG9D,yBAC7BgB,kBAAkBC,OAAOsC;IAE7B;IAEA,iHAAiH;IACjH,IAAI;QACF,gDAAgD;QAChD,MAAM,EACJF,YAAY,EACZD,kBAAkBgB,UAAU,EAC5B1B,gBAAgB,EAEjB,GAAGzC,gBACFuD,QAAQM,oBAAoB;QAG9B,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIpB,kBAAkB;YACpBzB,MAAMoD,OAAO,CAACC,WAAW,GAAG;YAC5Bd,QAAQc,WAAW,GAAG;QACxB;QAEAd,QAAQK,YAAY,GAAG5C,MAAMY,IAAI;QAEjC,IAAI,CAACuC,YAAY;YACf,IAAI,CAACZ,QAAQU,oBAAoB,EAAE;gBACjCf,QAAQE;gBACRG,QAAQU,oBAAoB,GAAG;YACjC;YAEA,2EAA2E;YAC3E,IAAIxB,kBAAkB;gBACpB,OAAOhC,kBACLO,OACAuC,SACAd,iBAAiBI,IAAI,EACrB7B,MAAMoD,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOrD;QACT;QAEA,IAAI,OAAOmD,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAO1D,kBACLO,OACAuC,SACAY,YACAnD,MAAMoD,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3Dd,QAAQM,oBAAoB,GAAG;QAE/B,KAAK,MAAMS,kBAAkBH,WAAY;YACvC,oFAAoF;YACpF,IAAIG,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOzD;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC0D,UAAU,GAAGJ;YACpB,MAAMK,UAAUjE,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJgD,aACAgB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIC,MAAM;YAClB;YAEA,IAAIjE,4BAA4B+C,aAAaiB,UAAU;gBACrD,OAAOlE,kBACLO,OACAuC,SACAV,MACA7B,MAAMoD,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACQ,aAAaC,KAAK,GAAGR,eAAeS,KAAK,CAAC,CAAC;YAElD,8FAA8F;YAC9F,IAAIF,gBAAgB,MAAM;gBACxBrB,MAAMwB,MAAM,GAAGpE,YAAYqE,KAAK;gBAChCzB,MAAMqB,WAAW,GAAGA;gBACpB/D,8BACE0C,OACA,4FAA4F;gBAC5FV,WACA4B,WACAI;gBAEFvB,QAAQC,KAAK,GAAGA;gBAChBD,QAAQ2B,aAAa,GAAG,IAAIC;YAC9B;YAEA5B,QAAQK,YAAY,GAAGF;YACvBH,QAAQ6B,WAAW,GAAGT;YACtBpB,QAAQZ,YAAY,GAAGE;YAEvBa,cAAciB;QAChB;QAEA,IAAIlC,kBAAkB;YACpB,MAAM4C,UAAU7E,kBAAkBiC,kBAAkB;YACpDc,QAAQZ,YAAY,GAAG0C;QACzB;QAEA,IAAI,CAAC9B,QAAQU,oBAAoB,EAAE;YACjCf,QAAQE;YACRG,QAAQU,oBAAoB,GAAG;QACjC;QACA,OAAOpD,cAAcG,OAAOuC;IAC9B,EAAE,OAAOf,GAAQ;QACf,IAAIA,EAAEwC,MAAM,KAAK,YAAY;YAC3B,IAAI,CAACzB,QAAQU,oBAAoB,EAAE;gBACjCR,OAAOjB,EAAE8C,KAAK;gBACd/B,QAAQU,oBAAoB,GAAG;YACjC;YAEA,mHAAmH;YACnH,OAAOjD;QACT;QAEA,MAAMwB;IACR;AACF"}