/// <reference types="node" />
import type { FontManifest, FontConfig } from '../server/font-utils';
import type { DomainLocale, ExportPathMap, NextConfigComplete } from '../server/config-shared';
import type { OutgoingHttpHeaders } from 'http';
import '../server/node-polyfill-fetch';
import '../server/node-environment';
import AmpHtmlValidator from 'next/dist/compiled/amphtml-validator';
import { IncrementalCache } from '../server/lib/incremental-cache';
interface AmpValidation {
    page: string;
    result: {
        errors: AmpHtmlValidator.ValidationError[];
        warnings: AmpHtmlValidator.ValidationError[];
    };
}
type PathMap = ExportPathMap[keyof ExportPathMap];
interface ExportPageInput {
    path: string;
    pathMap: PathMap;
    distDir: string;
    outDir: string;
    pagesDataDir: string;
    renderOpts: RenderOpts;
    buildExport?: boolean;
    serverRuntimeConfig: {
        [key: string]: any;
    };
    subFolders?: boolean;
    optimizeFonts: FontConfig;
    optimizeCss: any;
    disableOptimizedLoading: any;
    parentSpanId: any;
    httpAgentOptions: NextConfigComplete['httpAgentOptions'];
    debugOutput?: boolean;
    isrMemoryCacheSize?: NextConfigComplete['experimental']['isrMemoryCacheSize'];
    fetchCache?: boolean;
    incrementalCacheHandlerPath?: string;
    fetchCacheKeyPrefix?: string;
    nextConfigOutput?: NextConfigComplete['output'];
    enableExperimentalReact?: boolean;
}
interface ExportPageResults {
    ampValidations: AmpValidation[];
    fromBuildExportRevalidate?: number | false;
    fromBuildExportMeta?: {
        status?: number;
        headers?: OutgoingHttpHeaders;
    };
    error?: boolean;
    ssgNotFound?: boolean;
    duration: number;
}
interface RenderOpts {
    runtimeConfig?: {
        [key: string]: any;
    };
    params?: {
        [key: string]: string | string[];
    };
    ampPath?: string;
    ampValidatorPath?: string;
    ampSkipValidation?: boolean;
    optimizeFonts?: FontConfig;
    disableOptimizedLoading?: boolean;
    optimizeCss?: any;
    fontManifest?: FontManifest;
    locales?: string[];
    locale?: string;
    defaultLocale?: string;
    domainLocales?: DomainLocale[];
    trailingSlash?: boolean;
    supportsDynamicHTML?: boolean;
    incrementalCache?: IncrementalCache;
    strictNextHead?: boolean;
    originalPathname?: string;
    deploymentId?: string;
}
export default function exportPage({ parentSpanId, path, pathMap, distDir, outDir, pagesDataDir, renderOpts, buildExport, serverRuntimeConfig, subFolders, optimizeFonts, optimizeCss, disableOptimizedLoading, httpAgentOptions, debugOutput, isrMemoryCacheSize, fetchCache, fetchCacheKeyPrefix, incrementalCacheHandlerPath, enableExperimentalReact, }: ExportPageInput): Promise<ExportPageResults>;
export {};
