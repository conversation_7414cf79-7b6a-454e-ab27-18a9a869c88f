'use client';

import { Check, X, Star, Crown, Shield } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export default function ComparisonSection() {
  const { t } = useTranslation('comparison');
  // Get data from translations
  const featuresData = t('features', { returnObjects: true });
  const features = Array.isArray(featuresData) ? featuresData as string[] : [];

  const competitorsData = t('competitors', { returnObjects: true });
  const competitorsBase = Array.isArray(competitorsData) ? competitorsData as Array<{name: string, price: string, highlight: boolean}> : [];

  // Add feature arrays to competitors
  const competitors = competitorsBase.map((competitor, index) => ({
    ...competitor,
    features: index === 0
      ? [true, true, true, true, true, true, true, true, true, true, true, true, true, true, true]
      : index === 1
      ? [true, false, false, false, true, true, false, false, true, false, true, false, false, false, true]
      : index === 2
      ? [false, false, false, true, true, true, true, false, true, true, true, true, false, false, true]
      : [true, false, false, true, true, true, false, true, true, true, true, true, false, true, true]
  }));

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        <div className="overflow-x-auto mb-16">
          <div className="min-w-full bg-white rounded-2xl shadow-sm border border-gray-200">
            <div className="grid grid-cols-5 gap-0">
              <div className="p-6 border-b border-gray-200">
                <h3 className="font-semibold text-gray-900">{t('labels.features')}</h3>
              </div>
              {competitors.map((competitor, index) => (
                <div key={index} className={`p-6 border-b border-gray-200 text-center ${competitor.highlight ? 'bg-blue-50' : ''}`}>
                  <div className="flex items-center justify-center mb-2">
                    {competitor.highlight && <Crown className="h-5 w-5 text-yellow-500 mr-1" />}
                    <h3 className={`font-semibold ${competitor.highlight ? 'text-blue-900' : 'text-gray-900'}`}>
                      {competitor.name}
                    </h3>
                  </div>
                  <div className={`text-sm ${competitor.highlight ? 'text-blue-600 font-semibold' : 'text-gray-600'}`}>
                    {competitor.price}
                  </div>
                </div>
              ))}
              
              {features.map((feature, featureIndex) => (
                <>
                  <div key={`feature-${featureIndex}`} className="p-4 border-b border-gray-100">
                    <span className="text-sm text-gray-700">{feature}</span>
                  </div>
                  {competitors.map((competitor, compIndex) => (
                    <div key={`${featureIndex}-${compIndex}`} className={`p-4 border-b border-gray-100 text-center ${competitor.highlight ? 'bg-blue-50' : ''}`}>
                      {competitor.features[featureIndex] ? (
                        <Check className="h-5 w-5 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-5 w-5 text-red-500 mx-auto" />
                      )}
                    </div>
                  ))}
                </>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
            <div className="text-center mb-6">
              <div className="bg-red-100 p-4 rounded-full inline-block mb-4">
                <X className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{t('comparison.traditional.title')}</h3>
              <p className="text-gray-600">{t('comparison.traditional.subtitle')}</p>
            </div>
            <ul className="space-y-3">
              {(() => {
                const limitations = t('sections.traditional.limitations', { returnObjects: true });
                const items = Array.isArray(limitations) ? limitations as string[] : [];
                return items.map((limitation, index) => (
                  <li key={index} className="flex items-start">
                    <X className="h-5 w-5 text-red-500 mr-2 mt-0.5" />
                    <span className="text-gray-600">{limitation}</span>
                  </li>
                ));
              })()}
            </ul>
          </div>

          <div className="bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl p-8 text-white relative overflow-hidden">
            <div className="absolute top-4 right-4">
              <Crown className="h-8 w-8 text-yellow-300" />
            </div>
            <div className="text-center mb-6">
              <div className="bg-white/20 p-4 rounded-full inline-block mb-4">
                <Star className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">{t('sections.ourSolution.title')}</h3>
              <p className="text-blue-100">{t('sections.ourSolution.subtitle')}</p>
            </div>
            <ul className="space-y-3">
              {(() => {
                const benefits = t('sections.ourSolution.benefits', { returnObjects: true });
                const items = Array.isArray(benefits) ? benefits as string[] : [];
                return items.map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-green-300 mr-2 mt-0.5" />
                    <span>{benefit}</span>
                  </li>
                ));
              })()}
            </ul>
          </div>

          <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
            <div className="text-center mb-6">
              <div className="bg-green-100 p-4 rounded-full inline-block mb-4">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{t('sections.advantages.title')}</h3>
              <p className="text-gray-600">{t('sections.advantages.subtitle')}</p>
            </div>
            <ul className="space-y-3">
              {(() => {
                const points = t('sections.advantages.points', { returnObjects: true });
                const items = Array.isArray(points) ? points as string[] : [];
                return items.map((point, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5" />
                    <span className="text-gray-600">{point}</span>
                  </li>
                ));
              })()}
            </ul>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl p-12 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">
            {t('cta.title')}
          </h3>
          <p className="text-lg opacity-90 mb-8 max-w-2xl mx-auto">
            {t('cta.description')}
          </p>
          <button className="bg-white text-blue-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            {t('cta.button')}
          </button>
        </div>
      </div>
    </section>
  );
}