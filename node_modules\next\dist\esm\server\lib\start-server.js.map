{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "names": ["fs", "path", "http", "https", "Watchpack", "Log", "setupDebug", "getDebugPort", "formatHostname", "initialize", "RESTART_EXIT_CODE", "checkIsNodeDebugging", "CONFIG_FILES", "chalk", "debug", "getRequestHandlers", "dir", "port", "isDev", "server", "hostname", "minimalMode", "isNodeDebugging", "keepAliveTimeout", "experimentalTestProxy", "dev", "workerType", "logStartInfo", "networkUrl", "appUrl", "envInfo", "expFeatureInfo", "formatDurationText", "bootstrap", "bold", "hex", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "length", "join", "exp", "slice", "info", "event", "startServer", "allowRetry", "isExperimentalTestProxy", "selfSignedCertificate", "startServerProcessStartTime", "Date", "now", "handlersReady", "handlersError", "handlersPromise", "Promise", "resolve", "reject", "requestHandler", "req", "res", "Error", "upgradeHandler", "socket", "head", "requestListener", "undefined", "err", "statusCode", "end", "error", "url", "console", "createServer", "key", "readFileSync", "cert", "on", "destroy", "portRetryCount", "code", "warn", "listen", "exit", "addr", "address", "actualHostname", "formattedHostname", "debugPort", "PORT", "cleanup", "close", "exception", "initResult", "Boolean", "startServerProcessDuration", "Math", "round", "watchConfigFiles", "dirToWatch", "onChange", "wp", "watch", "files", "map", "file", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,OAAO,UAAS;AAChB,OAAO,yBAAwB;AAC/B,OAAO,kBAAiB;AAIxB,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,UAAU,OAAM;AACvB,OAAOC,WAAW,QAAO;AACzB,OAAOC,eAAe,YAAW;AACjC,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,YAAY,QAAQ,UAAS;AACtC,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,UAAU,QAAQ,kBAAiB;AAC5C,SACEC,iBAAiB,QAGZ,wBAAuB;AAC9B,SAASC,oBAAoB,QAAQ,sBAAqB;AAC1D,SAASC,YAAY,QAAQ,6BAA4B;AACzD,OAAOC,WAAW,kBAAiB;AAEnC,MAAMC,QAAQR,WAAW;AAsBzB,OAAO,eAAeS,mBAAmB,EACvCC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EAWtB;IACC,OAAOf,WAAW;QAChBO;QACAC;QACAG;QACAK,KAAKP;QACLG;QACAF;QACAO,YAAY;QACZJ,iBAAiBA,mBAAmB;QACpCC;QACAC;IACF;AACF;AAEA,SAASG,aAAa,EACpBC,UAAU,EACVC,MAAM,EACNT,QAAQ,EACRU,OAAO,EACPC,cAAc,EACdC,kBAAkB,EAQnB;IACC3B,IAAI4B,SAAS,CACXpB,MAAMqB,IAAI,CACRrB,MAAMsB,GAAG,CAAC,WACR,CAAC,EAAE,CAAC,EAAE9B,IAAI+B,QAAQ,CAACC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEC,QAAQC,GAAG,CAACC,cAAc,CAAC,CAAC;IAIxEnC,IAAI4B,SAAS,CAAC,CAAC,gBAAgB,EAAEJ,OAAO,CAAC;IACzC,IAAIT,UAAU;QACZf,IAAI4B,SAAS,CAAC,CAAC,gBAAgB,EAAEL,WAAW,CAAC;IAC/C;IACA,IAAIE,2BAAAA,QAASW,MAAM,EAAEpC,IAAI4B,SAAS,CAAC,CAAC,gBAAgB,EAAEH,QAAQY,IAAI,CAAC,MAAM,CAAC;IAE1E,IAAIX,kCAAAA,eAAgBU,MAAM,EAAE;QAC1BpC,IAAI4B,SAAS,CAAC,CAAC,qCAAqC,CAAC;QACrD,4BAA4B;QAC5B,KAAK,MAAMU,OAAOZ,eAAea,KAAK,CAAC,GAAG,GAAI;YAC5CvC,IAAI4B,SAAS,CAAC,CAAC,KAAK,EAAEU,IAAI,CAAC;QAC7B;QACA,qCAAqC,GACrC,IAAIZ,eAAeU,MAAM,GAAG,GAAG;YAC7BpC,IAAI4B,SAAS,CAAC,CAAC,QAAQ,CAAC;QAC1B;IACF;IAEA,oCAAoC;IACpC5B,IAAIwC,IAAI,CAAC;IACTxC,IAAIyC,KAAK,CAAC,CAAC,SAAS,EAAEd,mBAAmB,CAAC;AAC5C;AAEA,OAAO,eAAee,YAAY,EAChC/B,GAAG,EACHC,IAAI,EACJC,KAAK,EACLE,QAAQ,EACRC,WAAW,EACX2B,UAAU,EACVzB,gBAAgB,EAChB0B,uBAAuB,EACvBC,qBAAqB,EACrBpB,OAAO,EACPC,cAAc,EACK;IACnB,MAAMoB,8BAA8BC,KAAKC,GAAG;IAC5C,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIC,QACnD,CAACC,SAASC;QACRL,gBAAgBI;QAChBH,gBAAgBI;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIN,iBAAiB;YACnB,MAAMA;YACN,OAAOI,eAAeC,KAAKC;QAC7B;QACA,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,iBAAuC,OACzCH,KACAI,QACAC;QAEA,IAAIV,iBAAiB;YACnB,MAAMA;YACN,OAAOQ,eAAeH,KAAKI,QAAQC;QACrC;QACA,MAAM,IAAIH,MAAM;IAClB;IAEA,4CAA4C;IAC5C,IAAIb,yBAAyB,CAAChC,OAAO;QACnC,MAAM,IAAI6C,MACR;IAEJ;IAEA,eAAeI,gBAAgBN,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIN,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBY;YACpB;YACA,MAAMR,eAAeC,KAAKC;QAC5B,EAAE,OAAOO,KAAK;YACZP,IAAIQ,UAAU,GAAG;YACjBR,IAAIS,GAAG,CAAC;YACRlE,IAAImE,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,MAAMlD,SAAS+B,wBACX/C,MAAMwE,YAAY,CAChB;QACEC,KAAK5E,GAAG6E,YAAY,CAAC3B,sBAAsB0B,GAAG;QAC9CE,MAAM9E,GAAG6E,YAAY,CAAC3B,sBAAsB4B,IAAI;IAClD,GACAX,mBAEFjE,KAAKyE,YAAY,CAACR;IAEtB,IAAI5C,kBAAkB;QACpBJ,OAAOI,gBAAgB,GAAGA;IAC5B;IACAJ,OAAO4D,EAAE,CAAC,WAAW,OAAOlB,KAAKI,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeH,KAAKI,QAAQC;QACpC,EAAE,OAAOG,KAAK;YACZJ,OAAOe,OAAO;YACd3E,IAAImE,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,IAAIY,iBAAiB;IAErB9D,OAAO4D,EAAE,CAAC,SAAS,CAACV;QAClB,IACErB,cACA/B,QACAC,SACAmD,IAAIa,IAAI,KAAK,gBACbD,iBAAiB,IACjB;YACA5E,IAAI8E,IAAI,CAAC,CAAC,KAAK,EAAElE,KAAK,mBAAmB,EAAEA,OAAO,EAAE,SAAS,CAAC;YAC9DA,QAAQ;YACRgE,kBAAkB;YAClB9D,OAAOiE,MAAM,CAACnE,MAAMG;QACtB,OAAO;YACLf,IAAImE,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCE,QAAQF,KAAK,CAACH;YACd/B,QAAQ+C,IAAI,CAAC;QACf;IACF;IAEA,MAAM/D,kBAAkBX;IAExB,MAAM,IAAI8C,QAAc,CAACC;QACvBvC,OAAO4D,EAAE,CAAC,aAAa;YACrB,MAAMO,OAAOnE,OAAOoE,OAAO;YAC3B,MAAMC,iBAAiBhF,eACrB,OAAO8E,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAInE,YAAY,cAC7BkE;YAEN,MAAMG,oBACJ,CAACrE,YAAYoE,mBAAmB,YAC5B,cACAA,mBAAmB,SACnB,UACAhF,eAAeY;YAErBH,OAAO,OAAOqE,SAAS,WAAWA,CAAAA,wBAAAA,KAAMrE,IAAI,KAAIA,OAAOA;YAEvD,MAAMW,aAAa,CAAC,OAAO,EAAE4D,eAAe,CAAC,EAAEvE,KAAK,CAAC;YACrD,MAAMY,SAAS,CAAC,EACdqB,wBAAwB,UAAU,OACnC,GAAG,EAAEuC,kBAAkB,CAAC,EAAExE,KAAK,CAAC;YAEjC,IAAIK,iBAAiB;gBACnB,MAAMoE,YAAYnF;gBAClBF,IAAIwC,IAAI,CACN,CAAC,aAAa,EACZvB,oBAAoB,QAAQ,SAAS,GACtC,4EAA4E,EAAEoE,UAAU,CAAC,CAAC;YAE/F;YAEA,yCAAyC;YACzCpD,QAAQC,GAAG,CAACoD,IAAI,GAAG1E,OAAO;YAE1B,IAAI;gBACF,MAAM2E,UAAU,CAACV;oBACfpE,MAAM;oBACNK,OAAO0E,KAAK;oBACZvD,QAAQ+C,IAAI,CAACH,QAAQ;gBACvB;gBACA,MAAMY,YAAY,CAACzB;oBACjB,uDAAuD;oBACvDK,QAAQF,KAAK,CAACH;gBAChB;gBACA/B,QAAQyC,EAAE,CAAC,QAAQa;gBACnBtD,QAAQyC,EAAE,CAAC,UAAUa;gBACrBtD,QAAQyC,EAAE,CAAC,WAAWa;gBACtBtD,QAAQyC,EAAE,CAAC,qBAAqBe;gBAChCxD,QAAQyC,EAAE,CAAC,sBAAsBe;gBAEjC,MAAMC,aAAa,MAAMhF,mBAAmB;oBAC1CC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiB0E,QAAQ1E;oBACzBC;oBACAC,uBAAuB,CAAC,CAACyB;gBAC3B;gBACAW,iBAAiBmC,UAAU,CAAC,EAAE;gBAC9B/B,iBAAiB+B,UAAU,CAAC,EAAE;gBAE9B,MAAME,6BACJ7C,KAAKC,GAAG,KAAKF;gBACf,MAAMnB,qBACJiE,6BAA6B,OACzB,CAAC,EAAEC,KAAKC,KAAK,CAACF,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,CAAC,EAAEA,2BAA2B,EAAE,CAAC;gBAEvC3C;gBACA3B,aAAa;oBACXC;oBACAC;oBACAT;oBACAU;oBACAC;oBACAC;gBACF;YACF,EAAE,OAAOqC,KAAK;gBACZ,gCAAgC;gBAChCd;gBACAmB,QAAQF,KAAK,CAACH;gBACd/B,QAAQ+C,IAAI,CAAC;YACf;YAEA3B;QACF;QACAvC,OAAOiE,MAAM,CAACnE,MAAMG;IACtB;IAEA,IAAIF,OAAO;QACT,SAASkF,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAInG;YACfmG,GAAGC,KAAK,CAAC;gBACPC,OAAO7F,aAAa8F,GAAG,CAAC,CAACC,OAAS1G,KAAKyC,IAAI,CAAC2D,YAAYM;YAC1D;YACAJ,GAAGxB,EAAE,CAAC,UAAUuB;QAClB;QACAF,iBAAiBpF,KAAK,OAAO4F;YAC3B,IAAItE,QAAQC,GAAG,CAACsE,6BAA6B,EAAE;gBAC7CxG,IAAIwC,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEAxC,IAAI8E,IAAI,CACN,CAAC,kBAAkB,EAAElF,KAAK6G,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpDtE,QAAQ+C,IAAI,CAAC3E;QACf;IACF;AACF;AAEA,IAAI4B,QAAQC,GAAG,CAACwE,mBAAmB,IAAIzE,QAAQ0E,IAAI,EAAE;IACnD1E,QAAQ2E,WAAW,CAAC,WAAW,OAAOC;QACpC,IAAIA,OAAO,OAAOA,OAAOA,IAAIC,iBAAiB,IAAI7E,QAAQ0E,IAAI,EAAE;YAC9D,MAAMjE,YAAYmE,IAAIC,iBAAiB;YACvC7E,QAAQ0E,IAAI,CAAC;gBAAEI,iBAAiB;YAAK;QACvC;IACF;IACA9E,QAAQ0E,IAAI,CAAC;QAAEK,iBAAiB;IAAK;AACvC"}