'use client';

import { useState } from 'react';
import { X, Play, ArrowRight, FileText, Eye, Download, Zap } from 'lucide-react';
import Link from 'next/link';
import { useTranslation } from '@/hooks/useTranslation';

interface InteractiveDemoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function InteractiveDemoModal({ isOpen, onClose }: InteractiveDemoModalProps) {
  const { t } = useTranslation('demo-player');
  const [currentStep, setCurrentStep] = useState(0);

  // Get demo steps from translation
  const stepsData = t('modalSteps', { returnObjects: true });
  const demoSteps = Array.isArray(stepsData) ? stepsData as Array<{
    title: string;
    description: string;
    content: string;
    highlight: string;
  }> : [];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
      <div className="bg-white rounded-2xl shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Play className="h-8 w-8" />
              <div>
                <h2 className="text-2xl font-bold">{t('title')}</h2>
                <p className="text-blue-100">{t('subtitle')}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Demo Content */}
        <div className="p-6">
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-900">
                {demoSteps[currentStep].title}
              </h3>
              <span className="text-sm text-gray-500">
{t('progress.step')} {currentStep + 1} {t('progress.of')} {demoSteps.length}
              </span>
            </div>
            <p className="text-gray-600 mb-4">{demoSteps[currentStep].description}</p>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / demoSteps.length) * 100}%` }}
              />
            </div>
          </div>

          {/* Mock Editor Interface */}
          <div className="border border-gray-200 rounded-lg overflow-hidden mb-6">
            <div className="bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">{t('interface.title')}</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              </div>
            </div>
            
            <div className="flex h-64">
              {/* Editor Panel */}
              <div className={`w-1/2 border-r border-gray-200 ${demoSteps[currentStep].highlight === 'editor' ? 'ring-2 ring-blue-500' : ''}`}>
                <div className="bg-gray-50 border-b border-gray-200 px-3 py-2">
                  <span className="text-xs font-medium text-gray-600">{t('interface.editor')}</span>
                </div>
                <div className="p-3 font-mono text-sm bg-gray-900 text-green-400 h-full overflow-auto">
                  <pre className="whitespace-pre-wrap">{demoSteps[currentStep].content}</pre>
                </div>
              </div>
              
              {/* Preview Panel */}
              <div className={`w-1/2 ${demoSteps[currentStep].highlight === 'preview' ? 'ring-2 ring-blue-500' : ''}`}>
                <div className="bg-gray-50 border-b border-gray-200 px-3 py-2">
                  <span className="text-xs font-medium text-gray-600">{t('interface.preview')}</span>
                </div>
                <div className="p-3 h-full overflow-auto bg-white">
                  <div className="prose prose-sm max-w-none">
                    <h1>Hello World</h1>
                    <p>This is <strong>bold</strong> and <em>italic</em> text.</p>
                    {currentStep >= 1 && (
                      <ul>
                        <li>List item 1</li>
                        <li>List item 2</li>
                        <li>List item 3</li>
                      </ul>
                    )}
                    {currentStep >= 2 && (
                      <pre><code>console.log('Hello, World!');</code></pre>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Toolbar */}
            <div className={`bg-gray-100 border-t border-gray-200 px-4 py-2 flex items-center justify-between ${demoSteps[currentStep].highlight === 'export' ? 'ring-2 ring-blue-500' : ''}`}>
              <div className="flex items-center space-x-2">
                <button className="p-1 hover:bg-gray-200 rounded">
                  <Download className="h-4 w-4 text-gray-600" />
                </button>
                <button className="p-1 hover:bg-gray-200 rounded">
                  <FileText className="h-4 w-4 text-gray-600" />
                </button>
              </div>
              <div className="text-xs text-gray-500">{t('interface.autoSaved')}</div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {t('navigation.previous')}
            </button>
            
            <div className="flex space-x-2">
              {demoSteps.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentStep(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentStep ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
            
            {currentStep < demoSteps.length - 1 ? (
              <button
                onClick={() => setCurrentStep(Math.min(demoSteps.length - 1, currentStep + 1))}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {t('navigation.next')}
              </button>
            ) : (
              <Link
                href="/editor"
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                onClick={onClose}
              >
                {t('navigation.tryNow')}
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 border-t border-gray-200 p-6">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-3">
              <Zap className="h-5 w-5 text-yellow-500" />
              <span className="font-semibold text-gray-900">{t('footer.title')}</span>
            </div>
            <p className="text-gray-600 mb-4">
              {t('footer.description')}
            </p>
            <Link
              href="/editor"
              className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              onClick={onClose}
            >
              {t('footer.button')}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}