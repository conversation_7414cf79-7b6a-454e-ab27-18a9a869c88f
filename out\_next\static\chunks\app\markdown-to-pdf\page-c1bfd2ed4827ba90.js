(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9315],{5531:function(e,n,t){"use strict";t.d(n,{Z:function(){return i}});var a=t(2265);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,n,t)=>!!e&&t.indexOf(e)===n).join(" ");/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,a.forwardRef)(({color:e="currentColor",size:n=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:l="",children:i,iconNode:c,...d},m)=>(0,a.createElement)("svg",{ref:m,...s,width:n,height:n,stroke:e,strokeWidth:r?24*Number(t)/Number(n):t,className:o("lucide",l),...d},[...c.map(([e,n])=>(0,a.createElement)(e,n)),...Array.isArray(i)?i:[i]])),i=(e,n)=>{let t=(0,a.forwardRef)(({className:t,...s},i)=>(0,a.createElement)(l,{ref:i,iconNode:n,className:o(`lucide-${r(e)}`,t),...s}));return t.displayName=`${e}`,t}},8063:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},4530:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6224:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},5817:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},9670:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5099:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},1841:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},9804:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("PanelsTopLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},4280:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},9409:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},7346:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]])},1541:function(e,n,t){"use strict";t.d(n,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},8247:function(e,n,t){Promise.resolve().then(t.bind(t,6142))},6142:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return b}});var a=t(7437),r=t(2265),o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var l=t(9670),i=t(9409),c=t(1841),d=t(5099),m=t(4280),u=t(5817),p=t(4530),x=t(8063),h=t(9804),f=t(7346),g=t(1541),y=t(6224);function b(){let[e,n]=(0,r.useState)('# Markdown to PDF Converter\n\nWelcome to our **free online Markdown to PDF converter**! This powerful tool allows you to transform your Markdown documents into professional PDF files with customizable styling and formatting options.\n\n## Key Features\n\n### ✅ Professional PDF Output\n- High-quality PDF generation with crisp text and images\n- Multiple page size options (A4, Letter, Legal)\n- Portrait and landscape orientation support\n- Customizable margins and spacing\n\n### ✅ Advanced Formatting Options\n- **Multiple themes**: Default, GitHub, Academic, Minimal\n- **Typography control**: Font size and family options\n- **Page elements**: Headers, footers, and page numbers\n- **Table of contents**: Automatic generation from headings\n\n### ✅ Complete Markdown Support\n- All standard Markdown syntax elements\n- Tables with proper formatting\n- Code blocks with syntax highlighting\n- Images and media embedding\n- Mathematical expressions (LaTeX)\n\n## How to Use\n\n1. **Input your Markdown**: Paste or type your content in the editor\n2. **Customize settings**: Choose page size, theme, and formatting options\n3. **Preview**: See how your PDF will look before downloading\n4. **Download**: Generate and save your professional PDF\n\n## Supported Markdown Elements\n\n### Text Formatting\n- **Bold text** and *italic text*\n- ~~Strikethrough text~~\n- `Inline code`\n- [Links](https://example.com)\n\n### Lists\n#### Unordered Lists\n- Item 1\n- Item 2\n  - Nested item\n  - Another nested item\n- Item 3\n\n#### Ordered Lists\n1. First item\n2. Second item\n3. Third item\n\n### Code Blocks\n```javascript\nfunction convertToPdf() {\n  console.log("Converting Markdown to PDF...");\n  return "Success!";\n}\n```\n\n### Tables\n| Feature | Free Version | Premium |\n|---------|-------------|---------|\n| Basic Conversion | ✅ | ✅ |\n| Custom Themes | ✅ | ✅ |\n| Advanced Settings | ✅ | ✅ |\n| Batch Processing | ❌ | ✅ |\n\n### Blockquotes\n> "The best way to predict the future is to create it."\n> \n> — Peter Drucker\n\n### Mathematical Expressions\nInline math: $E = mc^2$\n\nBlock math:\n$$\n\\sum_{i=1}^{n} x_i = \\frac{1}{n} \\sum_{i=1}^{n} x_i\n$$\n\n---\n\n## Why Choose Our Converter?\n\n### \uD83D\uDE80 Fast and Reliable\n- Instant conversion with no waiting time\n- Handles large documents efficiently\n- Reliable output every time\n\n### \uD83D\uDD12 Privacy First\n- All processing happens in your browser\n- No data sent to external servers\n- Your documents remain completely private\n\n### \uD83D\uDCB0 Completely Free\n- No registration required\n- No watermarks on output\n- Unlimited conversions\n\n### \uD83C\uDFA8 Professional Results\n- Publication-ready PDF output\n- Consistent formatting across platforms\n- Print-optimized layouts\n\n---\n\n**Ready to convert?** Start by editing this sample content or paste your own Markdown text above. Customize the settings to match your preferences and click "Generate PDF" to create your professional document.\n\nHappy converting! \uD83D\uDCC4✨'),[t,o]=(0,r.useState)(""),[b,v]=(0,r.useState)(!1),[k,w]=(0,r.useState)(!1),[j,N]=(0,r.useState)(!0),[C,P]=(0,r.useState)(!1),[S,T]=(0,r.useState)("idle"),$=(0,r.useRef)(null),F=(0,r.useRef)(null),M=(0,r.useRef)(null),[D,z]=(0,r.useState)({pageSize:"A4",orientation:"portrait",margin:"normal",fontSize:"medium",theme:"default",includeTableOfContents:!1,includePageNumbers:!0,headerText:"",footerText:""}),Z=e=>{if(!e)return"";let n=e.replace(/^### (.*$)/gim,'<h3 id="$1">$1</h3>').replace(/^## (.*$)/gim,'<h2 id="$1">$1</h2>').replace(/^# (.*$)/gim,'<h1 id="$1">$1</h1>').replace(/!\[([^\]]*)\]\(([^)]+)\)/gim,'<img src="$2" alt="$1" class="pdf-image" />').replace(/\*\*\*(.*?)\*\*\*/gim,"<strong><em>$1</em></strong>").replace(/\*\*(.*?)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*?)\*/gim,"<em>$1</em>").replace(/~~(.*?)~~/gim,"<del>$1</del>").replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2">$1</a>').replace(/```(\w+)?\n([\s\S]*?)```/gim,'<pre class="code-block"><code class="language-$1">$2</code></pre>').replace(/`([^`]+)`/gim,'<code class="inline-code">$1</code>').replace(/^\|(.+)\|$/gim,(e,n)=>{let t=n.split("|").map(e=>e.trim()).filter(e=>e);return"<tr>"+t.map(e=>"<td>".concat(e,"</td>")).join("")+"</tr>"}).replace(/^\- (.*$)/gim,"<li>$1</li>").replace(/^\d+\. (.*$)/gim,"<li>$1</li>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/^---$/gim,"<hr>").replace(/\$\$([\s\S]*?)\$\$/gim,'<div class="math-block">$1</div>').replace(/\$([^$]+)\$/gim,'<span class="math-inline">$1</span>').replace(/\n/gim,"<br>");return(n=n.replace(RegExp("(<li>.*<\\/li>)","s"),"<ul>$1</ul>")).includes("<tr>")&&(n=n.replace(RegExp("(<tr>.*<\\/tr>)","s"),'<table class="pdf-table">$1</table>')),n},_=e=>{let n=e.match(/<h[1-3][^>]*>([^<]+)<\/h[1-3]>/g)||[];if(0===n.length)return"";let t='<div class="table-of-contents"><h2>Table of Contents</h2><ul>';return n.forEach(e=>{var n;let a=parseInt((null===(n=e.match(/<h([1-3])/))||void 0===n?void 0:n[1])||"1"),r=e.replace(/<[^>]*>/g,""),o="  ".repeat(a-1);t+="".concat(o,'<li><a href="#').concat(r,'">').concat(r,"</a></li>")}),t+="</ul></div>"},L=()=>{let e="\n      @page {\n        size: ".concat(D.pageSize," ").concat(D.orientation,";\n        margin: ").concat("narrow"===D.margin?"0.5in":"wide"===D.margin?"1.5in":"1in",";\n      }\n      \n      body {\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n        font-size: ").concat("small"===D.fontSize?"12px":"large"===D.fontSize?"16px":"14px",";\n        line-height: 1.6;\n        color: #333;\n        max-width: none;\n        margin: 0;\n        padding: 20px;\n      }\n      \n      .pdf-header {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        height: 50px;\n        text-align: center;\n        font-size: 12px;\n        color: #666;\n        border-bottom: 1px solid #eee;\n        padding: 10px;\n      }\n      \n      .pdf-footer {\n        position: fixed;\n        bottom: 0;\n        left: 0;\n        right: 0;\n        height: 50px;\n        text-align: center;\n        font-size: 12px;\n        color: #666;\n        border-top: 1px solid #eee;\n        padding: 10px;\n      }\n      \n      .table-of-contents {\n        page-break-after: always;\n        margin-bottom: 30px;\n      }\n      \n      .table-of-contents ul {\n        list-style: none;\n        padding-left: 0;\n      }\n      \n      .table-of-contents li {\n        margin: 5px 0;\n        padding-left: 20px;\n      }\n      \n      h1, h2, h3, h4, h5, h6 {\n        page-break-after: avoid;\n        margin-top: 30px;\n        margin-bottom: 15px;\n      }\n      \n      h1 { font-size: 2em; color: #2c3e50; }\n      h2 { font-size: 1.5em; color: #34495e; }\n      h3 { font-size: 1.2em; color: #34495e; }\n      \n      .pdf-image {\n        max-width: 100%;\n        height: auto;\n        display: block;\n        margin: 20px auto;\n        border-radius: 8px;\n        box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n      }\n      \n      .code-block {\n        background: #f8f9fa;\n        border: 1px solid #e9ecef;\n        border-radius: 6px;\n        padding: 16px;\n        margin: 16px 0;\n        overflow-x: auto;\n        font-family: 'Courier New', monospace;\n        font-size: 13px;\n      }\n      \n      .inline-code {\n        background: #f1f3f4;\n        padding: 2px 6px;\n        border-radius: 3px;\n        font-family: 'Courier New', monospace;\n        font-size: 0.9em;\n      }\n      \n      .pdf-table {\n        width: 100%;\n        border-collapse: collapse;\n        margin: 20px 0;\n      }\n      \n      .pdf-table td, .pdf-table th {\n        border: 1px solid #ddd;\n        padding: 12px;\n        text-align: left;\n      }\n      \n      .pdf-table th {\n        background-color: #f8f9fa;\n        font-weight: bold;\n      }\n      \n      blockquote {\n        border-left: 4px solid #3498db;\n        margin: 20px 0;\n        padding-left: 20px;\n        color: #666;\n        font-style: italic;\n      }\n      \n      hr {\n        border: none;\n        border-top: 2px solid #eee;\n        margin: 30px 0;\n      }\n      \n      .math-block {\n        text-align: center;\n        margin: 20px 0;\n        font-family: 'Times New Roman', serif;\n        font-size: 1.1em;\n      }\n      \n      .math-inline {\n        font-family: 'Times New Roman', serif;\n      }\n    ");return e+({default:"",github:"\n        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif; }\n        h1, h2 { border-bottom: 1px solid #eaecef; padding-bottom: 10px; }\n        .code-block { background: #f6f8fa; }\n      ",academic:"\n        body { font-family: 'Times New Roman', serif; }\n        h1, h2, h3 { font-family: 'Times New Roman', serif; }\n        .code-block { font-family: 'Courier New', monospace; }\n      ",minimal:"\n        body { font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; }\n        h1, h2, h3 { font-weight: 300; }\n        .code-block { background: #fafafa; border: none; }\n      "})[D.theme]};(0,r.useEffect)(()=>{let n=Z(e);o(n)},[e]),(0,r.useEffect)(()=>{let n=setTimeout(()=>{localStorage.setItem("pdf-markdown-content",e),localStorage.setItem("pdf-settings",JSON.stringify(D))},1e3);return()=>clearTimeout(n)},[e,D]),(0,r.useEffect)(()=>{let e=localStorage.getItem("pdf-markdown-content"),t=localStorage.getItem("pdf-settings");if(e&&n(e),t)try{z(JSON.parse(t))}catch(e){console.error("Failed to parse saved settings:",e)}},[]);let O=async()=>{v(!0),T("idle");try{await new Promise(e=>setTimeout(e,2e3));let e=t;if(D.includeTableOfContents){let n=_(t);e=n+e}let n='\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="UTF-8">\n          <title>Markdown Document</title>\n          <style>'.concat(L(),"</style>\n        </head>\n        <body>\n          ").concat(D.headerText?'<div class="pdf-header">'.concat(D.headerText,"</div>"):"","\n          ").concat(e,"\n          ").concat(D.footerText?'<div class="pdf-footer">'.concat(D.footerText,"</div>"):"","\n        </body>\n        </html>\n      "),a=new Blob([n],{type:"text/html"}),r=URL.createObjectURL(a),o=document.createElement("a");o.href=r,o.download="document.html",o.click(),URL.revokeObjectURL(r),T("success")}catch(e){console.error("PDF generation failed:",e),T("error")}finally{v(!1)}},E=async()=>{try{await navigator.clipboard.writeText(e),console.log("Markdown copied to clipboard")}catch(e){console.error("Failed to copy to clipboard:",e)}};return(0,a.jsxs)("div",{className:"".concat(C?"fixed inset-0 z-50":"min-h-screen"," bg-gray-50"),children:[(0,a.jsxs)("div",{className:"bg-white border-b border-gray-200 px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"bg-green-100 p-2 rounded-lg",children:(0,a.jsx)(s,{className:"h-6 w-6 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Markdown to PDF Converter"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Convert your Markdown documents to professional PDF files"})]})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("button",{onClick:()=>N(!j),className:"p-2 rounded-lg transition-colors ".concat(j?"bg-blue-100 text-blue-600":"hover:bg-gray-100 text-gray-600"),title:j?"Hide Preview":"Show Preview",children:(0,a.jsx)(l.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>w(!k),className:"p-2 rounded-lg transition-colors ".concat(k?"bg-blue-100 text-blue-600":"hover:bg-gray-100 text-gray-600"),title:"PDF Settings",children:(0,a.jsx)(i.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>P(!C),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600",title:C?"Exit Fullscreen":"Enter Fullscreen",children:C?(0,a.jsx)(c.Z,{className:"h-5 w-5"}):(0,a.jsx)(d.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:O,disabled:b||!e.trim(),className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:b?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{className:"h-4 w-4 animate-spin"}),(0,a.jsx)("span",{children:"Generating..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Generate PDF"})]})})]})]}),"success"===S&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"text-green-800",children:"PDF generated successfully!"})]}),"error"===S&&(0,a.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2",children:[(0,a.jsx)(x.Z,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{className:"text-red-800",children:"Failed to generate PDF. Please try again."})]})]}),(0,a.jsxs)("div",{className:"flex",style:{height:"calc(100vh - 89px)"},children:[k&&(0,a.jsx)("div",{className:"w-80 bg-white border-r border-gray-200 overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"PDF Settings"}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4 mr-2"}),"Page Settings"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Page Size"}),(0,a.jsxs)("select",{value:D.pageSize,onChange:e=>z(n=>({...n,pageSize:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"A4",children:"A4"}),(0,a.jsx)("option",{value:"Letter",children:"Letter"}),(0,a.jsx)("option",{value:"Legal",children:"Legal"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Orientation"}),(0,a.jsxs)("select",{value:D.orientation,onChange:e=>z(n=>({...n,orientation:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"portrait",children:"Portrait"}),(0,a.jsx)("option",{value:"landscape",children:"Landscape"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Margins"}),(0,a.jsxs)("select",{value:D.margin,onChange:e=>z(n=>({...n,margin:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"narrow",children:"Narrow"}),(0,a.jsx)("option",{value:"normal",children:"Normal"}),(0,a.jsx)("option",{value:"wide",children:"Wide"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-3 flex items-center",children:[(0,a.jsx)(f.Z,{className:"h-4 w-4 mr-2"}),"Typography"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Font Size"}),(0,a.jsxs)("select",{value:D.fontSize,onChange:e=>z(n=>({...n,fontSize:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"small",children:"Small"}),(0,a.jsx)("option",{value:"medium",children:"Medium"}),(0,a.jsx)("option",{value:"large",children:"Large"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Theme"}),(0,a.jsxs)("select",{value:D.theme,onChange:e=>z(n=>({...n,theme:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md text-sm",children:[(0,a.jsx)("option",{value:"default",children:"Default"}),(0,a.jsx)("option",{value:"github",children:"GitHub"}),(0,a.jsx)("option",{value:"academic",children:"Academic"}),(0,a.jsx)("option",{value:"minimal",children:"Minimal"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Document Options"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.includeTableOfContents,onChange:e=>z(n=>({...n,includeTableOfContents:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include Table of Contents"})]}),(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.includePageNumbers,onChange:e=>z(n=>({...n,includePageNumbers:e.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"ml-2 text-sm text-gray-700",children:"Include Page Numbers"})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Headers & Footers"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Header Text"}),(0,a.jsx)("input",{type:"text",value:D.headerText,onChange:e=>z(n=>({...n,headerText:e.target.value})),placeholder:"Optional header text",className:"w-full p-2 border border-gray-300 rounded-md text-sm"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs font-medium text-gray-600 mb-1",children:"Footer Text"}),(0,a.jsx)("input",{type:"text",value:D.footerText,onChange:e=>z(n=>({...n,footerText:e.target.value})),placeholder:"Optional footer text",className:"w-full p-2 border border-gray-300 rounded-md text-sm"})]})]})]})]})}),(0,a.jsxs)("div",{className:"".concat(j?"w-1/2":"w-full"," flex flex-col"),children:[(0,a.jsxs)("div",{className:"bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Markdown Editor"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{ref:M,type:"file",accept:".md,.markdown",onChange:e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];if(a&&("text/markdown"===a.type||a.name.endsWith(".md"))){let e=new FileReader;e.onload=e=>{var t;let a=null===(t=e.target)||void 0===t?void 0:t.result;n(a)},e.readAsText(a)}},className:"hidden"}),(0,a.jsx)("button",{onClick:()=>{var e;return null===(e=M.current)||void 0===e?void 0:e.click()},className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Upload Markdown File",children:(0,a.jsx)(g.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:E,className:"p-1 hover:bg-gray-200 rounded transition-colors text-gray-600",title:"Copy Markdown",children:(0,a.jsx)(y.Z,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>n(""),className:"text-xs px-2 py-1 hover:bg-gray-200 rounded transition-colors text-gray-600",children:"Clear"})]})]}),(0,a.jsx)("textarea",{ref:$,value:e,onChange:e=>n(e.target.value),className:"flex-1 p-4 font-mono text-sm resize-none focus:outline-none bg-white",placeholder:"Paste your Markdown content here or start typing...",spellCheck:!1})]}),j&&(0,a.jsxs)("div",{className:"".concat("w-1/2"," flex flex-col border-l border-gray-200"),children:[(0,a.jsx)("div",{className:"bg-gray-100 border-b border-gray-200 px-4 py-2",children:(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"PDF Preview"})}),(0,a.jsxs)("div",{ref:F,className:"flex-1 p-4 overflow-auto bg-white",style:{fontFamily:"academic"===D.theme?"Times New Roman, serif":"inherit"},children:[D.includeTableOfContents&&t&&(0,a.jsx)("div",{className:"mb-8 pb-8 border-b border-gray-200",dangerouslySetInnerHTML:{__html:_(t)}}),(0,a.jsx)("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:t}})]})]})]})]})}},622:function(e,n,t){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=t(2265),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,l=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function c(e,n,t){var a,o={},c=null,d=null;for(a in void 0!==t&&(c=""+t),void 0!==n.key&&(c=""+n.key),void 0!==n.ref&&(d=n.ref),n)s.call(n,a)&&!i.hasOwnProperty(a)&&(o[a]=n[a]);if(e&&e.defaultProps)for(a in n=e.defaultProps)void 0===o[a]&&(o[a]=n[a]);return{$$typeof:r,type:e,key:c,ref:d,props:o,_owner:l.current}}n.Fragment=o,n.jsx=c,n.jsxs=c},7437:function(e,n,t){"use strict";e.exports=t(622)}},function(e){e.O(0,[2971,7864,1744],function(){return e(e.s=8247)}),_N_E=e.O()}]);