{"version": 3, "sources": ["../../../src/client/components/static-generation-bailout.ts"], "names": ["staticGenerationBailout", "StaticGenBailoutError", "Error", "code", "formatErrorMessage", "reason", "opts", "dynamic", "link", "suffix", "staticGenerationStore", "staticGenerationAsyncStorage", "getStore", "forceStatic", "dynamicShouldError", "revalidate", "isStaticGeneration", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack"], "mappings": ";;;;+BAsBaA;;;eAAAA;;;oCAtBsB;sDACU;AAE7C,MAAMC,8BAA8BC;;;aAClCC,OAAO;;AACT;AASA,SAASC,mBAAmBC,MAAc,EAAEC,IAAkB;IAC5D,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAE,GAAGF,QAAQ,CAAC;IACnC,MAAMG,SAASD,OAAO,AAAC,0BAAuBA,OAAS;IACvD,OAAO,AAAC,SACND,CAAAA,UAAU,AAAC,uBAAqBA,UAAQ,OAAO,EAAC,IACjD,uDAAqDF,SAAO,OAAKI;AACpE;AAEO,MAAMT,0BAAmD,CAC9DK,QACAC;IAEA,MAAMI,wBAAwBC,kEAA4B,CAACC,QAAQ;IAEnE,IAAIF,yCAAAA,sBAAuBG,WAAW,EAAE;QACtC,OAAO;IACT;IAEA,IAAIH,yCAAAA,sBAAuBI,kBAAkB,EAAE;YAEIR;QADjD,MAAM,IAAIL,sBACRG,mBAAmBC,QAAQ;YAAE,GAAGC,IAAI;YAAEC,SAASD,CAAAA,gBAAAA,wBAAAA,KAAMC,OAAO,YAAbD,gBAAiB;QAAQ;IAE5E;IAEA,IAAII,uBAAuB;QACzBA,sBAAsBK,UAAU,GAAG;IACrC;IAEA,IAAIL,yCAAAA,sBAAuBM,kBAAkB,EAAE;QAC7C,MAAMC,MAAM,IAAIC,sCAAkB,CAChCd,mBAAmBC,QAAQ;YACzB,GAAGC,IAAI;YACP,uEAAuE;YACvE,8EAA8E;YAC9EE,MAAM;QACR;QAEFE,sBAAsBS,uBAAuB,GAAGd;QAChDK,sBAAsBU,iBAAiB,GAAGH,IAAII,KAAK;QAEnD,MAAMJ;IACR;IAEA,OAAO;AACT"}