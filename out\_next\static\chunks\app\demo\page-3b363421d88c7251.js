(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{2401:function(e,n,t){var o={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function s(e){if(!t.o(o,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=o[e],s=n[0];return t.e(n[1]).then(function(){return t.t(s,19)})}s.keys=function(){return Object.keys(o)},s.id=2401,e.exports=s},1539:function(e,n,t){var o={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function s(e){if(!t.o(o,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=o[e],s=n[0];return t.e(n[1]).then(function(){return t.t(s,19)})}s.keys=function(){return Object.keys(o)},s.id=1539,e.exports=s},8291:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},9670:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5099:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},4900:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},2549:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},2369:function(e,n,t){"use strict";t.d(n,{Z:function(){return s}});var o=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,o.Z)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},9959:function(e,n,t){Promise.resolve().then(t.bind(t,2329))},2329:function(e,n,t){"use strict";t.r(n),t.d(n,{default:function(){return v}});var o=t(7437),s=t(2265),r=t(4900),a=t(2549),l=t(5099),i=t(6637),c=t(9670),d=t(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,d.Z)("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]),u=(0,d.Z)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),h=(0,d.Z)("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]),x=(0,d.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),j=(0,d.Z)("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),p=(0,d.Z)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);var f=t(2369),g=t(8291),y=t(1396),w=t.n(y),b=t(4346);function v(){let{t:e}=(0,b.$G)("demo-player"),n=()=>{let n=e("steps",{returnObjects:!0}),t=Array.isArray(n)?n:[],o=["","# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.","# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)","# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)\n\n### Code Example\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```","# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)\n\n### Code Example\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```\n\n### Comparison Table\n\n| Feature | Free | Premium |\n|---------|------|--------|\n| Real-time Preview | ✅ | ✅ |\n| Export Options | ✅ | ✅ |\n| Advanced Themes | ❌ | ✅ |","# Welcome to Markdown Live Preview\n\nThis is a **free online** Markdown editor with *real-time* preview.\n\n## Features\n\n- Real-time preview\n- Syntax highlighting\n- Export options\n\n[Try it now!](https://example.com)\n\n### Code Example\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```\n\n### Comparison Table\n\n| Feature | Free | Premium |\n|---------|------|--------|\n| Real-time Preview | ✅ | ✅ |\n| Export Options | ✅ | ✅ |\n| Advanced Themes | ❌ | ✅ |\n\n---\n\n**Ready to start?** Click the button below to begin editing!"],s=["interface","editing","formatting","code","tables","export"],r=[3e3,4e3,4e3,5e3,4e3,3e3];return t.map((e,n)=>({id:n+1,title:e.title,description:e.description,duration:r[n]||3e3,action:e.action,markdown:o[n]||"",highlight:s[n]}))},[t,d]=(0,s.useState)([]),[y,v]=(0,s.useState)(!1),[k,N]=(0,s.useState)(0),[M,E]=(0,s.useState)(0),[Z,S]=(0,s.useState)(!1),[T,C]=(0,s.useState)(!1),[P,$]=(0,s.useState)(""),[O,R]=(0,s.useState)(0),q=(0,s.useRef)(null),F=(0,s.useRef)(null);(0,s.useEffect)(()=>{d(n())},[]);let L=Math.max(0,Math.min(k,t.length-1)),I=t[L]||t[0];t.reduce((e,n)=>e+n.duration,0),(0,s.useEffect)(()=>{if(y&&L<t.length&&I){let e=I.duration;q.current=setInterval(()=>{E(n=>{let o=n+50/e*100;return o>=100?L<t.length-1?(N(e=>e+1),0):(v(!1),100):o})},50)}else q.current&&(clearInterval(q.current),q.current=null);return()=>{q.current&&clearInterval(q.current)}},[y,L,I]),(0,s.useEffect)(()=>{if(y&&I){let e=I.markdown,n=I.duration/Math.max(e.length,1);$(""),R(0),F.current=setInterval(()=>{R(n=>{let t=n+1;return t<=e.length?($(e.slice(0,t)),t):(F.current&&clearInterval(F.current),n)})},n)}else I&&($(I.markdown),F.current&&clearInterval(F.current));return()=>{F.current&&clearInterval(F.current)}},[L,y,I]);let A=e=>{let n=Math.max(0,Math.min(e,t.length-1));N(n),E(0),v(!1)};return t.length&&I?(0,o.jsxs)("div",{className:"".concat(T?"fixed inset-0 z-50":"min-h-screen"," bg-gray-900 text-white"),children:[(0,o.jsx)("div",{className:"bg-gray-800 border-b border-gray-700 px-6 py-4",children:(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(r.Z,{className:"h-6 w-6 text-blue-400"}),(0,o.jsx)("h1",{className:"text-xl font-bold",children:e("title")})]}),(0,o.jsx)("span",{className:"text-gray-400 text-sm",children:e("subtitle")})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)("button",{onClick:()=>C(!T),className:"p-2 hover:bg-gray-700 rounded-lg transition-colors",title:e("controls.fullscreen"),children:T?(0,o.jsx)(a.Z,{className:"h-5 w-5"}):(0,o.jsx)(l.Z,{className:"h-5 w-5"})}),(0,o.jsx)(w(),{href:"/editor",className:"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg font-medium transition-colors",children:e("controls.tryNow")})]})]})}),(0,o.jsxs)("div",{className:"flex flex-1 h-full",children:[(0,o.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,o.jsxs)("div",{className:"bg-gray-800 border-b border-gray-700 px-6 py-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold",children:I.title}),(0,o.jsxs)("span",{className:"text-sm text-gray-400",children:[e("progress.step")," ",L+1," ",e("progress.of")," ",t.length]})]}),(0,o.jsx)("p",{className:"text-gray-300 text-sm mb-3",children:I.description}),(0,o.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2 mb-3",children:(0,o.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-100",style:{width:"".concat(M,"%")}})}),(0,o.jsxs)("div",{className:"flex items-center justify-between",children:[(0,o.jsx)("span",{className:"text-xs text-gray-400",children:I.action}),(0,o.jsxs)("span",{className:"text-xs text-gray-400",children:[Math.round(I.duration*M/100/1e3),"s / ",I.duration/1e3,"s"]})]})]}),(0,o.jsxs)("div",{className:"flex-1 flex",children:[(0,o.jsxs)("div",{className:"w-1/2 flex flex-col border-r border-gray-700",children:[(0,o.jsxs)("div",{className:"bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(i.Z,{className:"h-4 w-4 text-blue-400"}),(0,o.jsx)("span",{className:"text-sm font-medium",children:e("panels.markdownEditor")})]}),(0,o.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(y?"bg-green-400 animate-pulse":"bg-gray-500")})]}),(0,o.jsx)("div",{className:"flex-1 p-4 bg-gray-900 font-mono text-sm overflow-auto",children:(0,o.jsxs)("pre",{className:"whitespace-pre-wrap text-gray-100",children:[P,y&&O<I.markdown.length&&(0,o.jsx)("span",{className:"animate-pulse",children:"|"})]})})]}),(0,o.jsxs)("div",{className:"w-1/2 flex flex-col",children:[(0,o.jsxs)("div",{className:"bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center space-x-2",children:[(0,o.jsx)(c.Z,{className:"h-4 w-4 text-green-400"}),(0,o.jsx)("span",{className:"text-sm font-medium",children:e("panels.livePreview")})]}),(0,o.jsx)("div",{className:"flex-1 p-4 bg-white text-gray-900 overflow-auto",children:(0,o.jsx)("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:P?P.replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/\*\*(.*)\*\*/gim,"<strong>$1</strong>").replace(/\*(.*)\*/gim,"<em>$1</em>").replace(/\[([^\]]+)\]\(([^)]+)\)/gim,'<a href="$2">$1</a>').replace(/^- (.*$)/gim,"<li>$1</li>").replace(RegExp("(<li>.*<\\/li>)","s"),"<ul>$1</ul>").replace(/```(\w+)?\n([\s\S]*?)```/gim,"<pre><code>$2</code></pre>").replace(/`([^`]+)`/gim,"<code>$1</code>").replace(/^\| (.*) \|$/gim,"<tr><td>$1</td></tr>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/\n/gim,"<br>"):""}})})]})]})]}),(0,o.jsxs)("div",{className:"w-80 bg-gray-800 border-l border-gray-700 flex flex-col",children:[(0,o.jsxs)("div",{className:"p-6 border-b border-gray-700",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Demo Controls"}),(0,o.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-6",children:[(0,o.jsx)("button",{onClick:()=>{L>0&&(N(e=>e-1),E(0))},disabled:0===L,className:"p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Previous Step",children:(0,o.jsx)(m,{className:"h-5 w-5"})}),(0,o.jsx)("button",{onClick:()=>{v(!y)},className:"p-3 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors",title:y?"Pause":"Play",children:y?(0,o.jsx)(u,{className:"h-6 w-6"}):(0,o.jsx)(r.Z,{className:"h-6 w-6"})}),(0,o.jsx)("button",{onClick:()=>{L<t.length-1&&(N(e=>e+1),E(0))},disabled:L===t.length-1,className:"p-2 hover:bg-gray-700 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:"Next Step",children:(0,o.jsx)(h,{className:"h-5 w-5"})}),(0,o.jsx)("button",{onClick:()=>{var e;N(0),E(0),v(!1),$((null===(e=t[0])||void 0===e?void 0:e.markdown)||"")},className:"p-2 hover:bg-gray-700 rounded-lg transition-colors",title:"Restart Demo",children:(0,o.jsx)(x,{className:"h-5 w-5"})})]}),(0,o.jsx)("div",{className:"flex items-center justify-center space-x-3",children:(0,o.jsx)("button",{onClick:()=>S(!Z),className:"p-2 hover:bg-gray-700 rounded-lg transition-colors",title:Z?"Unmute":"Mute",children:Z?(0,o.jsx)(j,{className:"h-5 w-5"}):(0,o.jsx)(p,{className:"h-5 w-5"})})})]}),(0,o.jsxs)("div",{className:"flex-1 p-6",children:[(0,o.jsx)("h4",{className:"text-sm font-semibold text-gray-400 uppercase tracking-wide mb-4",children:"Demo Steps"}),(0,o.jsx)("div",{className:"space-y-2",children:t.map((e,n)=>(0,o.jsxs)("button",{onClick:()=>A(n),className:"w-full text-left p-3 rounded-lg transition-colors ".concat(n===L?"bg-blue-600 text-white":"hover:bg-gray-700 text-gray-300"),children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,o.jsx)("span",{className:"text-sm font-medium",children:e.title}),(0,o.jsxs)("span",{className:"text-xs opacity-75",children:[e.duration/1e3,"s"]})]}),(0,o.jsx)("p",{className:"text-xs opacity-75 line-clamp-2",children:e.description})]},e.id))})]}),(0,o.jsx)("div",{className:"p-6 border-t border-gray-700",children:(0,o.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-4 text-center",children:[(0,o.jsx)(f.Z,{className:"h-8 w-8 mx-auto mb-2 text-yellow-300"}),(0,o.jsx)("h4",{className:"font-semibold mb-2",children:"Ready to Start?"}),(0,o.jsx)("p",{className:"text-sm opacity-90 mb-3",children:"Experience the power of real-time Markdown editing"}),(0,o.jsxs)(w(),{href:"/editor",className:"inline-flex items-center bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors",children:["Launch Editor",(0,o.jsx)(g.Z,{className:"ml-2 h-4 w-4"})]})]})})]})]})]}):(0,o.jsx)("div",{className:"min-h-screen bg-gray-900 text-white flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"Demo Not Available"}),(0,o.jsx)("p",{className:"text-gray-400 mb-6",children:"The demo content is currently unavailable."}),(0,o.jsx)(w(),{href:"/editor",className:"bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg font-medium transition-colors",children:"Try Editor Instead"})]})})}},4346:function(e,n,t){"use strict";t.d(n,{$G:function(){return s},bU:function(){return r}});var o=t(3275);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:n,locale:t}=(0,o.QT)();return{t:(t,o)=>n(t,e,o),locale:t,ready:!0}}function r(){let{locale:e,changeLanguage:n}=(0,o.QT)();return{locale:e,locales:["en","ja"],defaultLocale:"en",changeLanguage:n,isRTL:!1}}},3275:function(e,n,t){"use strict";t.d(n,{QT:function(){return i},XJ:function(){return c},bd:function(){return l}});var o=t(7437),s=t(2265),r=t(4033);let a=(0,s.createContext)(void 0);function l(e){let{children:n,locale:t,translations:l}=e,[i,d]=(0,s.useState)(t),[m,u]=(0,s.useState)(l);(0,r.useRouter)(),(0,r.usePathname)();let h=async e=>{if(e!==i)try{localStorage.setItem("locale",e);let n=Object.keys(m),t=await c(e,n);d(e),u(t)}catch(e){console.error("Failed to change language:",e),localStorage.setItem("locale",i)}};return(0,s.useEffect)(()=>{d(t),u(l)},[t,l]),(0,o.jsx)(a.Provider,{value:{locale:i,t:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",t=arguments.length>2?arguments[2]:void 0,o=e.split("."),s=m[n];for(let r of o){if(!s||"object"!=typeof s||!(r in s))return console.warn("Translation key not found: ".concat(n,".").concat(e)),(null==t?void 0:t.returnObjects)?[]:e;s=s[r]}return(null==t?void 0:t.returnObjects)?s:"string"==typeof s?s:e},changeLanguage:h,translations:m},children:n})}function i(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}async function c(e,n){let o={};for(let s of n)try{let n=await t(1539)("./".concat(e,"/").concat(s,".json"));o[s]=n.default||n}catch(n){if(console.warn("Failed to load translation: ".concat(e,"/").concat(s,".json")),"en"!==e)try{let e=await t(2401)("./".concat(s,".json"));o[s]=e.default||e}catch(e){console.warn("Failed to load fallback translation: en/".concat(s,".json")),o[s]={}}else o[s]={}}return o}}},function(e){e.O(0,[6347,2971,7864,1744],function(){return e(e.s=9959)}),_N_E=e.O()}]);