(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6311],{2401:function(n,e,o){var s={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function t(n){if(!o.o(s,n))return Promise.resolve().then(function(){var e=Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e});var e=s[n],t=e[0];return o.e(e[1]).then(function(){return o.t(t,19)})}t.keys=function(){return Object.keys(s)},t.id=2401,n.exports=t},1539:function(n,e,o){var s={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function t(n){if(!o.o(s,n))return Promise.resolve().then(function(){var e=Error("Cannot find module '"+n+"'");throw e.code="MODULE_NOT_FOUND",e});var e=s[n],t=e[0];return o.e(e[1]).then(function(){return o.t(t,19)})}t.keys=function(){return Object.keys(s)},t.id=1539,n.exports=t},3756:function(n,e,o){Promise.resolve().then(o.bind(o,1951))},1951:function(n,e,o){"use strict";o.r(e),o.d(e,{default:function(){return i}});var s=o(7437),t=o(4346);function r(){let{t:n}=(0,t.$G)("navbar");return(0,s.jsxs)("ul",{className:"space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Brand:"})," ",n("brand")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Home:"})," ",n("navigation.home")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Demo:"})," ",n("navigation.demo")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Editor:"})," ",n("navigation.editor")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"FAQ:"})," ",n("navigation.faq")]})]})}function a(){let{t:n}=(0,t.$G)("hero");return(0,s.jsxs)("ul",{className:"space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Title Prefix:"})," ",n("title.prefix")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Title Highlight:"})," ",n("title.highlight")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Subtitle:"})," ",n("subtitle").substring(0,50),"..."]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Start Editing:"})," ",n("buttons.startEditing")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Watch Demo:"})," ",n("buttons.watchDemo")]})]})}function i(){let{t:n}=(0,t.$G)("common"),{locale:e,changeLanguage:o}=(0,t.bU)();return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"i18n Test Page"}),(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Current Locale:"})," ",e]})}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Language Switcher"}),(0,s.jsxs)("div",{className:"space-x-4",children:[(0,s.jsx)("button",{onClick:()=>o("en"),className:"px-4 py-2 rounded ".concat("en"===e?"bg-blue-600 text-white":"bg-gray-200"),children:"English"}),(0,s.jsx)("button",{onClick:()=>o("ja"),className:"px-4 py-2 rounded ".concat("ja"===e?"bg-blue-600 text-white":"bg-gray-200"),children:"日本語"})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Common Translations"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Save:"})," ",n("buttons.save")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Cancel:"})," ",n("buttons.cancel")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Submit:"})," ",n("buttons.submit")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Loading:"})," ",n("messages.loading")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Success:"})," ",n("messages.success")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Error:"})," ",n("messages.error")]})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Navbar Translations"}),(0,s.jsx)(r,{})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Hero Translations"}),(0,s.jsx)(a,{})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Navigation Translations"}),(0,s.jsxs)("ul",{className:"space-y-2",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Home:"})," ",n("navigation.home")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Dashboard:"})," ",n("navigation.dashboard")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Settings:"})," ",n("navigation.settings")]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Help:"})," ",n("navigation.help")]})]})]})]})}},4346:function(n,e,o){"use strict";o.d(e,{$G:function(){return t},bU:function(){return r}});var s=o(3275);function t(){let n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:e,locale:o}=(0,s.QT)();return{t:(o,s)=>e(o,n,s),locale:o,ready:!0}}function r(){let{locale:n,changeLanguage:e}=(0,s.QT)();return{locale:n,locales:["en","ja"],defaultLocale:"en",changeLanguage:e,isRTL:!1}}},3275:function(n,e,o){"use strict";o.d(e,{QT:function(){return l},XJ:function(){return c},bd:function(){return i}});var s=o(7437),t=o(2265),r=o(4033);let a=(0,t.createContext)(void 0);function i(n){let{children:e,locale:o,translations:i}=n,[l,j]=(0,t.useState)(o),[d,u]=(0,t.useState)(i);(0,r.useRouter)(),(0,r.usePathname)();let h=async n=>{if(n!==l)try{localStorage.setItem("locale",n);let e=Object.keys(d),o=await c(n,e);j(n),u(o)}catch(n){console.error("Failed to change language:",n),localStorage.setItem("locale",l)}};return(0,t.useEffect)(()=>{j(o),u(i)},[o,i]),(0,s.jsx)(a.Provider,{value:{locale:l,t:function(n){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",o=arguments.length>2?arguments[2]:void 0,s=n.split("."),t=d[e];for(let r of s){if(!t||"object"!=typeof t||!(r in t))return console.warn("Translation key not found: ".concat(e,".").concat(n)),(null==o?void 0:o.returnObjects)?[]:n;t=t[r]}return(null==o?void 0:o.returnObjects)?t:"string"==typeof t?t:n},changeLanguage:h,translations:d},children:e})}function l(){let n=(0,t.useContext)(a);if(void 0===n)throw Error("useI18n must be used within an I18nProvider");return n}async function c(n,e){let s={};for(let t of e)try{let e=await o(1539)("./".concat(n,"/").concat(t,".json"));s[t]=e.default||e}catch(e){if(console.warn("Failed to load translation: ".concat(n,"/").concat(t,".json")),"en"!==n)try{let n=await o(2401)("./".concat(t,".json"));s[t]=n.default||n}catch(n){console.warn("Failed to load fallback translation: en/".concat(t,".json")),s[t]={}}else s[t]={}}return s}},622:function(n,e,o){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=o(2265),t=Symbol.for("react.element"),r=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,i=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(n,e,o){var s,r={},c=null,j=null;for(s in void 0!==o&&(c=""+o),void 0!==e.key&&(c=""+e.key),void 0!==e.ref&&(j=e.ref),e)a.call(e,s)&&!l.hasOwnProperty(s)&&(r[s]=e[s]);if(n&&n.defaultProps)for(s in e=n.defaultProps)void 0===r[s]&&(r[s]=e[s]);return{$$typeof:t,type:n,key:c,ref:j,props:r,_owner:i.current}}e.Fragment=r,e.jsx=c,e.jsxs=c},7437:function(n,e,o){"use strict";n.exports=o(622)},4033:function(n,e,o){n.exports=o(290)}},function(n){n.O(0,[2971,7864,1744],function(){return n(n.s=3756)}),_N_E=n.O()}]);