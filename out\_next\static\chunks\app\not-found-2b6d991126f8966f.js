(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9160],{2401:function(e,n,o){var t={"./benefits.json":[6370,6370],"./common.json":[202,202],"./comparison.json":[1409,1409],"./contact.json":[4725,4725],"./demo-player.json":[8340,8340],"./demo.json":[4322,4322],"./editor.json":[4831,4831],"./faq-page.json":[4930,4930],"./faq.json":[4607,4607],"./features.json":[6328,6328],"./footer.json":[9565,9565],"./hero.json":[8526,4530],"./index.json":[6499,6499],"./introduction.json":[1662,1662],"./layout.json":[9710,9710],"./markdown-editor.json":[2295,2295],"./markdown-to-html-page.json":[1142,1142],"./markdown-to-html.json":[2937,2937],"./markdown-to-pdf-page.json":[7589,7589],"./markdown-to-pdf.json":[1807,1807],"./markdown-to-word-page.json":[7192,7192],"./markdown-to-word.json":[2496,2496],"./navbar.json":[1166,1166],"./not-found.json":[1636,1636],"./privacy.json":[8625,8625],"./tutorial.json":[2392,2392],"./use-cases.json":[2944,2944]};function s(e){if(!o.o(t,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=t[e],s=n[0];return o.e(n[1]).then(function(){return o.t(s,19)})}s.keys=function(){return Object.keys(t)},s.id=2401,e.exports=s},1539:function(e,n,o){var t={"./en/benefits.json":[6370,6370],"./en/common.json":[202,202],"./en/comparison.json":[1409,1409],"./en/contact.json":[4725,4725],"./en/demo-player.json":[8340,8340],"./en/demo.json":[4322,4322],"./en/editor.json":[4831,4831],"./en/faq-page.json":[4930,4930],"./en/faq.json":[4607,4607],"./en/features.json":[6328,6328],"./en/footer.json":[9565,9565],"./en/hero.json":[8526,4530],"./en/index.json":[6499,6499],"./en/introduction.json":[1662,1662],"./en/layout.json":[9710,9710],"./en/markdown-editor.json":[2295,2295],"./en/markdown-to-html-page.json":[1142,1142],"./en/markdown-to-html.json":[2937,2937],"./en/markdown-to-pdf-page.json":[7589,7589],"./en/markdown-to-pdf.json":[1807,1807],"./en/markdown-to-word-page.json":[7192,7192],"./en/markdown-to-word.json":[2496,2496],"./en/navbar.json":[1166,1166],"./en/not-found.json":[1636,1636],"./en/privacy.json":[8625,8625],"./en/tutorial.json":[2392,2392],"./en/use-cases.json":[2944,2944],"./ja/benefits.json":[1987,1987],"./ja/common.json":[808,808],"./ja/comparison.json":[9231,9231],"./ja/contact.json":[2012,2012],"./ja/demo-player.json":[3381,3381],"./ja/demo.json":[7862,7862],"./ja/editor.json":[2449,2449],"./ja/faq-page.json":[4028,4028],"./ja/faq.json":[5258,5258],"./ja/features.json":[3593,3593],"./ja/footer.json":[9105,9105],"./ja/hero.json":[6373,6373],"./ja/index.json":[3343,3343],"./ja/introduction.json":[7018,7018],"./ja/layout.json":[7006,7006],"./ja/markdown-editor.json":[4438,4438],"./ja/markdown-to-html-page.json":[1567,1567],"./ja/markdown-to-html.json":[7383,7383],"./ja/markdown-to-pdf-page.json":[5710,5710],"./ja/markdown-to-pdf.json":[7796,7796],"./ja/markdown-to-word-page.json":[4377,4377],"./ja/markdown-to-word.json":[4345,4345],"./ja/navbar.json":[7532,7532],"./ja/not-found.json":[1428,1428],"./ja/privacy.json":[9093,9093],"./ja/tutorial.json":[2572,2572],"./ja/use-cases.json":[1375,1375]};function s(e){if(!o.o(t,e))return Promise.resolve().then(function(){var n=Error("Cannot find module '"+e+"'");throw n.code="MODULE_NOT_FOUND",n});var n=t[e],s=n[0];return o.e(n[1]).then(function(){return o.t(s,19)})}s.keys=function(){return Object.keys(t)},s.id=1539,e.exports=s},1827:function(e,n,o){"use strict";o.d(n,{Z:function(){return s}});var t=o(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},3421:function(e,n,o){Promise.resolve().then(o.bind(o,8355))},8355:function(e,n,o){"use strict";o.r(n),o.d(n,{default:function(){return u}});var t=o(7437),s=o(1396),a=o.n(s),r=o(6637),c=o(5531);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,c.Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var i=o(1827);/**
 * @license lucide-react v0.446.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,c.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var j=o(4346);function u(){let{t:e}=(0,j.$G)("not-found");return(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center px-4",children:(0,t.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"text-9xl font-bold text-blue-600/20 select-none",children:"404"}),(0,t.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,t.jsx)(r.Z,{className:"h-24 w-24 text-blue-600"})})]})}),(0,t.jsx)("h1",{className:"text-4xl sm:text-5xl font-bold text-gray-900 mb-4",children:e("title")}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-lg mx-auto",children:e("description")}),(0,t.jsxs)("div",{className:"space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center mb-12",children:[(0,t.jsxs)(a(),{href:"/",className:"inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold",children:[(0,t.jsx)(l,{className:"h-5 w-5 mr-2"}),e("buttons.backToHome")]}),(0,t.jsxs)(a(),{href:"/faq",className:"inline-flex items-center border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold",children:[(0,t.jsx)(i.Z,{className:"h-5 w-5 mr-2"}),e("buttons.browseFaq")]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-3xl mx-auto",children:[(0,t.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20",children:[(0,t.jsx)(r.Z,{className:"h-8 w-8 text-blue-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Start Editing"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Jump straight into our Markdown editor"})]}),(0,t.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20",children:[(0,t.jsx)(i.Z,{className:"h-8 w-8 text-green-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Get Help"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Find answers in our FAQ section"})]}),(0,t.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20",children:[(0,t.jsx)(d,{className:"h-8 w-8 text-purple-600 mx-auto mb-3"}),(0,t.jsx)("h3",{className:"font-semibold text-gray-900 mb-2",children:"Go Back"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Return to the previous page"})]})]}),(0,t.jsx)("div",{className:"mt-12 text-center",children:(0,t.jsxs)("p",{className:"text-gray-500 text-sm",children:["Lost? Try searching for what you need or",(0,t.jsx)(a(),{href:"/contact",className:"text-blue-600 hover:text-blue-700 ml-1",children:"contact our support team"})]})})]})})}},4346:function(e,n,o){"use strict";o.d(n,{$G:function(){return s},bU:function(){return a}});var t=o(3275);function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{t:n,locale:o}=(0,t.QT)();return{t:(o,t)=>n(o,e,t),locale:o,ready:!0}}function a(){let{locale:e,changeLanguage:n}=(0,t.QT)();return{locale:e,locales:["en","ja"],defaultLocale:"en",changeLanguage:n,isRTL:!1}}},3275:function(e,n,o){"use strict";o.d(n,{QT:function(){return l},XJ:function(){return i},bd:function(){return c}});var t=o(7437),s=o(2265),a=o(4033);let r=(0,s.createContext)(void 0);function c(e){let{children:n,locale:o,translations:c}=e,[l,d]=(0,s.useState)(o),[j,u]=(0,s.useState)(c);(0,a.useRouter)(),(0,a.usePathname)();let m=async e=>{if(e!==l)try{localStorage.setItem("locale",e);let n=Object.keys(j),o=await i(e,n);d(e),u(o)}catch(e){console.error("Failed to change language:",e),localStorage.setItem("locale",l)}};return(0,s.useEffect)(()=>{d(o),u(c)},[o,c]),(0,t.jsx)(r.Provider,{value:{locale:l,t:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"common",o=arguments.length>2?arguments[2]:void 0,t=e.split("."),s=j[n];for(let a of t){if(!s||"object"!=typeof s||!(a in s))return console.warn("Translation key not found: ".concat(n,".").concat(e)),(null==o?void 0:o.returnObjects)?[]:e;s=s[a]}return(null==o?void 0:o.returnObjects)?s:"string"==typeof s?s:e},changeLanguage:m,translations:j},children:n})}function l(){let e=(0,s.useContext)(r);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}async function i(e,n){let t={};for(let s of n)try{let n=await o(1539)("./".concat(e,"/").concat(s,".json"));t[s]=n.default||n}catch(n){if(console.warn("Failed to load translation: ".concat(e,"/").concat(s,".json")),"en"!==e)try{let e=await o(2401)("./".concat(s,".json"));t[s]=e.default||e}catch(e){console.warn("Failed to load fallback translation: en/".concat(s,".json")),t[s]={}}else t[s]={}}return t}}},function(e){e.O(0,[6347,2971,7864,1744],function(){return e(e.s=3421)}),_N_E=e.O()}]);