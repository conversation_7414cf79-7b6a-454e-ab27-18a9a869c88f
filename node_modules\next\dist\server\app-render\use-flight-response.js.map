{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "names": ["useFlightResponse", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "writable", "req", "clientReferenceManifest", "rscChunks", "flightResponseRef", "nonce", "current", "createFromReadableStream", "require", "renderStream", "forwardStream", "tee", "res", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "bootstrapped", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "writer", "getWriter", "startScriptTag", "JSON", "stringify", "textDecoder", "TextDecoder", "read", "then", "done", "value", "push", "write", "encodeText", "htmlEscapeJsonString", "setTimeout", "close", "responsePartial", "decodeText", "scripts"], "mappings": ";;;;+BAWgBA;;;eAAAA;;;8BATuB;4BACF;AAErC,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAM5C,SAASJ,kBACdK,QAAoC,EACpCC,GAA+B,EAC/BC,uBAAgD,EAChDC,SAAuB,EACvBC,iBAAoC,EACpCC,KAAc;IAEd,IAAID,kBAAkBE,OAAO,KAAK,MAAM;QACtC,OAAOF,kBAAkBE,OAAO;IAClC;IACA,wGAAwG;IACxG,MAAM,EACJC,wBAAwB,EACzB,GAAGC,QAAQ,CAAC,oCAAoC,CAAC;IAElD,MAAM,CAACC,cAAcC,cAAc,GAAGT,IAAIU,GAAG;IAC7C,MAAMC,MAAML,yBAAyBE,cAAc;QACjDI,WAAWjB,gBACPM,wBAAwBY,oBAAoB,GAC5CZ,wBAAwBa,gBAAgB;IAC9C;IACAX,kBAAkBE,OAAO,GAAGM;IAE5B,IAAII,eAAe;IACnB,iDAAiD;IACjD,MAAMC,gBAAgBP,cAAcQ,SAAS;IAC7C,MAAMC,SAASnB,SAASoB,SAAS;IACjC,MAAMC,iBAAiBhB,QACnB,CAAC,cAAc,EAAEiB,KAAKC,SAAS,CAAClB,OAAO,CAAC,CAAC,GACzC;IACJ,MAAMmB,cAAc,IAAIC;IAExB,SAASC;QACPT,cAAcS,IAAI,GAAGC,IAAI,CAAC,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAE;YACxC,IAAIA,OAAO;gBACT1B,UAAU2B,IAAI,CAACD;YACjB;YAEA,IAAI,CAACb,cAAc;gBACjBA,eAAe;gBACfG,OAAOY,KAAK,CACVC,IAAAA,wBAAU,EACR,CAAC,EAAEX,eAAe,uCAAuC,EAAEY,IAAAA,gCAAoB,EAC7EX,KAAKC,SAAS,CAAC;oBAAC;iBAAE,GAClB,UAAU,CAAC;YAGnB;YACA,IAAIK,MAAM;gBACR,iIAAiI;gBACjI,iEAAiE;gBACjE,+IAA+I;gBAC/I,iDAAiD;gBACjDM,WAAW;oBACT9B,kBAAkBE,OAAO,GAAG;gBAC9B;gBACAa,OAAOgB,KAAK;YACd,OAAO;gBACL,MAAMC,kBAAkBC,IAAAA,wBAAU,EAACR,OAAOL;gBAC1C,MAAMc,UAAU,CAAC,EAAEjB,eAAe,mBAAmB,EAAEY,IAAAA,gCAAoB,EACzEX,KAAKC,SAAS,CAAC;oBAAC;oBAAGa;iBAAgB,GACnC,UAAU,CAAC;gBAEbjB,OAAOY,KAAK,CAACC,IAAAA,wBAAU,EAACM;gBACxBZ;YACF;QACF;IACF;IACAA;IAEA,OAAOd;AACT"}