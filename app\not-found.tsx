'use client';

import Link from 'next/link';
import { FileText, Home, Search, ArrowLeft } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

export default function NotFound() {
  const { t } = useTranslation('not-found');
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center px-4">
      <div className="max-w-2xl mx-auto text-center">
        <div className="mb-8">
          <div className="relative">
            <div className="text-9xl font-bold text-blue-600/20 select-none">404</div>
            <div className="absolute inset-0 flex items-center justify-center">
              <FileText className="h-24 w-24 text-blue-600" />
            </div>
          </div>
        </div>
        
        <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
          {t('title')}
        </h1>

        <p className="text-xl text-gray-600 mb-8 max-w-lg mx-auto">
          {t('description')}
        </p>
        
        <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center mb-12">
          <Link 
            href="/"
            className="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-semibold"
          >
            <Home className="h-5 w-5 mr-2" />
            {t('buttons.backToHome')}
          </Link>
          
          <Link 
            href="/faq"
            className="inline-flex items-center border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold"
          >
            <Search className="h-5 w-5 mr-2" />
            {t('buttons.browseFaq')}
          </Link>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-3xl mx-auto">
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <FileText className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Start Editing</h3>
            <p className="text-sm text-gray-600">
              Jump straight into our Markdown editor
            </p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <Search className="h-8 w-8 text-green-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Get Help</h3>
            <p className="text-sm text-gray-600">
              Find answers in our FAQ section
            </p>
          </div>
          
          <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <ArrowLeft className="h-8 w-8 text-purple-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Go Back</h3>
            <p className="text-sm text-gray-600">
              Return to the previous page
            </p>
          </div>
        </div>
        
        <div className="mt-12 text-center">
          <p className="text-gray-500 text-sm">
            Lost? Try searching for what you need or 
            <Link href="/contact" className="text-blue-600 hover:text-blue-700 ml-1">
              contact our support team
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}