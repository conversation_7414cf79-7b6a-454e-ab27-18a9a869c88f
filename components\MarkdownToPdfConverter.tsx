'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslation } from '@/hooks/useTranslation';
import { 
  FileText, 
  Download, 
  Upload, 
  Settings, 
  Eye, 
  Palette,
  FileDown,
  Zap,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Copy,
  Image as ImageIcon,
  Type,
  Layout,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface PdfSettings {
  pageSize: 'A4' | 'Letter' | 'Legal';
  orientation: 'portrait' | 'landscape';
  margin: 'normal' | 'narrow' | 'wide';
  fontSize: 'small' | 'medium' | 'large';
  theme: 'default' | 'github' | 'academic' | 'minimal';
  includeTableOfContents: boolean;
  includePageNumbers: boolean;
  headerText: string;
  footerText: string;
}

const defaultMarkdown = `# Markdown to PDF Converter

Welcome to our **free online Markdown to PDF converter**! This powerful tool allows you to transform your Markdown documents into professional PDF files with customizable styling and formatting options.

## Key Features

### ✅ Professional PDF Output
- High-quality PDF generation with crisp text and images
- Multiple page size options (A4, Letter, Legal)
- Portrait and landscape orientation support
- Customizable margins and spacing

### ✅ Advanced Formatting Options
- **Multiple themes**: Default, GitHub, Academic, Minimal
- **Typography control**: Font size and family options
- **Page elements**: Headers, footers, and page numbers
- **Table of contents**: Automatic generation from headings

### ✅ Complete Markdown Support
- All standard Markdown syntax elements
- Tables with proper formatting
- Code blocks with syntax highlighting
- Images and media embedding
- Mathematical expressions (LaTeX)

## How to Use

1. **Input your Markdown**: Paste or type your content in the editor
2. **Customize settings**: Choose page size, theme, and formatting options
3. **Preview**: See how your PDF will look before downloading
4. **Download**: Generate and save your professional PDF

## Supported Markdown Elements

### Text Formatting
- **Bold text** and *italic text*
- ~~Strikethrough text~~
- \`Inline code\`
- [Links](https://example.com)

### Lists
#### Unordered Lists
- Item 1
- Item 2
  - Nested item
  - Another nested item
- Item 3

#### Ordered Lists
1. First item
2. Second item
3. Third item

### Code Blocks
\`\`\`javascript
function convertToPdf() {
  console.log("Converting Markdown to PDF...");
  return "Success!";
}
\`\`\`

### Tables
| Feature | Free Version | Premium |
|---------|-------------|---------|
| Basic Conversion | ✅ | ✅ |
| Custom Themes | ✅ | ✅ |
| Advanced Settings | ✅ | ✅ |
| Batch Processing | ❌ | ✅ |

### Blockquotes
> "The best way to predict the future is to create it."
> 
> — Peter Drucker

### Mathematical Expressions
Inline math: $E = mc^2$

Block math:
$$
\\sum_{i=1}^{n} x_i = \\frac{1}{n} \\sum_{i=1}^{n} x_i
$$

---

## Why Choose Our Converter?

### 🚀 Fast and Reliable
- Instant conversion with no waiting time
- Handles large documents efficiently
- Reliable output every time

### 🔒 Privacy First
- All processing happens in your browser
- No data sent to external servers
- Your documents remain completely private

### 💰 Completely Free
- No registration required
- No watermarks on output
- Unlimited conversions

### 🎨 Professional Results
- Publication-ready PDF output
- Consistent formatting across platforms
- Print-optimized layouts

---

**Ready to convert?** Start by editing this sample content or paste your own Markdown text above. Customize the settings to match your preferences and click "Generate PDF" to create your professional document.

Happy converting! 📄✨`;

export default function MarkdownToPdfConverter() {
  const { t } = useTranslation('markdown-to-pdf');
  const [markdown, setMarkdown] = useState(defaultMarkdown);
  const [html, setHtml] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [conversionStatus, setConversionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [settings, setSettings] = useState<PdfSettings>({
    pageSize: 'A4',
    orientation: 'portrait',
    margin: 'normal',
    fontSize: 'medium',
    theme: 'default',
    includeTableOfContents: false,
    includePageNumbers: true,
    headerText: '',
    footerText: ''
  });

  // Enhanced markdown to HTML converter
  const convertMarkdownToHtml = (markdown: string) => {
    if (!markdown) return '';
    
    let html = markdown
      // Headers with IDs for TOC
      .replace(/^### (.*$)/gim, '<h3 id="$1">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 id="$1">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 id="$1">$1</h1>')
      // Images
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" class="pdf-image" />')
      // Bold and italic
      .replace(/\*\*\*(.*?)\*\*\*/gim, '<strong><em>$1</em></strong>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      .replace(/~~(.*?)~~/gim, '<del>$1</del>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre class="code-block"><code class="language-$1">$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code class="inline-code">$1</code>')
      // Tables
      .replace(/^\|(.+)\|$/gim, (match, content) => {
        const cells = content.split('|').map((cell: string) => cell.trim()).filter((cell: string) => cell);
        return '<tr>' + cells.map((cell: string) => `<td>${cell}</td>`).join('') + '</tr>';
      })
      // Lists
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
      // Blockquotes
      .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
      // Horizontal rules
      .replace(/^---$/gim, '<hr>')
      // Math expressions (simplified)
      .replace(/\$\$([\s\S]*?)\$\$/gim, '<div class="math-block">$1</div>')
      .replace(/\$([^$]+)\$/gim, '<span class="math-inline">$1</span>')
      // Line breaks
      .replace(/\n/gim, '<br>');

    // Wrap consecutive list items
    html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
    
    // Wrap table rows
    if (html.includes('<tr>')) {
      html = html.replace(/(<tr>.*<\/tr>)/s, '<table class="pdf-table">$1</table>');
    }
    
    return html;
  };

  // Generate table of contents
  const generateTableOfContents = (html: string) => {
    const headings = html.match(/<h[1-3][^>]*>([^<]+)<\/h[1-3]>/g) || [];
    if (headings.length === 0) return '';

    let toc = '<div class="table-of-contents"><h2>Table of Contents</h2><ul>';
    headings.forEach(heading => {
      const level = parseInt(heading.match(/<h([1-3])/)?.[1] || '1');
      const text = heading.replace(/<[^>]*>/g, '');
      const indent = '  '.repeat(level - 1);
      toc += `${indent}<li><a href="#${text}">${text}</a></li>`;
    });
    toc += '</ul></div>';
    
    return toc;
  };

  // Get CSS styles based on theme and settings
  const getThemeStyles = () => {
    const baseStyles = `
      @page {
        size: ${settings.pageSize} ${settings.orientation};
        margin: ${settings.margin === 'narrow' ? '0.5in' : settings.margin === 'wide' ? '1.5in' : '1in'};
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: ${settings.fontSize === 'small' ? '12px' : settings.fontSize === 'large' ? '16px' : '14px'};
        line-height: 1.6;
        color: #333;
        max-width: none;
        margin: 0;
        padding: 20px;
      }
      
      .pdf-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 50px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-bottom: 1px solid #eee;
        padding: 10px;
      }
      
      .pdf-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 50px;
        text-align: center;
        font-size: 12px;
        color: #666;
        border-top: 1px solid #eee;
        padding: 10px;
      }
      
      .table-of-contents {
        page-break-after: always;
        margin-bottom: 30px;
      }
      
      .table-of-contents ul {
        list-style: none;
        padding-left: 0;
      }
      
      .table-of-contents li {
        margin: 5px 0;
        padding-left: 20px;
      }
      
      h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        margin-top: 30px;
        margin-bottom: 15px;
      }
      
      h1 { font-size: 2em; color: #2c3e50; }
      h2 { font-size: 1.5em; color: #34495e; }
      h3 { font-size: 1.2em; color: #34495e; }
      
      .pdf-image {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 20px auto;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      }
      
      .code-block {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        font-family: 'Courier New', monospace;
        font-size: 13px;
      }
      
      .inline-code {
        background: #f1f3f4;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
      }
      
      .pdf-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }
      
      .pdf-table td, .pdf-table th {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
      }
      
      .pdf-table th {
        background-color: #f8f9fa;
        font-weight: bold;
      }
      
      blockquote {
        border-left: 4px solid #3498db;
        margin: 20px 0;
        padding-left: 20px;
        color: #666;
        font-style: italic;
      }
      
      hr {
        border: none;
        border-top: 2px solid #eee;
        margin: 30px 0;
      }
      
      .math-block {
        text-align: center;
        margin: 20px 0;
        font-family: 'Times New Roman', serif;
        font-size: 1.1em;
      }
      
      .math-inline {
        font-family: 'Times New Roman', serif;
      }
    `;

    // Theme-specific styles
    const themeStyles = {
      default: '',
      github: `
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif; }
        h1, h2 { border-bottom: 1px solid #eaecef; padding-bottom: 10px; }
        .code-block { background: #f6f8fa; }
      `,
      academic: `
        body { font-family: 'Times New Roman', serif; }
        h1, h2, h3 { font-family: 'Times New Roman', serif; }
        .code-block { font-family: 'Courier New', monospace; }
      `,
      minimal: `
        body { font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif; }
        h1, h2, h3 { font-weight: 300; }
        .code-block { background: #fafafa; border: none; }
      `
    };

    return baseStyles + themeStyles[settings.theme];
  };

  // Convert markdown to HTML
  useEffect(() => {
    const htmlContent = convertMarkdownToHtml(markdown);
    setHtml(htmlContent);
  }, [markdown]);

  // Auto-save to localStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      localStorage.setItem('pdf-markdown-content', markdown);
      localStorage.setItem('pdf-settings', JSON.stringify(settings));
    }, 1000);

    return () => clearTimeout(timer);
  }, [markdown, settings]);

  // Load saved content on mount
  useEffect(() => {
    const saved = localStorage.getItem('pdf-markdown-content');
    const savedSettings = localStorage.getItem('pdf-settings');
    
    if (saved) {
      setMarkdown(saved);
    }
    
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to parse saved settings:', error);
      }
    }
  }, []);

  // Generate PDF
  const generatePdf = async () => {
    setIsGenerating(true);
    setConversionStatus('idle');
    
    try {
      // Simulate PDF generation (in a real implementation, you'd use a library like jsPDF or Puppeteer)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create a complete HTML document
      let fullHtml = html;
      
      // Add table of contents if enabled
      if (settings.includeTableOfContents) {
        const toc = generateTableOfContents(html);
        fullHtml = toc + fullHtml;
      }
      
      // Create the complete HTML document
      const completeHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>Markdown Document</title>
          <style>${getThemeStyles()}</style>
        </head>
        <body>
          ${settings.headerText ? `<div class="pdf-header">${settings.headerText}</div>` : ''}
          ${fullHtml}
          ${settings.footerText ? `<div class="pdf-footer">${settings.footerText}</div>` : ''}
        </body>
        </html>
      `;
      
      // For demonstration, we'll create a downloadable HTML file
      // In a real implementation, you'd convert this to PDF
      const blob = new Blob([completeHtml], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'document.html'; // In real implementation: 'document.pdf'
      a.click();
      URL.revokeObjectURL(url);
      
      setConversionStatus('success');
    } catch (error) {
      console.error('PDF generation failed:', error);
      setConversionStatus('error');
    } finally {
      setIsGenerating(false);
    }
  };

  // File upload handler
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setMarkdown(content);
      };
      reader.readAsText(file);
    }
  };

  // Copy to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(markdown);
      console.log('Markdown copied to clipboard');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'min-h-screen'} bg-gray-50`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 p-2 rounded-lg">
                <FileDown className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{t('title')}</h1>
                <p className="text-sm text-gray-600">{t('subtitle')}</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`p-2 rounded-lg transition-colors ${
                showPreview ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={showPreview ? 'Hide Preview' : 'Show Preview'}
            >
              <Eye className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setShowSettings(!showSettings)}
              className={`p-2 rounded-lg transition-colors ${
                showSettings ? 'bg-blue-100 text-blue-600' : 'hover:bg-gray-100 text-gray-600'
              }`}
              title="PDF Settings"
            >
              <Settings className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
              title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              {isFullscreen ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
            </button>
            
            <button
              onClick={generatePdf}
              disabled={isGenerating || !markdown.trim()}
              className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>{t('buttons.generating')}</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>{t('buttons.generatePdf')}</span>
                </>
              )}
            </button>
          </div>
        </div>
        
        {/* Status Messages */}
        {conversionStatus === 'success' && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="text-green-800">{t('status.success')}</span>
          </div>
        )}
        
        {conversionStatus === 'error' && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <span className="text-red-800">{t('status.error')}</span>
          </div>
        )}
      </div>

      <div className="flex" style={{ height: isFullscreen ? 'calc(100vh - 89px)' : 'calc(100vh - 89px)' }}>
        {/* Settings Panel */}
        {showSettings && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('settings.title')}</h3>
              
              {/* Page Settings */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Layout className="h-4 w-4 mr-2" />
                  {t('settings.pageSettings.title')}
                </h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Page Size</label>
                    <select
                      value={settings.pageSize}
                      onChange={(e) => setSettings(prev => ({ ...prev, pageSize: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="A4">A4</option>
                      <option value="Letter">Letter</option>
                      <option value="Legal">Legal</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Orientation</label>
                    <select
                      value={settings.orientation}
                      onChange={(e) => setSettings(prev => ({ ...prev, orientation: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="portrait">Portrait</option>
                      <option value="landscape">Landscape</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Margins</label>
                    <select
                      value={settings.margin}
                      onChange={(e) => setSettings(prev => ({ ...prev, margin: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="narrow">Narrow</option>
                      <option value="normal">Normal</option>
                      <option value="wide">Wide</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Typography */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Type className="h-4 w-4 mr-2" />
                  Typography
                </h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Font Size</label>
                    <select
                      value={settings.fontSize}
                      onChange={(e) => setSettings(prev => ({ ...prev, fontSize: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                      <option value="large">Large</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Theme</label>
                    <select
                      value={settings.theme}
                      onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="default">Default</option>
                      <option value="github">GitHub</option>
                      <option value="academic">Academic</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Document Options */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Document Options</h4>
                
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.includeTableOfContents}
                      onChange={(e) => setSettings(prev => ({ ...prev, includeTableOfContents: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Include Table of Contents</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.includePageNumbers}
                      onChange={(e) => setSettings(prev => ({ ...prev, includePageNumbers: e.target.checked }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Include Page Numbers</span>
                  </label>
                </div>
              </div>
              
              {/* Headers and Footers */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Headers & Footers</h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Header Text</label>
                    <input
                      type="text"
                      value={settings.headerText}
                      onChange={(e) => setSettings(prev => ({ ...prev, headerText: e.target.value }))}
                      placeholder="Optional header text"
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Footer Text</label>
                    <input
                      type="text"
                      value={settings.footerText}
                      onChange={(e) => setSettings(prev => ({ ...prev, footerText: e.target.value }))}
                      placeholder="Optional footer text"
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Editor Panel */}
        <div className={`${showPreview ? (showSettings ? 'w-1/2' : 'w-1/2') : 'w-full'} flex flex-col`}>
          <div className="bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">{t('panels.markdownEditor')}</span>
            <div className="flex items-center space-x-2">
              <input
                ref={fileInputRef}
                type="file"
                accept=".md,.markdown"
                onChange={handleFileUpload}
                className="hidden"
              />
              
              <button
                onClick={() => fileInputRef.current?.click()}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title="Upload Markdown File"
              >
                <Upload className="h-4 w-4" />
              </button>
              
              <button
                onClick={copyToClipboard}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title="Copy Markdown"
              >
                <Copy className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => setMarkdown('')}
                className="text-xs px-2 py-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
              >
                Clear
              </button>
            </div>
          </div>
          
          <textarea
            ref={textareaRef}
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
            className="flex-1 p-4 font-mono text-sm resize-none focus:outline-none bg-white"
            placeholder="Paste your Markdown content here or start typing..."
            spellCheck={false}
          />
        </div>

        {/* Preview Panel */}
        {showPreview && (
          <div className={`${showSettings ? 'w-1/2' : 'w-1/2'} flex flex-col border-l border-gray-200`}>
            <div className="bg-gray-100 border-b border-gray-200 px-4 py-2">
              <span className="text-sm font-medium text-gray-700">{t('panels.pdfPreview')}</span>
            </div>
            
            <div
              ref={previewRef}
              className="flex-1 p-4 overflow-auto bg-white"
              style={{ fontFamily: settings.theme === 'academic' ? 'Times New Roman, serif' : 'inherit' }}
            >
              {settings.includeTableOfContents && html && (
                <div 
                  className="mb-8 pb-8 border-b border-gray-200"
                  dangerouslySetInnerHTML={{ __html: generateTableOfContents(html) }}
                />
              )}
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: html }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}