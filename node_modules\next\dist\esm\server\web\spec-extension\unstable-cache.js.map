{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["staticGenerationAsyncStorage", "_staticGenerationAsyncStorage", "CACHE_ONE_YEAR", "addImplicitTags", "validateTags", "unstable_cache", "cb", "keyParts", "options", "fetch", "__nextGetStaticStore", "revalidate", "Error", "toString", "cachedCb", "args", "store", "getStore", "incrementalCache", "globalThis", "__incrementalCache", "joinedKey", "Array", "isArray", "join", "JSON", "stringify", "run", "fetchCache", "urlPathname", "isStaticGeneration", "tags", "tag", "includes", "push", "implicitTags", "cache<PERSON>ey", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cacheEntry", "isOnDemandRevalidate", "get", "softTags", "invokeCallback", "result", "set", "kind", "data", "headers", "body", "status", "url", "value", "console", "error", "cachedValue", "isStale", "resData", "parse", "pendingRevalidates", "catch", "err"], "mappings": "AAAA,SAEEA,gCAAgCC,6BAA6B,QAExD,sEAAqE;AAC5E,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,eAAe,EAAEC,YAAY,QAAQ,wBAAuB;AAIrE,OAAO,SAASC,eACdC,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,MAAMR,+BACJ,CAAA,AAACS,MAAcC,oBAAoB,oBAAnC,AAACD,MAAcC,oBAAoB,MAAlCD,WAA0CR;IAE7C,IAAIO,QAAQG,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIC,MACR,CAAC,wFAAwF,EAAEN,GAAGO,QAAQ,GAAG,CAAC;IAE9G;IAEA,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QACJhB,gDAAAA,6BAA8BiB,QAAQ;QAExC,MAAMC,mBAGJF,CAAAA,yBAAAA,MAAOE,gBAAgB,KAAI,AAACC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,kBAAkB;YACrB,MAAM,IAAIN,MACR,CAAC,sDAAsD,EAAEN,GAAGO,QAAQ,GAAG,CAAC;QAE5E;QAEA,MAAMQ,YAAY,CAAC,EAAEf,GAAGO,QAAQ,GAAG,CAAC,EAClCS,MAAMC,OAAO,CAAChB,aAAaA,SAASiB,IAAI,CAAC,KAC1C,CAAC,EAAEC,KAAKC,SAAS,CAACX,MAAM,CAAC;QAE1B,6DAA6D;QAC7D,oEAAoE;QACpE,oEAAoE;QACpE,0BAA0B;QAC1B,OAAOf,6BAA6B2B,GAAG,CACrC;YACE,GAAGX,KAAK;YACRY,YAAY;YACZC,aAAab,CAAAA,yBAAAA,MAAOa,WAAW,KAAI;YACnCC,oBAAoB,CAAC,EAACd,yBAAAA,MAAOc,kBAAkB;QACjD,GACA;YACE,MAAMC,OAAO3B,aACXI,QAAQuB,IAAI,IAAI,EAAE,EAClB,CAAC,eAAe,EAAEzB,GAAGO,QAAQ,GAAG,CAAC;YAGnC,IAAIS,MAAMC,OAAO,CAACQ,SAASf,OAAO;gBAChC,IAAI,CAACA,MAAMe,IAAI,EAAE;oBACff,MAAMe,IAAI,GAAG,EAAE;gBACjB;gBACA,KAAK,MAAMC,OAAOD,KAAM;oBACtB,IAAI,CAACf,MAAMe,IAAI,CAACE,QAAQ,CAACD,MAAM;wBAC7BhB,MAAMe,IAAI,CAACG,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,MAAMG,eAAehC,gBAAgBa;YAErC,MAAMoB,WAAW,OAAMlB,oCAAAA,iBAAkBmB,aAAa,CAAChB;YACvD,MAAMiB,aACJF,YACA,CACEpB,CAAAA,CAAAA,yBAAAA,MAAOuB,oBAAoB,KAAIrB,iBAAiBqB,oBAAoB,AAAD,KAEpE,OAAMrB,oCAAAA,iBAAkBsB,GAAG,CAACJ,UAAU;gBACrCR,YAAY;gBACZjB,YAAYH,QAAQG,UAAU;gBAC9BoB;gBACAU,UAAUN;YACZ;YAEF,MAAMO,iBAAiB;gBACrB,MAAMC,SAAS,MAAMrC,MAAMS;gBAE3B,IAAIqB,YAAYlB,kBAAkB;oBAChC,MAAMA,iBAAiB0B,GAAG,CACxBR,UACA;wBACES,MAAM;wBACNC,MAAM;4BACJC,SAAS,CAAC;4BACV,gCAAgC;4BAChCC,MAAMvB,KAAKC,SAAS,CAACiB;4BACrBM,QAAQ;4BACRC,KAAK;wBACP;wBACAvC,YACE,OAAOH,QAAQG,UAAU,KAAK,WAC1BT,iBACAM,QAAQG,UAAU;oBAC1B,GACA;wBACEA,YAAYH,QAAQG,UAAU;wBAC9BiB,YAAY;wBACZG;oBACF;gBAEJ;gBACA,OAAOY;YACT;YAEA,IAAI,CAACL,cAAc,CAACA,WAAWa,KAAK,EAAE;gBACpC,OAAOT;YACT;YAEA,IAAIJ,WAAWa,KAAK,CAACN,IAAI,KAAK,SAAS;gBACrCO,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEhC,UAAU,CAAC;gBAE1D,OAAOqB;YACT;YACA,IAAIY;YACJ,MAAMC,UAAUjB,WAAWiB,OAAO;YAElC,IAAIjB,YAAY;gBACd,MAAMkB,UAAUlB,WAAWa,KAAK,CAACL,IAAI;gBACrCQ,cAAc7B,KAAKgC,KAAK,CAACD,QAAQR,IAAI;YACvC;YAEA,IAAIO,SAAS;gBACX,IAAI,CAACvC,OAAO;oBACV,OAAO0B;gBACT,OAAO;oBACL,IAAI,CAAC1B,MAAM0C,kBAAkB,EAAE;wBAC7B1C,MAAM0C,kBAAkB,GAAG,EAAE;oBAC/B;oBACA1C,MAAM0C,kBAAkB,CAACxB,IAAI,CAC3BQ,iBAAiBiB,KAAK,CAAC,CAACC,MACtBR,QAAQC,KAAK,CAAC,CAAC,6BAA6B,EAAEhC,UAAU,CAAC,EAAEuC;gBAGjE;YACF;YACA,OAAON;QACT;IAEJ;IACA,yGAAyG;IACzG,OAAOxC;AACT"}