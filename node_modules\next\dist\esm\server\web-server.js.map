{"version": 3, "sources": ["../../src/server/web-server.ts"], "names": ["byteLength", "BaseServer", "NoFallbackError", "generateETag", "addRequestMeta", "WebResponseCache", "isAPIRoute", "removeTrailingSlash", "isDynamicRoute", "interpolateDynamicPath", "normalizeVercelUrl", "getNamedRouteRegex", "IncrementalCache", "NextWebServer", "constructor", "options", "Object", "assign", "renderOpts", "webServerConfig", "extendRenderOpts", "getIncrementalCache", "requestHeaders", "dev", "requestProtocol", "appDir", "hasAppDir", "allowedRevalidateHeaderKeys", "nextConfig", "experimental", "minimalMode", "fetchCache", "fetchCacheKeyPrefix", "maxMemoryCacheSize", "isrMemoryCacheSize", "flushToDisk", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "serverOptions", "incremental<PERSON>ache<PERSON><PERSON><PERSON>", "getPrerenderManifest", "getResponseCache", "hasPage", "page", "getBuildId", "buildId", "getHasAppDir", "pagesType", "getPagesManifest", "pathname", "getAppPathsManifest", "attachRequestMeta", "req", "parsedUrl", "query", "prerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "getNextFontManifest", "nextFontManifest", "handleCatchallRenderRequest", "res", "Error", "normalizedPage", "routeRegex", "keys", "routeKeys", "i18nProvider", "detectedLocale", "analyze", "__next<PERSON><PERSON><PERSON>", "bubbleNoFallback", "_nextBubbleNoFallback", "render", "finished", "err", "renderHTML", "renderToHTML", "disableOptimizedLoading", "runtime", "sendRenderResult", "_req", "<PERSON><PERSON><PERSON><PERSON>", "poweredByHeader", "type", "<PERSON><PERSON><PERSON><PERSON>", "result", "contentType", "isDynamic", "writer", "transformStream", "writable", "getWriter", "innerClose", "target", "write", "chunk", "end", "close", "on", "_event", "cb", "off", "_cb", "undefined", "onClose", "closed", "then", "pipe", "payload", "toUnchunkedString", "String", "generateEtags", "body", "send", "findPageComponents", "params", "loadComponent", "components", "run<PERSON><PERSON>", "handleApiRequest", "loadEnvConfig", "getPublicDir", "getHasStaticDir", "get<PERSON>allback", "getFontManifest", "handleCompression", "handleUpgrade", "getFallbackErrorComponents", "getRoutesManifest", "getMiddleware", "getFilesystemPaths", "Set", "getPrefetchRsc"], "mappings": "AAUA,SAASA,UAAU,QAAQ,kBAAiB;AAC5C,OAAOC,cAELC,eAAe,QAGV,gBAAe;AACtB,SAASC,YAAY,QAAQ,aAAY;AACzC,SAASC,cAAc,QAAQ,iBAAgB;AAC/C,OAAOC,sBAAsB,uBAAsB;AACnD,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,sBAAsB,EAAEC,kBAAkB,QAAQ,iBAAgB;AAC3E,SAASC,kBAAkB,QAAQ,yCAAwC;AAC3E,SAASC,gBAAgB,QAAQ,0BAAyB;AAkB1D,eAAe,MAAMC,sBAAsBZ;IACzCa,YAAYC,OAAyB,CAAE;QACrC,KAAK,CAACA;QAEN,uBAAuB;QACvBC,OAAOC,MAAM,CAAC,IAAI,CAACC,UAAU,EAAEH,QAAQI,eAAe,CAACC,gBAAgB;IACzE;IAEUC,oBAAoB,EAC5BC,cAAc,EAGf,EAAE;QACD,MAAMC,MAAM,CAAC,CAAC,IAAI,CAACL,UAAU,CAACK,GAAG;QACjC,wCAAwC;QACxC,kDAAkD;QAClD,oBAAoB;QACpB,OAAO,IAAIX,iBAAiB;YAC1BW;YACAD;YACAE,iBAAiB;YACjBC,QAAQ,IAAI,CAACC,SAAS;YACtBC,6BACE,IAAI,CAACC,UAAU,CAACC,YAAY,CAACF,2BAA2B;YAC1DG,aAAa,IAAI,CAACA,WAAW;YAC7BC,YAAY;YACZC,qBAAqB,IAAI,CAACJ,UAAU,CAACC,YAAY,CAACG,mBAAmB;YACrEC,oBAAoB,IAAI,CAACL,UAAU,CAACC,YAAY,CAACK,kBAAkB;YACnEC,aAAa;YACbC,iBACE,IAAI,CAACC,aAAa,CAAClB,eAAe,CAACmB,uBAAuB;YAC5DC,sBAAsB,IAAM,IAAI,CAACA,oBAAoB;QACvD;IACF;IACUC,mBAAmB;QAC3B,OAAO,IAAInC,iBAAiB,IAAI,CAACyB,WAAW;IAC9C;IAEA,MAAgBW,QAAQC,IAAY,EAAE;QACpC,OAAOA,SAAS,IAAI,CAACL,aAAa,CAAClB,eAAe,CAACuB,IAAI;IACzD;IAEUC,aAAa;QACrB,OAAO,IAAI,CAACN,aAAa,CAAClB,eAAe,CAACC,gBAAgB,CAACwB,OAAO;IACpE;IAEUC,eAAe;QACvB,OAAO,IAAI,CAACR,aAAa,CAAClB,eAAe,CAAC2B,SAAS,KAAK;IAC1D;IAEUC,mBAAmB;QAC3B,OAAO;YACL,8DAA8D;YAC9D,CAAC,IAAI,CAACV,aAAa,CAAClB,eAAe,CAChC6B,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAACX,aAAa,CAAClB,eAAe,CAACuB,IAAI,CAAC,GAAG,CAAC;QACrE;IACF;IAEUO,sBAAsB;QAC9B,MAAMP,OAAO,IAAI,CAACL,aAAa,CAAClB,eAAe,CAACuB,IAAI;QACpD,OAAO;YACL,CAAC,IAAI,CAACL,aAAa,CAAClB,eAAe,CAACuB,IAAI,CAAC,EAAE,CAAC,GAAG,EAAEA,KAAK,GAAG,CAAC;QAC5D;IACF;IAEUQ,kBACRC,GAAmB,EACnBC,SAAiC,EACjC;QACAhD,eAAe+C,KAAK,qBAAqB;YAAE,GAAGC,UAAUC,KAAK;QAAC;IAChE;IAEUd,uBAAuB;YAE3B;QADJ,MAAM,EAAEe,iBAAiB,EAAE,GAAG,IAAI,CAACjB,aAAa,CAAClB,eAAe;QAChE,IAAI,EAAA,mBAAA,IAAI,CAACD,UAAU,qBAAf,iBAAiBK,GAAG,KAAI,CAAC+B,mBAAmB;YAC9C,OAAO;gBACLC,SAAS,CAAC;gBACVC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,gBAAgB,EAAE;gBAClBC,SAAS;oBACPC,eAAe;gBACjB;YACF;QACF;QACA,OAAON;IACT;IAEUO,sBAAsB;QAC9B,OAAO,IAAI,CAACxB,aAAa,CAAClB,eAAe,CAACC,gBAAgB,CAAC0C,gBAAgB;IAC7E;IAEA,MAAgBC,4BACdZ,GAAoB,EACpBa,GAAqB,EACrBZ,SAAiC,EACD;QAChC,IAAI,EAAEJ,QAAQ,EAAEK,KAAK,EAAE,GAAGD;QAC1B,IAAI,CAACJ,UAAU;YACb,MAAM,IAAIiB,MAAM;QAClB;QAEA,4DAA4D;QAC5D,+CAA+C;QAC/C,MAAMC,iBAAiB,IAAI,CAAC7B,aAAa,CAAClB,eAAe,CAAC6B,QAAQ;QAElE,IAAIA,aAAakB,gBAAgB;YAC/BlB,WAAWkB;YAEX,IAAI1D,eAAewC,WAAW;gBAC5B,MAAMmB,aAAaxD,mBAAmBqC,UAAU;gBAChDA,WAAWvC,uBAAuBuC,UAAUK,OAAOc;gBACnDzD,mBACEyC,KACA,MACAnC,OAAOoD,IAAI,CAACD,WAAWE,SAAS,GAChC,MACAF;YAEJ;QACF;QAEA,wDAAwD;QACxDnB,WAAWzC,oBAAoByC;QAE/B,IAAI,IAAI,CAACsB,YAAY,EAAE;YACrB,MAAM,EAAEC,cAAc,EAAE,GAAG,MAAM,IAAI,CAACD,YAAY,CAACE,OAAO,CAACxB;YAC3D,IAAIuB,gBAAgB;gBAClBnB,UAAUC,KAAK,CAACoB,YAAY,GAAGF;YACjC;QACF;QAEA,MAAMG,mBAAmB,CAAC,CAACrB,MAAMsB,qBAAqB;QAEtD,IAAIrE,WAAW0C,WAAW;YACxB,OAAOK,MAAMsB,qBAAqB;QACpC;QAEA,IAAI;YACF,MAAM,IAAI,CAACC,MAAM,CAACzB,KAAKa,KAAKhB,UAAUK,OAAOD,WAAW;YAExD,OAAO;gBACLyB,UAAU;YACZ;QACF,EAAE,OAAOC,KAAK;YACZ,IAAIA,eAAe5E,mBAAmBwE,kBAAkB;gBACtD,OAAO;oBACLG,UAAU;gBACZ;YACF;YACA,MAAMC;QACR;IACF;IAEUC,WACR5B,GAAmB,EACnBa,GAAoB,EACpBhB,QAAgB,EAChBK,KAAyB,EACzBnC,UAAsB,EACC;QACvB,MAAM,EAAE8D,YAAY,EAAE,GAAG,IAAI,CAAC3C,aAAa,CAAClB,eAAe;QAC3D,IAAI,CAAC6D,cAAc;YACjB,MAAM,IAAIf,MACR;QAEJ;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,IAAIjB,aAAc9B,CAAAA,WAAWK,GAAG,GAAG,eAAe,aAAY,GAAI;YAChEyB,WAAW;QACb;QACA,OAAOgC,aACL7B,KACAa,KACAhB,UACAK,OACArC,OAAOC,MAAM,CAACC,YAAY;YACxB+D,yBAAyB;YACzBC,SAAS;QACX;IAEJ;IAEA,MAAgBC,iBACdC,IAAoB,EACpBpB,GAAoB,EACpBjD,OAMC,EACc;QACfiD,IAAIqB,SAAS,CAAC,kBAAkB;QAEhC,yBAAyB;QACzB,iEAAiE;QACjE,IAAItE,QAAQuE,eAAe,IAAIvE,QAAQwE,IAAI,KAAK,QAAQ;YACtDvB,IAAIqB,SAAS,CAAC,gBAAgB;QAChC;QAEA,IAAI,CAACrB,IAAIwB,SAAS,CAAC,iBAAiB;YAClCxB,IAAIqB,SAAS,CACX,gBACAtE,QAAQ0E,MAAM,CAACC,WAAW,GACtB3E,QAAQ0E,MAAM,CAACC,WAAW,GAC1B3E,QAAQwE,IAAI,KAAK,SACjB,qBACA;QAER;QAEA,IAAIxE,QAAQ0E,MAAM,CAACE,SAAS,EAAE;YAC5B,MAAMC,SAAS5B,IAAI6B,eAAe,CAACC,QAAQ,CAACC,SAAS;YAErD,IAAIC;YACJ,MAAMC,SAAS;gBACbC,OAAO,CAACC,QAAsBP,OAAOM,KAAK,CAACC;gBAC3CC,KAAK,IAAMR,OAAOS,KAAK;gBAEvBC,IAAGC,MAAe,EAAEC,EAAc;oBAChCR,aAAaQ;gBACf;gBACAC,KAAIF,MAAe,EAAEG,GAAe;oBAClCV,aAAaW;gBACf;YACF;YACA,MAAMC,UAAU;gBACdZ,8BAAAA;YACF;YACA,uEAAuE;YACvE,wEAAwE;YACxE,uBAAuB;YACvBJ,OAAOiB,MAAM,CAACC,IAAI,CAACF,SAASA;YAC5B7F,QAAQ0E,MAAM,CAACsB,IAAI,CAACd;QACtB,OAAO;YACL,MAAMe,UAAU,MAAMjG,QAAQ0E,MAAM,CAACwB,iBAAiB;YACtDjD,IAAIqB,SAAS,CAAC,kBAAkB6B,OAAOlH,WAAWgH;YAClD,IAAIjG,QAAQoG,aAAa,EAAE;gBACzBnD,IAAIqB,SAAS,CAAC,QAAQlF,aAAa6G;YACrC;YACAhD,IAAIoD,IAAI,CAACJ;QACX;QAEAhD,IAAIqD,IAAI;IACV;IAEA,MAAgBC,mBAAmB,EACjC5E,IAAI,EACJW,KAAK,EACLkE,MAAM,EAMP,EAAE;QACD,MAAM9B,SAAS,MAAM,IAAI,CAACpD,aAAa,CAAClB,eAAe,CAACqG,aAAa,CAAC9E;QACtE,IAAI,CAAC+C,QAAQ,OAAO;QAEpB,OAAO;YACLpC,OAAO;gBACL,GAAIA,SAAS,CAAC,CAAC;gBACf,GAAIkE,UAAU,CAAC,CAAC;YAClB;YACAE,YAAYhC;QACd;IACF;IAEA,2EAA2E;IAC3E,+DAA+D;IAE/D,MAAgBiC,SAAS;QACvB,wDAAwD;QACxD,OAAO;IACT;IAEA,MAAgBC,mBAAmB;QACjC,4DAA4D;QAC5D,OAAO;IACT;IAEUC,gBAAgB;IACxB,2EAA2E;IAC3E,mBAAmB;IACrB;IAEUC,eAAe;QACvB,kDAAkD;QAClD,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAO;IACT;IAEA,MAAgBC,cAAc;QAC5B,OAAO;IACT;IAEUC,kBAAkB;QAC1B,OAAOrB;IACT;IAEUsB,oBAAoB;IAC5B,wEAAwE;IACxE,4EAA4E;IAC9E;IAEA,MAAgBC,gBAA+B;IAC7C,+CAA+C;IACjD;IAEA,MAAgBC,6BAAuE;QACrF,wEAAwE;QACxE,OAAO;IACT;IAEUC,oBAAyD;QACjE,4EAA4E;QAC5E,gDAAgD;QAChD,OAAOzB;IACT;IAEU0B,gBAAmD;QAC3D,yEAAyE;QACzE,gDAAgD;QAChD,OAAO1B;IACT;IAEU2B,qBAAqB;QAC7B,OAAO,IAAIC;IACb;IAEA,MAAgBC,iBAAyC;QACvD,OAAO;IACT;AACF"}