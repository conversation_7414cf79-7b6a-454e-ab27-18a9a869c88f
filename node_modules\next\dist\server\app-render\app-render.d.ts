/// <reference types="node" />
import type { IncomingMessage, ServerResponse } from 'http';
import type { DynamicParamTypesShort, RenderOpts, Segment } from './types';
import { NextParsedUrlQuery } from '../request-meta';
import RenderResult from '../render-result';
export type GetDynamicParamFromSegment = (segment: string) => {
    param: string;
    value: string | string[] | null;
    treeSegment: Segment;
    type: DynamicParamTypesShort;
} | null;
export declare function renderToHTMLOrFlight(req: IncomingMessage, res: ServerResponse, pagePath: string, query: NextParsedUrlQuery, renderOpts: RenderOpts): Promise<RenderResult>;
