'use client';

import { useState, useEffect, useRef } from 'react';
import {
  Download,
  Copy,
  FileText,
  Eye,
  Settings,
  Maximize2,
  Minimize2,
  Save,
  Upload,
  RefreshCw,
  Palette,
  Moon,
  Sun,
  Image as ImageIcon,
  X,
  Plus
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';



interface UploadedImage {
  id: string;
  name: string;
  url: string;
  size: number;
}

export default function MarkdownEditor() {
  const { t } = useTranslation('markdown-editor');

  // Generate default markdown content from translations
  const generateDefaultMarkdown = () => {
    const featuresData = t('defaultContent.features.list', { returnObjects: true });
    const features = Array.isArray(featuresData) ? featuresData as string[] : [];

    const unorderedItemsData = t('defaultContent.gettingStarted.lists.unordered.items', { returnObjects: true });
    const unorderedItems = Array.isArray(unorderedItemsData) ? unorderedItemsData as string[] : [];

    const orderedItemsData = t('defaultContent.gettingStarted.lists.ordered.items', { returnObjects: true });
    const orderedItems = Array.isArray(orderedItemsData) ? orderedItemsData as string[] : [];

    const tableHeadersData = t('defaultContent.gettingStarted.tables.headers', { returnObjects: true });
    const tableHeaders = Array.isArray(tableHeadersData) ? tableHeadersData as string[] : [];

    const tableRowsData = t('defaultContent.gettingStarted.tables.rows', { returnObjects: true });
    const tableRows = Array.isArray(tableRowsData) ? tableRowsData as string[][] : [];

    return `${t('defaultContent.title')}

${t('defaultContent.subtitle')}

${t('defaultContent.features.title')}

${features.map(feature => feature).join('\n')}

${t('defaultContent.gettingStarted.title')}

${t('defaultContent.gettingStarted.basicFormatting.title')}

${t('defaultContent.gettingStarted.basicFormatting.description')}

${t('defaultContent.gettingStarted.images.title')}

${t('defaultContent.gettingStarted.images.description')}

![Sample Image](https://images.pexels.com/photos/1181467/pexels-photo-1181467.jpeg?auto=compress&cs=tinysrgb&w=600)

${t('defaultContent.gettingStarted.lists.title')}

${t('defaultContent.gettingStarted.lists.unordered.title')}
${unorderedItems.join('\n')}

${t('defaultContent.gettingStarted.lists.ordered.title')}
${orderedItems.join('\n')}

${t('defaultContent.gettingStarted.code.title')}

${t('defaultContent.gettingStarted.code.inline')}

${t('defaultContent.gettingStarted.code.block')}
\`\`\`javascript
function greetUser(name) {
  return \`Hello, \${name}! Welcome to our free online Markdown editor.\`;
}

console.log(greetUser("${t('defaultContent.gettingStarted.code.exampleUser')}"));
\`\`\`

${t('defaultContent.gettingStarted.tables.title')}

| ${tableHeaders.join(' | ')} |
|${tableHeaders.map(() => '---------').join('|')}|
${tableRows.map(row => `| ${row.join(' | ')} |`).join('\n')}

${t('defaultContent.gettingStarted.links.title')}

${t('defaultContent.gettingStarted.links.example')}

${t('defaultContent.gettingStarted.blockquotes.title')}

${t('defaultContent.gettingStarted.blockquotes.quote')}

${t('defaultContent.gettingStarted.horizontalRule.title')}

${t('defaultContent.gettingStarted.horizontalRule.example')}

${t('defaultContent.math.title')}

You can write mathematical expressions using LaTeX syntax:

Inline math: $E = mc^2$

Block math:
$$
\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\cdots + x_n
$$

---

**Start editing above to see the live preview in action!** 🚀`;
  };
  const [markdown, setMarkdown] = useState('');
  const [html, setHtml] = useState('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [showImagePanel, setShowImagePanel] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Initialize default content when component mounts
  useEffect(() => {
    if (!markdown) {
      setMarkdown(generateDefaultMarkdown());
    }
  }, []);

  // Enhanced markdown to HTML converter with image support
  const convertMarkdownToHtml = (markdown: string) => {
    if (!markdown) return '';
    
    let html = markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      // Images (must come before links)
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" class="max-w-full h-auto rounded-lg shadow-sm my-4" />')
      // Bold and italic
      .replace(/\*\*\*(.*?)\*\*\*/gim, '<strong><em>$1</em></strong>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline">$1</a>')
      // Code blocks
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-x-auto my-4"><code class="language-$1 text-sm">$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm">$1</code>')
      // Tables
      .replace(/^\|(.+)\|$/gim, (match, content) => {
        const cells = content.split('|').map((cell: string) => cell.trim()).filter((cell: string) => cell);
        return '<tr>' + cells.map((cell: string) => `<td class="border border-gray-300 dark:border-gray-600 px-3 py-2">${cell}</td>`).join('') + '</tr>';
      })
      // Lists
      .replace(/^\- (.*$)/gim, '<li class="ml-4">$1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li class="ml-4">$1</li>')
      // Blockquotes
      .replace(/^> (.*$)/gim, '<blockquote class="border-l-4 border-blue-500 pl-4 italic my-4 text-gray-600 dark:text-gray-400">$1</blockquote>')
      // Horizontal rules
      .replace(/^---$/gim, '<hr class="my-6 border-gray-300 dark:border-gray-600">')
      // Line breaks
      .replace(/\n/gim, '<br>');

    // Wrap consecutive list items in ul tags
    html = html.replace(/(<li class="ml-4">.*<\/li>)/s, '<ul class="list-disc list-inside my-4">$1</ul>');
    
    // Wrap table rows in table
    if (html.includes('<tr>')) {
      html = html.replace(/(<tr>.*<\/tr>)/s, '<table class="w-full border-collapse my-4">$1</table>');
    }
    
    return html;
  };

  // Convert markdown to HTML
  useEffect(() => {
    const htmlContent = convertMarkdownToHtml(markdown);
    setHtml(htmlContent);
  }, [markdown]);

  // Auto-save to localStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      localStorage.setItem('markdown-content', markdown);
      localStorage.setItem('uploaded-images', JSON.stringify(uploadedImages));
    }, 1000);

    return () => clearTimeout(timer);
  }, [markdown, uploadedImages]);

  // Load saved content on mount
  useEffect(() => {
    const saved = localStorage.getItem('markdown-content');
    const savedImages = localStorage.getItem('uploaded-images');
    
    if (saved) {
      setMarkdown(saved);
    }
    
    if (savedImages) {
      try {
        setUploadedImages(JSON.parse(savedImages));
      } catch (error) {
        console.error('Failed to parse saved images:', error);
      }
    }
  }, []);

  // Synchronized scrolling
  const handleScroll = (source: 'editor' | 'preview') => {
    if (!textareaRef.current || !previewRef.current) return;

    const sourceElement = source === 'editor' ? textareaRef.current : previewRef.current;
    const targetElement = source === 'editor' ? previewRef.current : textareaRef.current;

    const sourceScrollRatio = sourceElement.scrollTop / (sourceElement.scrollHeight - sourceElement.clientHeight);
    const targetScrollTop = sourceScrollRatio * (targetElement.scrollHeight - targetElement.clientHeight);

    targetElement.scrollTop = targetScrollTop;
  };

  // Image upload handler
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          const newImage: UploadedImage = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            name: file.name,
            url: result,
            size: file.size
          };
          
          setUploadedImages(prev => [...prev, newImage]);
        };
        reader.readAsDataURL(file);
      }
    });
    
    // Clear the input
    if (event.target) {
      event.target.value = '';
    }
  };

  // Insert image into markdown
  const insertImage = (image: UploadedImage) => {
    const imageMarkdown = `![${image.name}](${image.url})`;
    const textarea = textareaRef.current;
    
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newMarkdown = markdown.substring(0, start) + imageMarkdown + markdown.substring(end);
      setMarkdown(newMarkdown);
      
      // Focus back to textarea and set cursor position
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + imageMarkdown.length, start + imageMarkdown.length);
      }, 0);
    }
  };

  // Remove uploaded image
  const removeImage = (imageId: string) => {
    setUploadedImages(prev => prev.filter(img => img.id !== imageId));
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Export functions
  const exportAsHTML = () => {
    const fullHTML = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${t('export.documentTitle')}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        pre { background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        img { max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); margin: 16px 0; }
    </style>
</head>
<body>
${html}
</body>
</html>`;

    const blob = new Blob([fullHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.html';
    a.click();
    URL.revokeObjectURL(url);
  };

  const exportAsMarkdown = () => {
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'document.md';
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyToClipboard = async (content: string, type: 'html' | 'markdown') => {
    try {
      await navigator.clipboard.writeText(content);
      console.log(`${type} copied to clipboard`);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setMarkdown(content);
      };
      reader.readAsText(file);
    }
  };

  const clearContent = () => {
    setMarkdown('');
  };

  const resetToDefault = () => {
    setMarkdown(generateDefaultMarkdown());
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'min-h-screen'} ${isDarkMode ? 'dark bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header */}
      <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-4 py-3`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <FileText className={`h-6 w-6 ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
              <h1 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {t('title')}
              </h1>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-700 text-gray-300' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
                title={t('tooltips.togglePreview')}
              >
                <Eye className="h-4 w-4" />
              </button>

              <button
                onClick={() => setShowImagePanel(!showImagePanel)}
                className={`p-2 rounded-lg transition-colors ${
                  showImagePanel
                    ? (isDarkMode ? 'bg-blue-600 text-white' : 'bg-blue-600 text-white')
                    : (isDarkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600')
                }`}
                title={t('tooltips.togglePreview')}
              >
                <ImageIcon className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => setIsDarkMode(!isDarkMode)}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-700 text-gray-300' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
                title={isDarkMode ? t('buttons.lightMode') : t('buttons.darkMode')}
              >
                {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </button>
              
              <button
                onClick={() => setIsFullscreen(!isFullscreen)}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-700 text-gray-300' 
                    : 'hover:bg-gray-100 text-gray-600'
                }`}
                title={t('tooltips.fullscreen')}
              >
                {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <input
              ref={fileInputRef}
              type="file"
              accept=".md,.markdown"
              onChange={handleFileUpload}
              className="hidden"
            />

            <input
              ref={imageInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              className="hidden"
            />
            
            <button
              onClick={() => fileInputRef.current?.click()}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-300' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={t('tooltips.uploadFile')}
            >
              <Upload className="h-4 w-4" />
            </button>

            <button
              onClick={() => imageInputRef.current?.click()}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-300' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={t('buttons.addImage')}
            >
              <ImageIcon className="h-4 w-4" />
            </button>

            <button
              onClick={() => copyToClipboard(html, 'html')}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-300' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={t('tooltips.copyToClipboard')}
            >
              <Copy className="h-4 w-4" />
            </button>

            <button
              onClick={exportAsHTML}
              className={`p-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-300' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={t('tooltips.downloadHtml')}
            >
              <Download className="h-4 w-4" />
            </button>

            <button
              onClick={exportAsMarkdown}
              className={`px-3 py-2 rounded-lg transition-colors ${
                isDarkMode 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
              title={t('tooltips.downloadMarkdown')}
            >
              <FileText className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex" style={{ height: isFullscreen ? 'calc(100vh - 73px)' : 'calc(100vh - 73px)' }}>
        {/* Image Panel */}
        {showImagePanel && (
          <div className={`w-64 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-r flex flex-col`}>
            <div className={`${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-100 border-gray-200'} border-b px-4 py-2 flex items-center justify-between`}>
              <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Images ({uploadedImages.length})
              </span>
              <button
                onClick={() => imageInputRef.current?.click()}
                className={`p-1 rounded transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-600 text-gray-400' 
                    : 'hover:bg-gray-200 text-gray-600'
                }`}
                title={t('buttons.addImage')}
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-2">
              {uploadedImages.length === 0 ? (
                <div className={`text-center py-8 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">{t('panels.noImages')}</p>
                  <button
                    onClick={() => imageInputRef.current?.click()}
                    className={`text-xs mt-2 px-3 py-1 rounded transition-colors ${
                      isDarkMode 
                        ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                  >
                    {t('buttons.upload')}
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  {uploadedImages.map((image) => (
                    <div
                      key={image.id}
                      className={`group relative rounded-lg overflow-hidden border ${
                        isDarkMode ? 'border-gray-600 hover:border-gray-500' : 'border-gray-200 hover:border-gray-300'
                      } transition-colors cursor-pointer`}
                      onClick={() => insertImage(image)}
                    >
                      <img
                        src={image.url}
                        alt={image.name}
                        className="w-full h-24 object-cover"
                      />
                      <div className={`absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center`}>
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                          <Plus className="h-6 w-6 text-white" />
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          removeImage(image.id);
                        }}
                        className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                      <div className={`absolute bottom-0 left-0 right-0 ${isDarkMode ? 'bg-gray-900' : 'bg-white'} bg-opacity-90 p-2`}>
                        <p className={`text-xs truncate ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                          {image.name}
                        </p>
                        <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {formatFileSize(image.size)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Editor Panel */}
        <div className={`${showPreview ? (showImagePanel ? 'w-1/2' : 'w-1/2') : 'w-full'} flex flex-col`}>
          <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} border-b px-4 py-2 flex items-center justify-between`}>
            <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {t('panels.editor')}
            </span>
            <div className="flex items-center space-x-2">
              <button
                onClick={clearContent}
                className={`text-xs px-2 py-1 rounded transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-700 text-gray-400' 
                    : 'hover:bg-gray-200 text-gray-600'
                }`}
              >
                {t('buttons.clear') || 'Clear'}
              </button>
              <button
                onClick={resetToDefault}
                className={`text-xs px-2 py-1 rounded transition-colors ${
                  isDarkMode 
                    ? 'hover:bg-gray-700 text-gray-400' 
                    : 'hover:bg-gray-200 text-gray-600'
                }`}
              >
                Reset
              </button>
            </div>
          </div>
          
          <textarea
            ref={textareaRef}
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
            onScroll={() => handleScroll('editor')}
            className={`flex-1 p-4 font-mono text-sm resize-none focus:outline-none ${
              isDarkMode 
                ? 'bg-gray-900 text-gray-100 placeholder-gray-500' 
                : 'bg-white text-gray-900 placeholder-gray-400'
            }`}
            placeholder={t('placeholders.startTyping')}
            spellCheck={false}
          />
        </div>

        {/* Preview Panel */}
        {showPreview && (
          <div className={`${showImagePanel ? 'w-1/2' : 'w-1/2'} flex flex-col border-l border-gray-200`}>
            <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} border-b px-4 py-2`}>
              <span className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('panels.preview')}
              </span>
            </div>
            
            <div
              ref={previewRef}
              onScroll={() => handleScroll('preview')}
              className={`flex-1 p-4 overflow-auto prose max-w-none ${
                isDarkMode 
                  ? 'bg-gray-900 prose-invert' 
                  : 'bg-white'
              }`}
              dangerouslySetInnerHTML={{ __html: html }}
            />
          </div>
        )}
      </div>
    </div>
  );
}