import { StaticGenerationStore } from '../client/components/static-generation-async-storage.external';
import { PipeTarget } from './pipe-readable';
type ContentTypeOption = string | undefined;
export type RenderResultMetadata = {
    pageData?: any;
    revalidate?: any;
    staticBailoutInfo?: any;
    assetQueryString?: string;
    isNotFound?: boolean;
    isRedirect?: boolean;
    fetchMetrics?: StaticGenerationStore['fetchMetrics'];
    fetchTags?: string;
};
type RenderResultResponse = string | ReadableStream<Uint8Array> | null;
export default class RenderResult {
    /**
     * The detected content type for the response. This is used to set the
     * `Content-Type` header.
     */
    readonly contentType: ContentTypeOption;
    /**
     * The metadata for the response. This is used to set the revalidation times
     * and other metadata.
     */
    readonly metadata: RenderResultMetadata;
    /**
     * The response itself. This can be a string, a stream, or null. If it's a
     * string, then it's a static response. If it's a stream, then it's a
     * dynamic response. If it's null, then the response was not found or was
     * already sent.
     */
    private readonly response;
    /**
     * Creates a new RenderResult instance from a static response.
     *
     * @param value the static response value
     * @returns a new RenderResult instance
     */
    static fromStatic(value: string): RenderResult;
    constructor(response: RenderResultResponse, { contentType, ...metadata }?: {
        contentType?: ContentTypeOption;
    } & RenderResultMetadata);
    extendMetadata(metadata: RenderResultMetadata): void;
    /**
     * Returns true if the response is null. It can be null if the response was
     * not found or was already sent.
     */
    get isNull(): boolean;
    /**
     * Returns false if the response is a string. It can be a string if the page
     * was prerendered. If it's not, then it was generated dynamically.
     */
    get isDynamic(): boolean;
    /**
     * Returns true if the response is a stream. If the page was dynamic, this
     * will throw an error.
     *
     * @returns The response as a string
     */
    toUnchunkedString(): string;
    pipe(res: PipeTarget): Promise<void>;
}
export {};
