'use client';

import React, { useState, useRef, useEffect } from 'react';
import { 
  Code, 
  Download, 
  Upload, 
  Settings, 
  Eye, 
  Palette,
  FileDown,
  Zap,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Copy,
  Image as ImageIcon,
  Type,
  Layout,
  Maximize2,
  Minimize2,
  Globe,
  Monitor,
  Smartphone,
  Tablet,
  ExternalLink
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface HtmlSettings {
  outputFormat: 'clean' | 'standalone' | 'fragment';
  cssFramework: 'none' | 'bootstrap' | 'tailwind' | 'bulma';
  theme: 'default' | 'github' | 'material' | 'dark' | 'minimal';
  includeCSS: boolean;
  includeJS: boolean;
  minifyOutput: boolean;
  semanticHTML: boolean;
  accessibilityFeatures: boolean;
  responsiveDesign: boolean;
  syntaxHighlighting: boolean;
  mathRendering: boolean;
  customCSS: string;
}

type ThemeStyles = {
  [key in 'default' | 'github' | 'material' | 'dark' | 'minimal']: string;
};

const defaultMarkdown = `# Markdown to HTML Converter

Welcome to our **free online Markdown to HTML converter**! This powerful tool transforms your Markdown documents into clean, semantic HTML with advanced customization options and professional styling.

## Key Features

### ✅ Professional HTML Output
- Clean, semantic HTML5 markup
- W3C compliant code generation
- SEO-optimized structure
- Accessibility features built-in
- Cross-browser compatibility

### ✅ Advanced Customization Options
- **Multiple output formats**: Clean HTML, Standalone page, Fragment
- **CSS frameworks**: Bootstrap, Tailwind CSS, Bulma integration
- **Custom themes**: GitHub, Material Design, Dark mode, Minimal
- **Responsive design**: Mobile-first approach
- **Syntax highlighting**: Beautiful code block formatting

### ✅ Developer-Friendly Features
- Minified output for production
- Custom CSS injection
- JavaScript integration
- Mathematical expression rendering
- Image optimization and lazy loading

## How to Use This Converter

1. **Input your Markdown**: Paste or type your content in the editor panel
2. **Customize settings**: Choose output format, CSS framework, and styling options
3. **Preview HTML**: See how your HTML will render in real-time
4. **Generate & Download**: Create and download your optimized HTML file

## Supported Markdown Elements

### Text Formatting
Transform your text with various formatting options:
- **Bold text** for strong emphasis
- *Italic text* for subtle emphasis
- ~~Strikethrough text~~ for deletions
- \`Inline code\` for technical terms
- [Hyperlinks](https://example.com) for references

### Document Structure
#### Headings
Create well-structured documents with proper heading hierarchy:
- # Main Title (H1)
- ## Section Title (H2)
- ### Subsection Title (H3)
- #### Sub-subsection Title (H4)

#### Lists
Organize information with clean list structures:

**Unordered Lists:**
- Feature 1: Real-time HTML conversion
- Feature 2: Multiple CSS frameworks
  - Sub-feature: Bootstrap integration
  - Sub-feature: Tailwind CSS support
  - Sub-feature: Custom styling options
- Feature 3: Responsive design output

**Ordered Lists:**
1. Open the HTML converter
2. Paste your Markdown content
3. Select output format and styling
4. Customize CSS framework settings
5. Generate and download HTML

### Code Examples
Display code with beautiful syntax highlighting:

\`\`\`html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated from Markdown</title>
</head>
<body>
    <h1>Hello, World!</h1>
    <p>This HTML was generated from <strong>Markdown</strong>!</p>
</body>
</html>
\`\`\`

\`\`\`css
/* Custom CSS for enhanced styling */
.markdown-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.code-block {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    overflow-x: auto;
}
\`\`\`

\`\`\`javascript
// JavaScript for interactive features
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling to anchor links
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});
\`\`\`

### Tables
Create professional tables with responsive design:

| Feature | Free Version | Pro Version | Enterprise |
|---------|-------------|-------------|------------|
| Basic HTML Conversion | ✅ | ✅ | ✅ |
| CSS Framework Integration | ✅ | ✅ | ✅ |
| Custom Themes | ✅ | ✅ | ✅ |
| Responsive Design | ✅ | ✅ | ✅ |
| Syntax Highlighting | ✅ | ✅ | ✅ |
| Custom CSS Injection | ❌ | ✅ | ✅ |
| JavaScript Integration | ❌ | ✅ | ✅ |
| Batch Processing | ❌ | ❌ | ✅ |
| API Access | ❌ | ❌ | ✅ |

### Blockquotes
Add emphasis with styled blockquotes:

> "The web is more a social creation than a technical one. I designed it for a social effect — to help people work together — and not as a technical toy."
> 
> — Tim Berners-Lee, Inventor of the World Wide Web

### Mathematical Expressions
Include mathematical formulas with MathJax rendering:

Inline math: $E = mc^2$

Block math:
$$
\\sum_{i=1}^{n} x_i = \\frac{1}{n} \\sum_{i=1}^{n} x_i
$$

Complex equation:
$$
\\frac{\\partial f}{\\partial x} = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}
$$

---

## Output Format Options

### 1. Clean HTML Fragment
Perfect for embedding in existing websites:
- Pure HTML markup without \`<html>\`, \`<head>\`, or \`<body>\` tags
- Minimal CSS classes for easy styling
- Lightweight and fast loading

### 2. Standalone HTML Page
Complete HTML document ready for deployment:
- Full HTML5 document structure
- Embedded CSS and JavaScript
- Meta tags for SEO optimization
- Responsive viewport configuration

### 3. HTML with CSS Framework
Professional styling with popular frameworks:
- **Bootstrap**: Mobile-first responsive design
- **Tailwind CSS**: Utility-first CSS framework
- **Bulma**: Modern CSS framework based on Flexbox

## CSS Framework Integration

### Bootstrap Integration
\`\`\`html
<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Your Markdown content with Bootstrap classes -->
<div class="container">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <!-- Generated HTML content -->
        </div>
    </div>
</div>
\`\`\`

### Tailwind CSS Integration
\`\`\`html
<!-- Tailwind CSS -->
<script src="https://cdn.tailwindcss.com"></script>

<!-- Your Markdown content with Tailwind classes -->
<div class="max-w-4xl mx-auto px-4 py-8">
    <div class="prose prose-lg">
        <!-- Generated HTML content -->
    </div>
</div>
\`\`\`

---

## Why Choose Our Markdown to HTML Converter?

### 🚀 Fast and Reliable
- **Instant conversion**: Real-time HTML generation
- **Large document support**: Handles documents of any size
- **Optimized output**: Clean, efficient HTML code
- **Error handling**: Robust processing with helpful feedback

### 🔒 Privacy and Security
- **Local processing**: All conversion happens in your browser
- **No data transmission**: Your content never leaves your device
- **Complete privacy**: No registration or tracking
- **Secure environment**: Safe document handling

### 💰 Completely Free
- **No registration**: Start converting immediately
- **No limitations**: Unlimited conversions
- **Full feature access**: All options available for free
- **No watermarks**: Clean, professional output

### 🎨 Professional Results
- **W3C compliant**: Valid HTML5 markup
- **SEO optimized**: Proper semantic structure
- **Accessibility ready**: WCAG 2.1 compliant features
- **Cross-browser compatible**: Works everywhere

### 📱 Responsive Design
- **Mobile-first**: Optimized for all devices
- **Flexible layouts**: Adapts to any screen size
- **Touch-friendly**: Perfect for mobile interaction
- **Print optimized**: Beautiful printed output

---

**Ready to convert?** Start by editing this sample content or paste your own Markdown text above. Customize the HTML output settings to match your needs and click "Generate HTML" to create your optimized web content.

Transform your Markdown into beautiful, semantic HTML today! 🌐✨`;

export default function MarkdownToHtmlConverter() {
  const { t } = useTranslation('markdown-to-html');
  const [markdown, setMarkdown] = useState(defaultMarkdown);
  const [html, setHtml] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [conversionStatus, setConversionStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [settings, setSettings] = useState<HtmlSettings>({
    outputFormat: 'standalone',
    cssFramework: 'none',
    theme: 'default',
    includeCSS: true,
    includeJS: false,
    minifyOutput: false,
    semanticHTML: true,
    accessibilityFeatures: true,
    responsiveDesign: true,
    syntaxHighlighting: true,
    mathRendering: true,
    customCSS: ''
  });

  // Enhanced markdown to HTML converter
  const convertMarkdownToHtml = (markdown: string) => {
    if (!markdown) return '';
    
    let html = markdown
      // Headers with proper semantic structure
      .replace(/^#### (.*$)/gim, '<h4 id="$1">$1</h4>')
      .replace(/^### (.*$)/gim, '<h3 id="$1">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 id="$1">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 id="$1">$1</h1>')
      // Images with accessibility features
      .replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" class="responsive-image" loading="lazy" />')
      // Text formatting
      .replace(/\*\*\*(.*?)\*\*\*/gim, '<strong><em>$1</em></strong>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      .replace(/~~(.*?)~~/gim, '<del>$1</del>')
      // Links with accessibility
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" rel="noopener noreferrer">$1</a>')
      // Code blocks with syntax highlighting
      .replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre class="code-block"><code class="language-$1">$2</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/gim, '<code class="inline-code">$1</code>')
      // Tables with responsive design
      .replace(/^\|(.+)\|$/gim, (match, content) => {
        const cells = content.split('|').map((cell: string) => cell.trim()).filter((cell: string) => cell);
        return '<tr>' + cells.map((cell: string, index: number) => {
          const tag = index === 0 ? 'th' : 'td';
          return `<${tag} class="table-cell">${cell}</${tag}>`;
        }).join('') + '</tr>';
      })
      // Lists with proper structure
      .replace(/^\- (.*$)/gim, '<li>$1</li>')
      .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
      // Blockquotes with semantic markup
      .replace(/^> (.*$)/gim, '<blockquote class="blockquote">$1</blockquote>')
      // Horizontal rules
      .replace(/^---$/gim, '<hr class="divider">')
      // Math expressions
      .replace(/\$\$([\s\S]*?)\$\$/gim, '<div class="math-block">$1</div>')
      .replace(/\$([^$]+)\$/gim, '<span class="math-inline">$1</span>')
      // Line breaks
      .replace(/\n/gim, '<br>');

    // Wrap consecutive list items
    html = html.replace(/(<li>.*<\/li>)/s, '<ul class="list">$1</ul>');
    
    // Wrap table rows
    if (html.includes('<tr>')) {
      html = html.replace(/(<tr>.*<\/tr>)/s, '<table class="responsive-table">$1</table>');
    }
    
    return html;
  };

  // Get CSS styles based on theme and settings
  const getThemeStyles = (theme: HtmlSettings['theme']): string => {
    const styles: ThemeStyles = {
      default: '/* Default theme styles */',
      github: '/* GitHub theme styles */',
      material: '/* Material theme styles */',
      dark: '/* Dark theme styles */',
      minimal: '/* Minimal theme styles */'
    };
    return styles[theme];
  };

  // Get CSS framework links
  const getCSSFrameworkLinks = () => {
    switch (settings.cssFramework) {
      case 'bootstrap':
        return '<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">';
      case 'tailwind':
        return '<script src="https://cdn.tailwindcss.com"></script>';
      case 'bulma':
        return '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">';
      default:
        return '';
    }
  };

  // Generate complete HTML document
  const generateCompleteHTML = () => {
    const cssFrameworkLinks = getCSSFrameworkLinks();
    const customStyles = settings.includeCSS ? `<style>${getThemeStyles(settings.theme)}</style>` : '';
    const mathJax = settings.mathRendering ? `
      <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
      <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    ` : '';
    
    const syntaxHighlightingJS = settings.syntaxHighlighting ? `
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
      <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
      <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    ` : '';

    if (settings.outputFormat === 'fragment') {
      return html;
    }

    if (settings.outputFormat === 'clean') {
      return `<div class="markdown-content">${html}</div>`;
    }

    // Standalone HTML page
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Generated from Markdown">
    <title>Markdown Document</title>
    ${cssFrameworkLinks}
    ${customStyles}
    ${syntaxHighlightingJS}
    ${mathJax}
</head>
<body>
    <main class="markdown-content" role="main">
        ${html}
    </main>
    ${settings.includeJS ? `
    <script>
        // Smooth scrolling for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });
    </script>
    ` : ''}
</body>
</html>`;
  };

  // Convert markdown to HTML
  useEffect(() => {
    const htmlContent = convertMarkdownToHtml(markdown);
    setHtml(htmlContent);
  }, [markdown]);

  // Auto-save to localStorage
  useEffect(() => {
    const timer = setTimeout(() => {
      localStorage.setItem('html-markdown-content', markdown);
      localStorage.setItem('html-settings', JSON.stringify(settings));
    }, 1000);

    return () => clearTimeout(timer);
  }, [markdown, settings]);

  // Load saved content on mount
  useEffect(() => {
    const saved = localStorage.getItem('html-markdown-content');
    const savedSettings = localStorage.getItem('html-settings');
    
    if (saved) {
      setMarkdown(saved);
    }
    
    if (savedSettings) {
      try {
        setSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to parse saved settings:', error);
      }
    }
  }, []);

  // Generate HTML
  const generateHTML = async () => {
    setIsGenerating(true);
    setConversionStatus('idle');
    
    try {
      // Simulate HTML generation
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const completeHTML = generateCompleteHTML();
      
      // Minify if requested
      const finalHTML = settings.minifyOutput 
        ? completeHTML.replace(/\s+/g, ' ').replace(/>\s+</g, '><').trim()
        : completeHTML;
      
      // Create downloadable HTML file
      const blob = new Blob([finalHTML], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'document.html';
      a.click();
      URL.revokeObjectURL(url);
      
      setConversionStatus('success');
    } catch (error) {
      console.error('HTML generation failed:', error);
      setConversionStatus('error');
    } finally {
      setIsGenerating(false);
    }
  };

  // File upload handler
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && (file.type === 'text/markdown' || file.name.endsWith('.md'))) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setMarkdown(content);
      };
      reader.readAsText(file);
    }
  };

  // Copy to clipboard
  const copyToClipboard = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      console.log('Content copied to clipboard');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  // Get preview width based on mode
  const getPreviewWidth = () => {
    switch (previewMode) {
      case 'mobile': return '375px';
      case 'tablet': return '768px';
      default: return '100%';
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'min-h-screen'} bg-gray-50`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="bg-orange-100 p-2 rounded-lg">
                <Code className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{t('title')}</h1>
                <p className="text-sm text-gray-600">{t('subtitle')}</p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`p-2 rounded-lg transition-colors ${
                showPreview ? 'bg-orange-100 text-orange-600' : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={t('buttons.togglePreview')}
            >
              <Eye className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setShowSettings(!showSettings)}
              className={`p-2 rounded-lg transition-colors ${
                showSettings ? 'bg-orange-100 text-orange-600' : 'hover:bg-gray-100 text-gray-600'
              }`}
              title={t('buttons.settings')}
            >
              <Settings className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors text-gray-600"
              title={t('buttons.fullscreen')}
            >
              {isFullscreen ? <Minimize2 className="h-5 w-5" /> : <Maximize2 className="h-5 w-5" />}
            </button>
            
            <button
              onClick={generateHTML}
              disabled={isGenerating || !markdown.trim()}
              className="bg-orange-600 text-white px-6 py-2 rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {isGenerating ? (
                <>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>{t('buttons.generating')}</span>
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  <span>{t('buttons.generateHtml')}</span>
                </>
              )}
            </button>
          </div>
        </div>
        
        {/* Status Messages */}
        {conversionStatus === 'success' && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span className="text-green-800">{t('messages.success')}</span>
          </div>
        )}
        
        {conversionStatus === 'error' && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <span className="text-red-800">{t('messages.error')}</span>
          </div>
        )}
      </div>
      <div className="flex" style={{ height: isFullscreen ? 'calc(100vh - 89px)' : 'calc(100vh - 89px)' }}>
        {/* Settings Panel */}
        {showSettings && (
          <div className="w-80 bg-white border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('settings.title')}</h3>
              
              {/* Output Format */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Layout className="h-4 w-4 mr-2" />
                  {t('settings.outputFormat.title')}
                </h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">{t('settings.outputFormat.formatType')}</label>
                    <select
                      value={settings.outputFormat}
                      onChange={(e) => setSettings(prev => ({ ...prev, outputFormat: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="fragment">{t('settings.outputFormat.options.fragment')}</option>
                      <option value="clean">{t('settings.outputFormat.options.clean')}</option>
                      <option value="standalone">{t('settings.outputFormat.options.standalone')}</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">CSS Framework</label>
                    <select
                      value={settings.cssFramework}
                      onChange={(e) => setSettings(prev => ({ ...prev, cssFramework: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="none">None</option>
                      <option value="bootstrap">Bootstrap</option>
                      <option value="tailwind">Tailwind CSS</option>
                      <option value="bulma">Bulma</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-xs font-medium text-gray-600 mb-1">Theme</label>
                    <select
                      value={settings.theme}
                      onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value as any }))}
                      className="w-full p-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="default">Default</option>
                      <option value="github">GitHub</option>
                      <option value="material">Material Design</option>
                      <option value="dark">Dark Theme</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>
                </div>
              </div>
              
              {/* Features */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  Features
                </h4>
                
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.includeCSS}
                      onChange={(e) => setSettings(prev => ({ ...prev, includeCSS: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Include CSS Styles</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.includeJS}
                      onChange={(e) => setSettings(prev => ({ ...prev, includeJS: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Include JavaScript</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.syntaxHighlighting}
                      onChange={(e) => setSettings(prev => ({ ...prev, syntaxHighlighting: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Syntax Highlighting</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.mathRendering}
                      onChange={(e) => setSettings(prev => ({ ...prev, mathRendering: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Math Rendering</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.responsiveDesign}
                      onChange={(e) => setSettings(prev => ({ ...prev, responsiveDesign: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Responsive Design</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.accessibilityFeatures}
                      onChange={(e) => setSettings(prev => ({ ...prev, accessibilityFeatures: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Accessibility Features</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.semanticHTML}
                      onChange={(e) => setSettings(prev => ({ ...prev, semanticHTML: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Semantic HTML</span>
                  </label>
                  
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.minifyOutput}
                      onChange={(e) => setSettings(prev => ({ ...prev, minifyOutput: e.target.checked }))}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Minify Output</span>
                  </label>
                </div>
              </div>
              
              {/* Custom CSS */}
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Custom CSS</h4>
                <textarea
                  value={settings.customCSS}
                  onChange={(e) => setSettings(prev => ({ ...prev, customCSS: e.target.value }))}
                  placeholder="/* Add your custom CSS here */"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm font-mono"
                  rows={6}
                />
              </div>
            </div>
          </div>
        )}

        {/* Editor Panel */}
        <div className={`${showPreview ? (showSettings ? 'w-1/2' : 'w-1/2') : 'w-full'} flex flex-col`}>
          <div className="bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">{t('panels.editor')}</span>
            <div className="flex items-center space-x-2">
              <input
                ref={fileInputRef}
                type="file"
                accept=".md,.markdown"
                onChange={handleFileUpload}
                className="hidden"
              />
              
              <button
                onClick={() => fileInputRef.current?.click()}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title={t('buttons.upload')}
              >
                <Upload className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => copyToClipboard(markdown)}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title={t('buttons.copyMarkdown')}
              >
                <Copy className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => copyToClipboard(generateCompleteHTML())}
                className="p-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
                title={t('buttons.copyHtml')}
              >
                <Code className="h-4 w-4" />
              </button>
              
              <button
                onClick={() => setMarkdown('')}
                className="text-xs px-2 py-1 hover:bg-gray-200 rounded transition-colors text-gray-600"
              >
                {t('buttons.clear')}
              </button>
            </div>
          </div>
          
          <textarea
            ref={textareaRef}
            value={markdown}
            onChange={(e) => setMarkdown(e.target.value)}
            className="flex-1 p-4 font-mono text-sm resize-none focus:outline-none bg-white"
            placeholder={t('placeholders.editor')}
            spellCheck={false}
          />
        </div>

        {/* Preview Panel */}
        {showPreview && (
          <div className={`${showSettings ? 'w-1/2' : 'w-1/2'} flex flex-col border-l border-gray-200`}>
            <div className="bg-gray-100 border-b border-gray-200 px-4 py-2 flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">{t('panels.preview')}</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setPreviewMode('desktop')}
                  className={`p-1 rounded transition-colors ${
                    previewMode === 'desktop' ? 'bg-orange-100 text-orange-600' : 'hover:bg-gray-200 text-gray-600'
                  }`}
                  title="Desktop View"
                >
                  <Monitor className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setPreviewMode('tablet')}
                  className={`p-1 rounded transition-colors ${
                    previewMode === 'tablet' ? 'bg-orange-100 text-orange-600' : 'hover:bg-gray-200 text-gray-600'
                  }`}
                  title="Tablet View"
                >
                  <Tablet className="h-4 w-4" />
                </button>
                <button
                  onClick={() => setPreviewMode('mobile')}
                  className={`p-1 rounded transition-colors ${
                    previewMode === 'mobile' ? 'bg-orange-100 text-orange-600' : 'hover:bg-gray-200 text-gray-600'
                  }`}
                  title="Mobile View"
                >
                  <Smartphone className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <div className="flex-1 overflow-auto bg-gray-100 p-4">
              <div 
                className="mx-auto bg-white shadow-sm border border-gray-200 transition-all duration-300"
                style={{ 
                  width: getPreviewWidth(),
                  minHeight: '100%'
                }}
              >
                <div
                  ref={previewRef}
                  className="p-4"
                  style={{
                    fontFamily: settings.theme === 'minimal' ? 'Georgia, serif' : 
                               settings.theme === 'material' ? 'Roboto, sans-serif' : 
                               '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    background: settings.theme === 'dark' ? '#1a1a1a' : '#fff',
                    color: settings.theme === 'dark' ? '#e0e0e0' : '#333'
                  }}
                >
                  <style dangerouslySetInnerHTML={{ __html: getThemeStyles(settings.theme) }} />
                  <div dangerouslySetInnerHTML={{ __html: html }} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}