{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/resolve-handler-error.ts"], "names": ["resolveHandlerError", "err", "isRedirectError", "redirect", "getURLFromRedirectError", "Error", "handleTemporaryRedirectResponse", "mutableCookies", "isNotFoundError", "handleNotFoundResponse"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;0BAVgB;0BAIzB;kCAIA;AAEA,SAASA,oBAAoBC,GAAQ;IAC1C,IAAIC,IAAAA,yBAAe,EAACD,MAAM;QACxB,MAAME,WAAWC,IAAAA,iCAAuB,EAACH;QACzC,IAAI,CAACE,UAAU;YACb,MAAM,IAAIE,MAAM;QAClB;QAEA,wDAAwD;QACxD,OAAOC,IAAAA,iDAA+B,EAACH,UAAUF,IAAIM,cAAc;IACrE;IAEA,IAAIC,IAAAA,yBAAe,EAACP,MAAM;QACxB,0DAA0D;QAC1D,OAAOQ,IAAAA,wCAAsB;IAC/B;IAEA,6DAA6D;IAC7D,OAAO;AACT"}