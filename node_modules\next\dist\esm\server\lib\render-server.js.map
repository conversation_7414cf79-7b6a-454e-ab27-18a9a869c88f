{"version": 3, "sources": ["../../../src/server/lib/render-server.ts"], "names": ["next", "result", "apps", "sandboxContext", "requireCacheHotReloader", "process", "env", "NODE_ENV", "require", "clearModuleContext", "target", "deleteAppClientCache", "deleteCache", "filePaths", "filePath", "propagateServerField", "dir", "field", "value", "app", "Error", "appField", "server", "apply", "Array", "isArray", "initialize", "opts", "type", "__NEXT_PRIVATE_RENDER_WORKER", "title", "requestHandler", "upgradeHandler", "_routerWorker", "workerType", "_renderWorker", "hostname", "customServer", "httpServer", "port", "isNodeDebugging", "getRequestHandler", "getUpgradeHandler", "prepare", "serverFields"], "mappings": "AAEA,OAAOA,UAAU,UAAS;AAG1B,MAAMC,SAWF,CAAC;AAEL,IAAIC,OAA4D,CAAC;AAEjE,IAAIC;AACJ,IAAIC;AAIJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,iBAAiBK,QAAQ;IACzBJ,0BAA0BI,QAAQ;AACpC;AAEA,OAAO,SAASC,mBAAmBC,MAAc;IAC/C,OAAOP,kCAAAA,eAAgBM,kBAAkB,CAACC;AAC5C;AAEA,OAAO,SAASC;IACd,OAAOP,2CAAAA,wBAAyBO,oBAAoB;AACtD;AAEA,OAAO,SAASC,YAAYC,SAAmB;IAC7C,KAAK,MAAMC,YAAYD,UAAW;QAChCT,2CAAAA,wBAAyBQ,WAAW,CAACE;IACvC;AACF;AAEA,OAAO,eAAeC,qBACpBC,GAAW,EACXC,KAA8B,EAC9BC,KAAU;IAEV,MAAMC,MAAMjB,IAAI,CAACc,IAAI;IACrB,IAAI,CAACG,KAAK;QACR,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,WAAW,AAACF,IAAYG,MAAM;IAElC,IAAID,UAAU;QACZ,IAAI,OAAOA,QAAQ,CAACJ,MAAM,KAAK,YAAY;YACzC,MAAMI,QAAQ,CAACJ,MAAM,CAACM,KAAK,CACzB,AAACJ,IAAYG,MAAM,EACnBE,MAAMC,OAAO,CAACP,SAASA,QAAQ,EAAE;QAErC,OAAO;YACLG,QAAQ,CAACJ,MAAM,GAAGC;QACpB;IACF;AACF;AAEA,OAAO,eAAeQ,WAAWC,IAchC;IACC,8DAA8D;IAC9D,4BAA4B;IAC5B,IAAI1B,MAAM,CAAC0B,KAAKX,GAAG,CAAC,EAAE;QACpB,OAAOf,MAAM,CAAC0B,KAAKX,GAAG,CAAC;IACzB;IAEA,MAAMY,OAAOvB,QAAQC,GAAG,CAACuB,4BAA4B;IACrD,IAAID,MAAM;QACRvB,QAAQyB,KAAK,GAAG,wBAAwBF;IAC1C;IAEA,IAAIG;IACJ,IAAIC;IAEJ,MAAMb,MAAMnB,KAAK;QACf,GAAG2B,IAAI;QACPM,eAAeN,KAAKO,UAAU,KAAK;QACnCC,eAAeR,KAAKO,UAAU,KAAK;QACnCE,UAAUT,KAAKS,QAAQ,IAAI;QAC3BC,cAAc;QACdC,YAAYX,KAAKL,MAAM;QACvBiB,MAAMZ,KAAKY,IAAI;QACfC,iBAAiBb,KAAKa,eAAe;IACvC;IACAtC,IAAI,CAACyB,KAAKX,GAAG,CAAC,GAAGG;IACjBY,iBAAiBZ,IAAIsB,iBAAiB;IACtCT,iBAAiBb,IAAIuB,iBAAiB;IAEtC,MAAMvB,IAAIwB,OAAO,CAAChB,KAAKiB,YAAY;IAEnC3C,MAAM,CAAC0B,KAAKX,GAAG,CAAC,GAAG;QACjBe;QACAC;IACF;IACA,OAAO/B,MAAM,CAAC0B,KAAKX,GAAG,CAAC;AACzB"}