{"version": 3, "sources": ["../../../src/telemetry/events/swc-plugins.ts"], "names": ["eventSwcPlugins", "EVENT_SWC_PLUGIN_PRESENT", "dir", "config", "packageJsonPath", "findUp", "cwd", "dependencies", "devDependencies", "require", "deps", "swcPluginPackages", "experimental", "swcPlugins", "map", "name", "_", "Promise", "all", "plugin", "version", "undefined", "pluginName", "fileExists", "path", "basename", "eventName", "payload", "pluginVersion"], "mappings": ";;;;+BAcsBA;;;eAAAA;;;+DAdH;6DACF;4BACU;;;;;;AAG3B,MAAMC,2BAA2B;AAS1B,eAAeD,gBACpBE,GAAW,EACXC,MAAkB;IAElB,IAAI;YAUAA,iCAAAA;QATF,MAAMC,kBAAkB,MAAMC,IAAAA,eAAM,EAAC,gBAAgB;YAAEC,KAAKJ;QAAI;QAChE,IAAI,CAACE,iBAAiB;YACpB,OAAO,EAAE;QACX;QAEA,MAAM,EAAEG,eAAe,CAAC,CAAC,EAAEC,kBAAkB,CAAC,CAAC,EAAE,GAAGC,QAAQL;QAE5D,MAAMM,OAAO;YAAE,GAAGF,eAAe;YAAE,GAAGD,YAAY;QAAC;QACnD,MAAMI,oBACJR,EAAAA,uBAAAA,OAAOS,YAAY,sBAAnBT,kCAAAA,qBAAqBU,UAAU,qBAA/BV,gCAAiCW,GAAG,CAAC,CAAC,CAACC,MAAMC,EAAE,GAAKD,UAAS,EAAE;QAEjE,OAAOE,QAAQC,GAAG,CAChBP,kBAAkBG,GAAG,CAAC,OAAOK;YAC3B,0EAA0E;YAC1E,MAAMC,UAAUV,IAAI,CAACS,OAAO,IAAIE;YAChC,IAAIC,aAAaH;YACjB,IAAI,MAAMI,IAAAA,sBAAU,EAACD,aAAa;gBAChCA,aAAaE,aAAI,CAACC,QAAQ,CAACN,QAAQ;YACrC;YAEA,OAAO;gBACLO,WAAWzB;gBACX0B,SAAS;oBACPL,YAAYA;oBACZM,eAAeR;gBACjB;YACF;QACF;IAEJ,EAAE,OAAOJ,GAAG;QACV,OAAO,EAAE;IACX;AACF"}