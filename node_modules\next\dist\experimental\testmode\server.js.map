{"version": 3, "sources": ["../../../src/experimental/testmode/server.ts"], "names": ["interceptTestApis", "wrapRequestHandlerWorker", "wrapRequestHandlerNode", "testStorage", "AsyncLocalStorage", "getTestStack", "stack", "Error", "split", "i", "length", "slice", "filter", "f", "includes", "map", "s", "replace", "trim", "join", "buildProxyRequest", "testData", "request", "url", "method", "headers", "body", "cache", "credentials", "integrity", "mode", "redirect", "referrer", "referrerPolicy", "api", "Array", "from", "<PERSON><PERSON><PERSON>", "arrayBuffer", "toString", "buildResponse", "proxyResponse", "status", "response", "Response", "Headers", "handleFetch", "originalFetch", "testInfo", "getStore", "proxyPort", "proxyRequest", "resp", "JSON", "stringify", "ok", "json", "interceptFetch", "global", "fetch", "testFetch", "input", "init", "next", "internal", "Request", "clientRequestInterceptor", "ClientRequestInterceptor", "on", "respondWith", "apply", "dispose", "handler", "req", "res", "proxyPortHeader", "Number", "testReqInfo", "run", "parsedUrl"], "mappings": ";;;;;;;;;;;;;;;;IAyIgBA,iBAAiB;eAAjBA;;IAkBAC,wBAAwB;eAAxBA;;IAsBAC,sBAAsB;eAAtBA;;;6BAjLkB;+BAMO;AAUzC,MAAMC,cAAc,IAAIC,8BAAiB;AAMzC,SAASC;IACP,IAAIC,QAAQ,AAAC,CAAA,IAAIC,QAAQD,KAAK,IAAI,EAAC,EAAGE,KAAK,CAAC;IAC5C,qDAAqD;IACrD,IAAK,IAAIC,IAAI,GAAGA,IAAIH,MAAMI,MAAM,EAAED,IAAK;QACrC,IAAIH,KAAK,CAACG,EAAE,CAACC,MAAM,GAAG,GAAG;YACvBJ,QAAQA,MAAMK,KAAK,CAACF;YACpB;QACF;IACF;IACA,+BAA+B;IAC/BH,QAAQA,MAAMM,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,QAAQ,CAAC;IACxC,mBAAmB;IACnBR,QAAQA,MAAMK,KAAK,CAAC,GAAG;IACvB,uCAAuC;IACvCL,QAAQA,MAAMS,GAAG,CAAC,CAACC,IAAMA,EAAEC,OAAO,CAAC,8BAA8B,IAAIC,IAAI;IACzE,OAAOZ,MAAMa,IAAI,CAAC;AACpB;AAEA,eAAeC,kBACbC,QAAgB,EAChBC,OAAgB;IAEhB,MAAM,EACJC,GAAG,EACHC,MAAM,EACNC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACf,GAAGX;IACJ,OAAO;QACLD;QACAa,KAAK;QACLZ,SAAS;YACPC;YACAC;YACAC,SAAS;mBAAIU,MAAMC,IAAI,CAACX;gBAAU;oBAAC;oBAAmBpB;iBAAe;aAAC;YACtEqB,MAAMA,OACFW,OAAOD,IAAI,CAAC,MAAMd,QAAQgB,WAAW,IAAIC,QAAQ,CAAC,YAClD;YACJZ;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;QACF;IACF;AACF;AAEA,SAASO,cAAcC,aAAiC;IACtD,MAAM,EAAEC,MAAM,EAAEjB,OAAO,EAAEC,IAAI,EAAE,GAAGe,cAAcE,QAAQ;IACxD,OAAO,IAAIC,SAASlB,OAAOW,OAAOD,IAAI,CAACV,MAAM,YAAY,MAAM;QAC7DgB;QACAjB,SAAS,IAAIoB,QAAQpB;IACvB;AACF;AAEA,eAAeqB,YACbC,aAAoB,EACpBzB,OAAgB;IAEhB,MAAM0B,WAAW7C,YAAY8C,QAAQ;IACrC,IAAI,CAACD,UAAU;QACb,MAAM,IAAIzC,MAAM;IAClB;IAEA,MAAM,EAAEc,QAAQ,EAAE6B,SAAS,EAAE,GAAGF;IAChC,MAAMG,eAAe,MAAM/B,kBAAkBC,UAAUC;IAEvD,MAAM8B,OAAO,MAAML,cAAc,CAAC,iBAAiB,EAAEG,UAAU,CAAC,EAAE;QAChE1B,QAAQ;QACRE,MAAM2B,KAAKC,SAAS,CAACH;IACvB;IACA,IAAI,CAACC,KAAKG,EAAE,EAAE;QACZ,MAAM,IAAIhD,MAAM,CAAC,sBAAsB,EAAE6C,KAAKV,MAAM,CAAC,CAAC;IACxD;IAEA,MAAMD,gBAAiB,MAAMW,KAAKI,IAAI;IACtC,MAAM,EAAEtB,GAAG,EAAE,GAAGO;IAChB,OAAQP;QACN,KAAK;YACH,OAAOa,cAAczB;QACvB,KAAK;QACL,KAAK;YACH,MAAM,IAAIf,MACR,CAAC,uBAAuB,EAAEe,QAAQE,MAAM,CAAC,CAAC,EAAEF,QAAQC,GAAG,CAAC,CAAC,CAAC;QAE9D;YACE;IACJ;IACA,OAAOiB,cAAcC;AACvB;AAEA,SAASgB;IACP,MAAMV,gBAAgBW,OAAOC,KAAK;IAClCD,OAAOC,KAAK,GAAG,SAASC,UACtBC,KAAoB,EACpBC,IAAmB;YAIfA;QAFJ,iCAAiC;QACjC,aAAa;QACb,IAAIA,yBAAAA,aAAAA,KAAMC,IAAI,qBAAVD,WAAYE,QAAQ,EAAE;YACxB,OAAOjB,cAAcc,OAAOC;QAC9B;QACA,OAAOhB,YAAYC,eAAe,IAAIkB,QAAQJ,OAAOC;IACvD;AACF;AAEO,SAAS9D;IACd,MAAM+C,gBAAgBW,OAAOC,KAAK;IAClCF;IAEA,MAAMS,2BAA2B,IAAIC,uCAAwB;IAC7DD,yBAAyBE,EAAE,CAAC,WAAW,OAAO,EAAE9C,OAAO,EAAE;QACvD,MAAMqB,WAAW,MAAMG,YAAYC,eAAezB;QAClDA,QAAQ+C,WAAW,CAAC1B;IACtB;IACAuB,yBAAyBI,KAAK;IAE9B,WAAW;IACX,OAAO;QACLJ,yBAAyBK,OAAO;QAChCb,OAAOC,KAAK,GAAGZ;IACjB;AACF;AAEO,SAAS9C,yBACduE,OAA6B;IAE7B,OAAO,OAAOC,KAAKC;QACjB,MAAMC,kBAAkBF,IAAIhD,OAAO,CAAC,uBAAuB;QAC3D,IAAI,CAACkD,iBAAiB;YACpB,MAAMH,QAAQC,KAAKC;YACnB;QACF;QAEA,MAAMnD,MAAMkD,IAAIlD,GAAG,IAAI;QACvB,MAAM2B,YAAY0B,OAAOD;QACzB,MAAMtD,WAAW,AAACoD,IAAIhD,OAAO,CAAC,iBAAiB,IAA2B;QAC1E,MAAMoD,cAA2B;YAC/BtD;YACA2B;YACA7B;QACF;QACA,MAAMlB,YAAY2E,GAAG,CAACD,aAAa,IAAML,QAAQC,KAAKC;IACxD;AACF;AAEO,SAASxE,uBACdsE,OAA2B;IAE3B,OAAO,OAAOC,KAAKC,KAAKK;QACtB,MAAMJ,kBAAkBF,IAAIhD,OAAO,CAAC,uBAAuB;QAC3D,IAAI,CAACkD,iBAAiB;YACpB,MAAMH,QAAQC,KAAKC,KAAKK;YACxB;QACF;QAEA,MAAMxD,MAAMkD,IAAIlD,GAAG,IAAI;QACvB,MAAM2B,YAAY0B,OAAOD;QACzB,MAAMtD,WAAW,AAACoD,IAAIhD,OAAO,CAAC,iBAAiB,IAA2B;QAC1E,MAAMoD,cAA2B;YAC/BtD;YACA2B;YACA7B;QACF;QACA,MAAMlB,YAAY2E,GAAG,CAACD,aAAa,IAAML,QAAQC,KAAKC,KAAKK;IAC7D;AACF"}