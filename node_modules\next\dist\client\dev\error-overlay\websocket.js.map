{"version": 3, "sources": ["../../../../src/client/dev/error-overlay/websocket.ts"], "names": ["addMessageListener", "sendMessage", "connectHMR", "source", "eventCallbacks", "getSocketProtocol", "assetPrefix", "protocol", "location", "URL", "callback", "push", "data", "readyState", "OPEN", "send", "options", "init", "close", "handleOnline", "window", "console", "log", "handleMessage", "event", "msg", "JSON", "parse", "eventCallback", "handleDisconnect", "onerror", "onclose", "hostname", "port", "replace", "url", "startsWith", "split", "WebSocket", "path", "onopen", "onmessage"], "mappings": ";;;;;;;;;;;;;;;;IAmBgBA,kBAAkB;eAAlBA;;IAIAC,WAAW;eAAXA;;IAKAC,UAAU;eAAVA;;;AA1BhB,IAAIC;AAIJ,MAAMC,iBAAwC,EAAE;AAEhD,SAASC,kBAAkBC,WAAmB;IAC5C,IAAIC,WAAWC,SAASD,QAAQ;IAEhC,IAAI;QACF,uBAAuB;QACvBA,WAAW,IAAIE,IAAIH,aAAaC,QAAQ;IAC1C,EAAE,UAAM,CAAC;IAET,OAAOA,aAAa,UAAU,OAAO;AACvC;AAEO,SAASP,mBAAmBU,QAAwB;IACzDN,eAAeO,IAAI,CAACD;AACtB;AAEO,SAAST,YAAYW,IAAY;IACtC,IAAI,CAACT,UAAUA,OAAOU,UAAU,KAAKV,OAAOW,IAAI,EAAE;IAClD,OAAOX,OAAOY,IAAI,CAACH;AACrB;AAEO,SAASV,WAAWc,OAA8C;IACvE,SAASC;QACP,IAAId,QAAQA,OAAOe,KAAK;QAExB,SAASC;YACPC,OAAOC,OAAO,CAACC,GAAG,CAAC;QACrB;QAEA,SAASC,cAAcC,KAA2B;YAChD,sDAAsD;YACtD,MAAMC,MAAwBC,KAAKC,KAAK,CAACH,MAAMZ,IAAI;YACnD,KAAK,MAAMgB,iBAAiBxB,eAAgB;gBAC1CwB,cAAcH;YAChB;QACF;QAEA,SAASI;YACP1B,OAAO2B,OAAO,GAAG;YACjB3B,OAAO4B,OAAO,GAAG;YACjB5B,OAAOe,KAAK;YACZD;QACF;QAEA,MAAM,EAAEe,QAAQ,EAAEC,IAAI,EAAE,GAAGzB;QAC3B,MAAMD,WAAWF,kBAAkBW,QAAQV,WAAW,IAAI;QAC1D,MAAMA,cAAcU,QAAQV,WAAW,CAAC4B,OAAO,CAAC,QAAQ;QAExD,IAAIC,MAAM,AAAG5B,WAAS,QAAKyB,WAAS,MAAGC,OACrC3B,CAAAA,cAAc,AAAC,MAAGA,cAAgB,EAAC;QAGrC,IAAIA,YAAY8B,UAAU,CAAC,SAAS;YAClCD,MAAM,AAAG5B,WAAS,QAAKD,YAAY+B,KAAK,CAAC,MAAM,CAAC,EAAE;QACpD;QAEAlC,SAAS,IAAIiB,OAAOkB,SAAS,CAAC,AAAC,KAAEH,MAAMnB,QAAQuB,IAAI;QACnDpC,OAAOqC,MAAM,GAAGrB;QAChBhB,OAAO2B,OAAO,GAAGD;QACjB1B,OAAO4B,OAAO,GAAGF;QACjB1B,OAAOsC,SAAS,GAAGlB;IACrB;IAEAN;AACF"}