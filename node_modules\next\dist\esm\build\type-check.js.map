{"version": 3, "sources": ["../../src/build/type-check.ts"], "names": ["path", "Log", "Worker", "JestWorker", "verifyAndLint", "createSpinner", "eventTypeCheckCompleted", "isError", "verifyTypeScriptSetup", "dir", "distDir", "intentDirs", "typeCheckPreflight", "tsconfigPath", "disableStaticImages", "cacheDir", "enableWorkerThreads", "hasAppDir", "hasPagesDir", "typeCheckWorker", "require", "resolve", "numWorkers", "maxRetries", "getStdout", "pipe", "process", "stdout", "getStderr", "stderr", "then", "result", "end", "startTypeChecking", "config", "ignoreESLint", "nextBuildSpan", "pagesDir", "runLint", "shouldLint", "telemetry", "appDir", "ignoreTypeScriptErrors", "Boolean", "typescript", "ignoreBuildErrors", "eslintCacheDir", "join", "info", "typeCheckingAndLintingSpinnerPrefixText", "typeCheckingAndLintingSpinner", "typeCheckStart", "hrtime", "verifyResult", "typeCheckEnd", "Promise", "all", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "filter", "images", "experimental", "workerThreads", "resolved", "checkEnd", "eslint", "dirs", "stopAndPersist", "record", "durationInSeconds", "typescriptVersion", "version", "inputFilesCount", "totalFilesCount", "incremental", "err", "message", "flush", "exit"], "mappings": "AAIA,OAAOA,UAAU,OAAM;AACvB,YAAYC,SAAS,eAAc;AACnC,SAASC,UAAUC,UAAU,QAAQ,iCAAgC;AACrE,SAASC,aAAa,QAAQ,uBAAsB;AACpD,OAAOC,mBAAmB,YAAW;AACrC,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,OAAOC,aAAa,kBAAiB;AAErC;;;;;;;CAOC,GACD,SAASC,sBACPC,GAAW,EACXC,OAAe,EACfC,UAAoB,EACpBC,kBAA2B,EAC3BC,YAAoB,EACpBC,mBAA4B,EAC5BC,QAA4B,EAC5BC,mBAAwC,EACxCC,SAAkB,EAClBC,WAAoB;IAEpB,MAAMC,kBAAkB,IAAIhB,WAC1BiB,QAAQC,OAAO,CAAC,iCAChB;QACEC,YAAY;QACZN;QACAO,YAAY;IACd;IAKFJ,gBAAgBK,SAAS,GAAGC,IAAI,CAACC,QAAQC,MAAM;IAC/CR,gBAAgBS,SAAS,GAAGH,IAAI,CAACC,QAAQG,MAAM;IAE/C,OAAOV,gBACJX,qBAAqB,CAAC;QACrBC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAE;QACAC;IACF,GACCY,IAAI,CAAC,CAACC;QACLZ,gBAAgBa,GAAG;QACnB,OAAOD;IACT;AACJ;AAEA,OAAO,eAAeE,kBAAkB,EACtClB,QAAQ,EACRmB,MAAM,EACNzB,GAAG,EACH0B,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,OAAO,EACPC,UAAU,EACVC,SAAS,EACTC,MAAM,EAYP;IACC,MAAMC,yBAAyBC,QAAQT,OAAOU,UAAU,CAACC,iBAAiB;IAE1E,MAAMC,iBAAiB9C,KAAK+C,IAAI,CAAChC,UAAU;IAE3C,IAAI2B,wBAAwB;QAC1BzC,IAAI+C,IAAI,CAAC;IACX;IACA,IAAIV,WAAWH,cAAc;QAC3B,uEAAuE;QACvElC,IAAI+C,IAAI,CAAC;IACX;IAEA,IAAIC;IACJ,IAAIC;IAIJ,IAAI,CAACR,0BAA0BH,YAAY;QACzCU,0CACE;IACJ,OAAO,IAAI,CAACP,wBAAwB;QAClCO,0CAA0C;IAC5C,OAAO,IAAIV,YAAY;QACrBU,0CAA0C;IAC5C;IAEA,mFAAmF;IACnF,4EAA4E;IAC5E,IAAIA,yCAAyC;QAC3CC,gCAAgC7C,cAC9B4C;IAEJ;IAEA,MAAME,iBAAiBzB,QAAQ0B,MAAM;IAErC,IAAI;QACF,MAAM,CAAC,CAACC,cAAcC,aAAa,CAAC,GAAG,MAAMC,QAAQC,GAAG,CAAC;YACvDpB,cAAcqB,UAAU,CAAC,2BAA2BC,YAAY,CAAC,IAC/DlD,sBACEC,KACAyB,OAAOxB,OAAO,EACd;oBAAC2B;oBAAUI;iBAAO,CAACkB,MAAM,CAAChB,UAC1B,CAACD,wBACDR,OAAOU,UAAU,CAAC/B,YAAY,EAC9BqB,OAAO0B,MAAM,CAAC9C,mBAAmB,EACjCC,UACAmB,OAAO2B,YAAY,CAACC,aAAa,EACjC,CAAC,CAACrB,QACF,CAAC,CAACJ,UACFP,IAAI,CAAC,CAACiC;oBACN,MAAMC,WAAWtC,QAAQ0B,MAAM,CAACD;oBAChC,OAAO;wBAACY;wBAAUC;qBAAS;gBAC7B;YAEFzB,cACEH,cAAcqB,UAAU,CAAC,mBAAmBC,YAAY,CAAC;oBAIrDxB;gBAHF,MAAM9B,cACJK,KACAqC,iBACAZ,iBAAAA,OAAO+B,MAAM,qBAAb/B,eAAegC,IAAI,EACnBhC,OAAO2B,YAAY,CAACC,aAAa,EACjCtB;YAEJ;SACH;QACDU,iDAAAA,8BAA+BiB,cAAc;QAE7C,IAAI,CAACzB,0BAA0BW,cAAc;gBAKtBA,sBACAA,uBACJA;YANjBb,UAAU4B,MAAM,CACd9D,wBAAwB;gBACtB+D,mBAAmBf,YAAY,CAAC,EAAE;gBAClCgB,mBAAmBjB,aAAakB,OAAO;gBACvCC,eAAe,GAAEnB,uBAAAA,aAAatB,MAAM,qBAAnBsB,qBAAqBmB,eAAe;gBACrDC,eAAe,GAAEpB,wBAAAA,aAAatB,MAAM,qBAAnBsB,sBAAqBoB,eAAe;gBACrDC,WAAW,GAAErB,wBAAAA,aAAatB,MAAM,qBAAnBsB,sBAAqBqB,WAAW;YAC/C;QAEJ;IACF,EAAE,OAAOC,KAAK;QACZ,mDAAmD;QACnD,8CAA8C;QAC9C,IAAIpE,QAAQoE,QAAQA,IAAIC,OAAO,KAAK,8BAA8B;YAChE,MAAMpC,UAAUqC,KAAK;YACrBnD,QAAQoD,IAAI,CAAC;QACf;QACA,MAAMH;IACR;AACF"}