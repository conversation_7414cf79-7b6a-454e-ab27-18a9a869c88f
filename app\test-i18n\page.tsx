'use client';

import { useTranslation, useLocale } from '@/hooks/useTranslation';

function NavbarTest() {
  const { t } = useTranslation('navbar');
  return (
    <ul className="space-y-1">
      <li><strong>Brand:</strong> {t('brand')}</li>
      <li><strong>Home:</strong> {t('navigation.home')}</li>
      <li><strong>Demo:</strong> {t('navigation.demo')}</li>
      <li><strong>Editor:</strong> {t('navigation.editor')}</li>
      <li><strong>FAQ:</strong> {t('navigation.faq')}</li>
    </ul>
  );
}

function HeroTest() {
  const { t } = useTranslation('hero');
  return (
    <ul className="space-y-1">
      <li><strong>Title Prefix:</strong> {t('title.prefix')}</li>
      <li><strong>Title Highlight:</strong> {t('title.highlight')}</li>
      <li><strong>Subtitle:</strong> {t('subtitle').substring(0, 50)}...</li>
      <li><strong>Start Editing:</strong> {t('buttons.startEditing')}</li>
      <li><strong>Watch Demo:</strong> {t('buttons.watchDemo')}</li>
    </ul>
  );
}

export default function TestI18nPage() {
  const { t } = useTranslation('common');
  const { locale, changeLanguage } = useLocale();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">i18n Test Page</h1>
      
      <div className="mb-6">
        <p><strong>Current Locale:</strong> {locale}</p>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Language Switcher</h2>
        <div className="space-x-4">
          <button
            onClick={() => changeLanguage('en')}
            className={`px-4 py-2 rounded ${locale === 'en' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            English
          </button>
          <button
            onClick={() => changeLanguage('ja')}
            className={`px-4 py-2 rounded ${locale === 'ja' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}
          >
            日本語
          </button>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Common Translations</h2>
        <ul className="space-y-2">
          <li><strong>Save:</strong> {t('buttons.save')}</li>
          <li><strong>Cancel:</strong> {t('buttons.cancel')}</li>
          <li><strong>Submit:</strong> {t('buttons.submit')}</li>
          <li><strong>Loading:</strong> {t('messages.loading')}</li>
          <li><strong>Success:</strong> {t('messages.success')}</li>
          <li><strong>Error:</strong> {t('messages.error')}</li>
        </ul>
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Navbar Translations</h2>
        <NavbarTest />
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Hero Translations</h2>
        <HeroTest />
      </div>

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Navigation Translations</h2>
        <ul className="space-y-2">
          <li><strong>Home:</strong> {t('navigation.home')}</li>
          <li><strong>Dashboard:</strong> {t('navigation.dashboard')}</li>
          <li><strong>Settings:</strong> {t('navigation.settings')}</li>
          <li><strong>Help:</strong> {t('navigation.help')}</li>
        </ul>
      </div>
    </div>
  );
}
